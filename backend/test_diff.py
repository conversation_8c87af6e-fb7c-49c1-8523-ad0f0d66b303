#!/usr/bin/env python3
"""
Test the diff generator to ensure it produces proper Git diff format.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.diff_generator import generate_comment_out_diff

def test_diff_generator():
    """Test the diff generator with a sample file."""
    
    # Test with main.py
    file_path = "/app/src/main.py"
    pattern = "logger.info"
    
    print("🧪 Testing Diff Generator...")
    print(f"File: {file_path}")
    print(f"Pattern: {pattern}")
    print("=" * 50)
    
    try:
        diff_result = generate_comment_out_diff(file_path, pattern)
        
        print("Generated Diff:")
        print(diff_result)
        
        # Check if it's proper Git diff format
        lines = diff_result.split('\n')
        
        checks = {
            "Has diff --git header": any(line.startswith("diff --git") for line in lines),
            "Has index line": any(line.startswith("index") for line in lines),
            "Has --- header": any(line.startswith("---") for line in lines),
            "Has +++ header": any(line.startswith("+++") for line in lines),
            "Has @@ hunks": any(line.startswith("@@") for line in lines),
            "Has - lines": any(line.startswith("-") and not line.startswith("---") for line in lines),
            "Has + lines": any(line.startswith("+") and not line.startswith("+++") for line in lines)
        }
        
        print("\n" + "=" * 50)
        print("Git Diff Format Validation:")
        for check, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"{status} {check}")
        
        all_passed = all(checks.values())
        print(f"\n{'🎉 All checks passed!' if all_passed else '❌ Some checks failed!'}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_diff_generator()
    sys.exit(0 if success else 1)
