#!/usr/bin/env python3
"""
Script to fix existing assistants by adding workflow IDs.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qdrant.operations.assistants import get_assistant_manager
from qdrant.operations.workflows import get_workflow_manager


async def create_default_workflow():
    """Create the default workflow if it doesn't exist."""
    print("🔧 Creating Default Workflow...")
    
    try:
        workflow_manager = get_workflow_manager()
        
        # Check if default workflow already exists
        existing = workflow_manager.get_workflow("default-assistant-workflow")
        if existing:
            print("✅ Default workflow already exists")
            return "default-assistant-workflow"
        
        # Create the default workflow
        default_workflow = {
            "id": "default-assistant-workflow",
            "name": "Default Assistant Workflow",
            "description": "Default workflow for assistants using the LangChain Code Agent",
            "blocks": [
                {
                    "id": "langchain-block",
                    "type": "service",
                    "name": "LangChain Code Agent",
                    "serviceId": "langchain_code_agent",
                    "x": 200,
                    "y": 150,
                    "config": {
                        "history_limit": 15,
                        "enable_code_execution": True,
                        "enable_file_operations": True,
                        "enable_git_operations": True
                    }
                }
            ],
            "connections": []
        }
        
        # Save the workflow
        result = await workflow_manager.save_workflow(default_workflow)
        
        if result.get('success'):
            workflow_id = result['workflow_id']
            print(f"✅ Default workflow created: {workflow_id}")
            return workflow_id
        else:
            print("❌ Failed to create default workflow")
            return None
            
    except Exception as e:
        print(f"❌ Error creating default workflow: {e}")
        return None


async def fix_existing_assistants():
    """Fix existing assistants by adding workflow IDs."""
    print("🤖 Fixing Existing Assistants...")
    
    try:
        assistant_manager = get_assistant_manager()
        
        # Get all assistants
        assistants = assistant_manager.get_assistants()
        print(f"📋 Found {len(assistants)} assistants to check")
        
        fixed_count = 0
        for assistant in assistants:
            assistant_id = assistant.get('id')
            current_workflow = assistant.get('workflowId')
            
            if not current_workflow:
                print(f"🔧 Fixing assistant: {assistant_id}")
                
                # Add default workflow ID
                assistant['workflowId'] = 'default-assistant-workflow'
                
                # Update the assistant
                success = await assistant_manager.store_assistant(assistant)
                if success:
                    print(f"✅ Fixed assistant: {assistant_id}")
                    fixed_count += 1
                else:
                    print(f"❌ Failed to fix assistant: {assistant_id}")
            else:
                print(f"✅ Assistant {assistant_id} already has workflow: {current_workflow}")
        
        print(f"🎉 Fixed {fixed_count} assistants")
        return fixed_count
        
    except Exception as e:
        print(f"❌ Error fixing assistants: {e}")
        import traceback
        traceback.print_exc()
        return 0


async def main():
    """Fix existing assistants and create default workflow."""
    print("🚀 Fixing Assistant Workflow Issues\n")
    
    # Step 1: Create default workflow
    workflow_id = await create_default_workflow()
    if not workflow_id:
        print("❌ Cannot proceed without default workflow")
        return False
    
    print()
    
    # Step 2: Fix existing assistants
    fixed_count = await fix_existing_assistants()
    
    print(f"\n{'='*50}")
    print("SUMMARY")
    print('='*50)
    print(f"Default workflow: {'✅ Ready' if workflow_id else '❌ Failed'}")
    print(f"Assistants fixed: {fixed_count}")
    
    if workflow_id and fixed_count >= 0:
        print("\n🎉 All assistants should now have working workflows!")
        print("Try sending a message to test the chat functionality.")
    else:
        print("\n⚠️  Some issues remain. Please check the errors above.")
    
    return workflow_id and fixed_count >= 0


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
