[{"role": "user", "event_type": "user_input", "conversation_id": "8a27f4e7-f39b-4be5-8f4f-6bcc65e36269", "user_id": "alexander", "assistant_id": "langchain_code_agent", "content": "in /app/src/main.py?", "timestamp": "2025-06-30T22:48:31.952386"}, {"role": "assistant", "event_type": "planner_call", "planner_input": {"input": "in /app/src/main.py?", "chat_history": "system: Previous conversation summary: The user initially encountered errors with the assistant's workflow and requested information on a broad topic. They then asked for help locating logger.info operations in their code, but the assistant was unable to find relevant code snippets. The conversation continued with the user specifying the directory to search in, but ultimately ended with a workflow execution failure.\nai: Workflow execution failed: An unexpected error occurred with LangGraph agent.\nsystem: You are a helpful assistant. The user seems to be referencing file paths frequently. Consider using the `search_files` tool to locate specific files or the `read_file` tool to view their contents.", "tool_names": ["qdrant_index_codebase_tool", "execute_code", "QdrantRetriever", "Neo4jQuery", "search_code_patterns"]}, "planner_output": "1. Use the `search_files` tool to locate the /app/src/main.py file in the specified directory.\n2. Once the file is located, use the `read_file` tool to view the contents of /app/src/main.py for logger.info operations.\n3. Analyze the code structure and patterns in /app/src/main.py to identify any logger.info operations or relevant code snippets.\n4. If logger.info operations are found, analyze the context and surrounding code to understand their usage and purpose.\n5. If necessary, propose fixes or suggestions based on the analysis of the logger.info operations in /app/src/main.py.", "timestamp": "2025-06-30T22:48:39.188348"}, {"role": "assistant", "event_type": "agent_llm_call", "llm_input": {"input": "in /app/src/main.py?", "chat_history": ["content=\"Previous conversation summary: The user initially encountered errors with the assistant's workflow and requested information on a broad topic. They then asked for help locating logger.info operations in their code, but the assistant was unable to find relevant code snippets. The conversation continued with the user specifying the directory to search in, but ultimately ended with a workflow execution failure.\" additional_kwargs={} response_metadata={}", "content='Workflow execution failed: An unexpected error occurred with LangGraph agent.' additional_kwargs={} response_metadata={}", "content='You are a helpful assistant. The user seems to be referencing file paths frequently. Consider using the `search_files` tool to locate specific files or the `read_file` tool to view their contents.' additional_kwargs={} response_metadata={}"], "intermediate_steps": [["tool='search_code_patterns' tool_input={'query': 'logger.info', 'file_pattern': 'main.py'} log=\"\\nInvoking: `search_code_patterns` with `{'query': 'logger.info', 'file_pattern': 'main.py'}`\\n\\n\\n\" message_log=[AIMessage(content='', additional_kwargs={'function_call': {'arguments': '{\"query\":\"logger.info\",\"file_pattern\":\"main.py\"}', 'name': 'search_code_patterns'}}, response_metadata={'token_usage': {'completion_tokens': 22, 'prompt_tokens': 757, 'total_tokens': 779, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-3.5-turbo', 'system_fingerprint': None, 'finish_reason': 'function_call', 'logprobs': None}, id='run--ed9df926-bdf0-4f9d-a6f5-fc0da9e81ce0-0')]", "Code search results for 'logger.info':\n/app/src/agent/code_agent_tools.py:116:                logger.info(\"ripgrep not available, falling back to grep\")\n/app/src/agent/post_process.py:6:    logger.info(f\"[Workflow] Postprocessing output: {state.get('output')}\")\n/app/src/agent/combine_conversation_history.py:23:            logger.info(f\"Retrieved {len(qdrant_history)} messages from Qdrant for conversation {conversation_id}\")\n/app/src/agent/combine_conversation_history.py:74:    logger.info(f\"Combined conversation history has {len(combined_history)} messages\")\n/app/src/agent/preprocess_node.py:8:    logger.info(f\"[Workflow] Preprocessing input: {state.get('input_text')}\")\n/app/src/agent/clever_retrieve_file_content.py:15:            logger.info(f\"[clever_retrieve_file_content] MCPClient read_file_content result: {result!r}\")\n/app/src/agent/clever_retrieve_file_content.py:53:        logger.info(f\"Filesystem MCP could not retrieve '{file_path}': {e}\")\n/app/src/agent/clever_retrieve_file_content.py:85:                        logger.info(f\"Neo4j file path '{neo4j_path}' not found in filesystem: {e2}\")\n/app/src/agent/clever_retrieve_file_content.py:87:        logger.info(f\"Neo4j lookup failed for '{file_path}': {e}\")\n/app/src/agent/clever_retrieve_file_content.py:113:        logger.info(f\"Qdrant lookup failed for '{file_path}': {e}\")\n/app/src/agent/context_management.py:37:            logger.info(f\"Added summary of older chat history to context.\")\n/app/src/agent/context_management.py:68:                    logger.info(f\"Adding graph context to agent: {graph_context[:100]}...\")\n/app/src/agent/code_agent_preprocessing.py:258:    logger.info(f\"Enhanced agent state for code task. Intent: {intent_analysis['primary_intent']}, \"\n/app/src/agent/get_relevant_langchain_tools.py:25:            logger.info(f\"LangChainService (id={id(self)}): Fetched {len(relevant_mcp_tools)} relevant MCP tools for query: '{current_user_message[:50]}...'\")\n/app/src/agent/get_relevant_langchain_tools.py:103:        logger.info(f\"LangChainService (id={id(self)}): Added {len(essential_tools)} essential code agent tools\")\n/app/src/agent/get_relevant_langchain_tools.py:109:        # logger.info(f\"LangChainService (id={id(self)}): Added {len(essential_qdrant_tools)} essential Qdrant tools\")\n/app/src/agent/get_relevant_langchain_tools.py:114:    logger.info(f\"LangChainService (id={id(self)}): Final list of {len(relevant_langchain_tools)} Langchain tools for this invocation: {[t.name for t in relevant_langchain_tools]}\")\n/app/src/agent/summarize_node.py:8:    logger.info(f\"[Workflow] Summarizing history if needed.\")\n/app/src/agent/summarize_node.py:36:                logger.info(f\"[Workflow] Added summary of older chat history to context.\")\n/app/src/agent/store_conversation.py:22:            logger.info(f\"{self.name}: Final Langchain interaction saved to Neo4j.\")\n/app/src/agent/store_conversation.py:37:            logger.info(f\"{self.name}: Final Langchain interaction saved to Qdrant.\")\n/app/src/main.py:94:    logger.info(\"Lifespan: Initializing application components in worker...\")\n/app/src/main.py:99:    logger.info(\"Lifespan: Initializing routes...\")\n/app/src/main.py:111:    logger.info(\"Lifespan: Application components and routes initialized.\")\n/app/src/main.py:115:    #     logger.info(\"Lifespan: Cleaning up MCP servers during application shutdown...\")\n/app/src/main.py:117:    #     logger.info(\"Lifespan: MCP servers cleanup completed.\")\n/app/src/main.py:171:logger.info(\"Initializing application components...\")\n/app/src/main.py:180:        logger.info(\"Creating SSL context...\")\n/app/src/main.py:181:        logger.info(f\"Certificate file: {args.cert}\")\n/app/src/main.py:182:        logger.info(f\"Key file: {args.key}\")\n/app/src/main.py:184:        logger.info(\"SSL context created successfully\")\n/app/src/main.py:196:    logger.info(\"Keycloak initialized successfully\")\n/app/src/main.py:204:logger.info(\"python-socketio AsyncServer (sio) initialized.\")\n/app/src/main.py:234:        logger.info(\"Initializing MCPClient and its components...\")\n/app/src/main.py:236:        logger.info(f\"Global MCPClient created. Its chat_session ID: {id(mcp_client.chat_session)}. Servers for this instance will NOT be initialized here.\")\n/app/src/main.py:238:        logger.info(\"Initializing service managers...\")\n/app/src/main.py:281:        logger.info(\"All async initializations in main_initialization completed.\")\n/app/src/main.py:304:    logger.info(\"Preparing to start Uvicorn server via run_server_task...\")\n/app/src/main.py:311:        logger.info(f\"Uvicorn will run with app_target (import string for reload): {app_target_for_uvicorn}\")\n/app/src/main.py:314:        logger.info(f\"Uvicorn will run with app_target (ASGI app object): {app_target_for_uvicorn}\")\n/app/src/main.py:322:        logger.info(\"Reload mode is ON. Calling create_and_run_server directly for Uvicorn reloader.\")\n/app/src/main.py:333:        logger.info(\"Reload mode is OFF. Calling create_and_run_server via anyio.to_thread.run_sync.\")\n/app/src/main.py:344:    logger.info(\"Uvicorn server task (run_server_task) finished.\")\n/app/src/main.py:352:        logger.info(\"Initializing Elasticsearch (synchronous part)...\")\n/app/src/main.py:355:        logger.info(\"Starting main_initialization (asynchronous part)...\")\n/app/src/main.py:357:        logger.info(\"Asynchronous initializations finished.\")\n/app/src/main.py:359:        logger.info(\"Starting server components...\")\n/app/src/main.py:363:            logger.info(\"Uvicorn server task started via run_server_task. Main async runner will now wait for shutdown signal.\")\n/app/src/main.py:365:            logger.info(\"Shutdown signal received, cancelling server task group.\")\n/app/src/main.py:373:        logger.info(\"main_async_runner finished.\")\n/app/src/main.py:376:    logger.info(f\"Signal {signum} received, initiating shutdown...\")\n/app/src/main.py:388:        logger.info(\"Application main_async_runner finished.\")\n/app/src/main.py:390:        logger.info(\"Application shutting down due to KeyboardInterrupt (main)...\")\n/app/src/main.py:397:        logger.info(f\"Application exiting with code {exit_code}.\")\n/app/src/services/utils/code_agent/phase3_implementation.py:47:    logger.info(f\"[Phase 3] Starting Implementation for plan: {generated_plan[:150]}...\")\n/app/src/services/utils/code_agent/phase3_implementation.py:75:        logger.info(f\"[Phase 3] Implementation Turn {i+1}/{max_implementation_turns}. Message count: {len(messages)}\")\n/app/src/services/utils/code_agent/phase3_implementation.py:83:                logger.info(f\"[Phase 3] Pruned messages for LLM call. Original: {len(messages)}, Sent: {len(messages_for_llm_call)}\")\n/app/src/services/utils/code_agent/phase3_implementation.py:100:                logger.info(f\"[Phase 3] LLM requested tool calls: {tool_calls}\")\n/app/src/services/utils/code_agent/phase3_implementation.py:132:                                logger.info(f\"[Phase 3] Recorded proposal for {file_path}. Notes: {notes}\")\n/app/src/services/utils/code_agent/phase3_implementation.py:172:                logger.info(f\"[Phase 3] LLM provided content (assumed to be progress or completion). Content: {response_content[:200]}...\")\n/app/src/services/utils/code_agent/phase3_implementation.py:215:    logger.info(f\"[Phase 3] Successfully completed implementation. Proposed changes: {len(current_proposed_changes)}\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:49:    logger.info(f\"[Phase 2] Starting Plan Generation for goal: {clarified_goal[:100]}...\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:59:        logger.info(f\"[Phase 2] Added {len(conversation_history)} history messages to planning prompt.\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:68:        logger.info(f\"[Phase 2] Planning Turn {i+1}/{max_planning_turns}. Message count: {len(messages)}\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:82:                logger.info(f\"[Phase 2] LLM requested tool calls: {tool_calls}\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:106:                logger.info(f\"[Phase 2] LLM provided content (assumed to be the plan). Content: {response_content[:200]}...\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:137:    logger.info(f\"[Phase 2] Successfully generated plan:\\n{generated_plan}\")\n/app/src/services/utils/code_agent/phase4_review.py:29:    logger.info(\"[Phase 4] Starting: Reviewing and Presenting Changes.\")\n/app/src/services/utils/code_agent/phase4_review.py:33:        logger.info(\"[Phase 4] No proposed changes to review.\")\n/app/src/services/utils/code_agent/phase4_review.py:44:    logger.info(f\"[Phase 4] Generating diffs for {len(current_proposed_changes)} proposed changes.\")\n/app/src/services/utils/code_agent/phase4_review.py:66:                logger.info(f\"[Phase 4] Read current content of {file_path} for diff.\")\n/app/src/services/utils/code_agent/phase4_review.py:68:                logger.info(f\"[Phase 4] File {file_path} not found for diff. Assuming it's a new file proposal.\")\n/app/src/services/utils/code_agent/phase4_review.py:86:                logger.info(f\"[Phase 4] Calling MCP 'get-unified-diff' for {file_path}\")\n/app/src/services/utils/code_agent/phase4_review.py:96:                    logger.info(f\"[Phase 4] Extracted diff text from CallToolResult for {file_path}\")\n/app/src/services/utils/code_agent/phase4_review.py:123:    logger.info(f\"[Phase 4] Review and presentation phase finished.\")\n/app/src/services/utils/code_agent/phase1_clarification.py:27:    logger.info(f\"[Phase 1 Logic] Clarifying request: '{initial_request}'\")\n/app/src/services/utils/code_agent/phase1_clarification.py:69:        logger.info(f\"[Phase 1 Logic] Raw LLM response for clarification: '{response_content}'\")\n/app/src/services/utils/code_agent/phase1_clarification.py:84:            logger.info(f\"[Phase 1 Logic] Successfully clarified goal: '{parsed_result['clarified_goal']}'\")\n/app/src/services/utils/code_agent/phase1_clarification.py:99:            logger.info(f\"[Phase 1 Logic] Clarifying questions identified: {parsed_result['clarifying_questions_needed']}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:173:        logger.info(f\"AICodeAgentServiceLangchain initialized with ID: {self.id}, Model: {self._model}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:176:        logger.info(f\"AICodeAgentServiceLangchain.initialize (id={id(self)}): Starting initialization.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:178:            logger.info(f\"AICodeAgentServiceLangchain.initialize (id={id(self)}): Dependencies already initialized. Skipping re-initialization.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:184:            logger.info(f\"AICodeAgentServiceLangchain (id={id(self)}): MyQdrantClient initialized for host: {os.getenv('QDRANT_HOST', 'localhost')}:{os.getenv('QDRANT_PORT', 6333)}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:194:            logger.info(f\"AICodeAgentServiceLangchain: Qdrant client initialized for code search at {QDRANT_CODE_HOST}:{QDRANT_CODE_PORT}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:201:            logger.info(f\"AICodeAgentServiceLangchain: Ensured Qdrant collection '{self._codebase_collection_name}' exists.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:269:        logger.info(\"AICodeAgentServiceLangchain: Added local tool 'propose_code_modification'.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:272:        logger.info(f\"AICodeAgentServiceLangchain.initialize (id={id(self)}): Dependencies initialized.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:285:        logger.info(f\"Recorded proposal for {file_path}. Notes: {notes}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:308:        logger.info(f\"AICodeAgentServiceLangchain: Attempting to execute tool: {func_name} with arguments: {args}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:330:        logger.info(f\"Adapter called for MCP Tool: {tool_name_to_exec}. Langchain raw_pos_args: {args_from_langchain}, raw_kw_args: {kwargs_from_langchain}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:364:            logger.info(f\"Executing MCP Tool: {tool_name_to_exec} with determined args for MCP: {actual_args_for_mcp}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:473:        logger.info(f\"Sending workflow status update: Phase='{phase}', Message='{status_message}', TargetRoom='{room_target}'\")\n/app/src/services/implementations/aic_code_agent_langchain.py:514:            logger.info(f\"Conceptual save of workflow step '{step_name}' for user '{username}', convo '{conversation_id}'. Status: {status}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:678:        logger.info(f\"Executing {self.name} (id={id(self)}) with input: {input_data}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:815:            logger.info(f\"{self.name}: Returning formatted service response: {json.dumps(formatted_response, default=str)}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:100:            logger.info(f\"[red]Could not read config {path}: {exc}[/red]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:127:        logger.info(f\"[green]Started MCP server:[/green] {name}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:142:            logger.info(f\"[red]tools/list error from {server_name}: {rsp['error']}[/red]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:253:                logger.info(f\"[green]Discovered {len(server_tools)} tools from {server_name}.[/green]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:255:                logger.info(f\"[yellow]Could not list tools from {server_name}: {exc}[/yellow]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:258:        logger.info(f\"[bold blue]Total available tools:[/bold blue] {len(self.oa_tools)}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:264:        logger.info(\"[bold magenta]MCP Agent (OpenAI edition)[/bold magenta]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:265:        logger.info(\"Type 'exit' to quit.\\n\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:269:            logger.info(\"[yellow]No tools found – continuing with plain chat.[/yellow]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:289:            logger.info(f\"\\n[bold green]Assistant:[/bold green] {asst_msg.content}\\n\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:331:                logger.info(f\"\\n[bold green]Assistant:[/bold green] {follow.content}\\n\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:386:        logger.info(f\"\\n[bold yellow]Tool Call:[/bold yellow] {tool_call.name}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:387:        logger.info(f\"[bold yellow]Arguments:[/bold yellow] {json.dumps(tool_call.arguments, indent=2)}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:407:            logger.info(f\"\\n[bold yellow]Reasoning:[/bold yellow]\\n{message.reasoning_content}\\n\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:479:        logger.info(f\"\\n[bold blue]Conversation trace saved to:[/bold blue] {trace_path}\")\n/app/src/services/implementations/langchain_text_service.py:127:            logger.info(f\"LangChainService: Backward compatibility - mapping 'langchain_service' to '{self.DEFAULT_SERVICE_ID}'\")\n/app/src/services/implementations/langchain_text_service.py:156:        logger.info(f\"LangChainCodeAgent initialized with ID: {self.id}, Model: {self._model}\")\n/app/src/services/implementations/langchain_text_service.py:157:        # logger.info(f\"kwargs: {kwargs}, args: {args}\") # Potentially verbose\n/app/src/services/implementations/langchain_text_service.py:206:            logger.info(f\"Generated conversation summary: {summary[:100]}...\")\n/app/src/services/implementations/langchain_text_service.py:228:        logger.info(f\"LangChainService.initialize (id={id(self)}): Starting initialization.\")\n/app/src/services/implementations/langchain_text_service.py:230:            logger.info(f\"LangChainService.initialize (id={id(self)}): Dependencies (clients, MCP tool defs) already initialized. Skipping re-initialization.\")\n/app/src/services/implementations/langchain_text_service.py:236:            logger.info(f\"LangChainService (id={id(self)}): MyQdrantClient initialized for host: {QDRANT_HOST}:{QDRANT_PORT}\")\n/app/src/services/implementations/langchain_text_service.py:247:                logger.info(f\"LangChainService (id={id(self)}): Neo4jGraphAnalytics initialized with Neo4j client\")\n/app/src/services/implementations/langchain_text_service.py:259:            logger.info(f\"LangChainService (id={id(self)}): Elastic client initialized.\")\n/app/src/services/implementations/langchain_text_service.py:273:                logger.info(f\"LangChainService (id={id(self)}): FunctionsManager's MCP servers not fully initialized. Attempting to initialize them now via mcp_cli.chat_session.initialize_servers().\")\n/app/src/services/implementations/langchain_text_service.py:277:            logger.info(f\"LangChainService (id={id(self)}): Status of MCPClient servers before listing tools:\")\n/app/src/services/implementations/langchain_text_service.py:280:                logger.info(f\"  - Server {s_idx} ('{s_server.name}' id={id(s_server)}): Session {session_status}\")\n/app/src/services/implementations/langchain_text_service.py:289:                logger.info(f\"LangChainService (id={id(self)}): Fetched and stored {len(self._all_mcp_tool_definitions)} MCPClientTool definitions.\")\n/app/src/services/implementations/langchain_text_service.py:293:                logger.info(f\"LangChainService (id={id(self)}): Fetched and stored {len(self._all_mcp_tool_definitions)} MCPClientTool definitions.\")\n/app/src/services/implementations/langchain_text_service.py:311:        logger.info(f\"LangChainService.initialize (id={id(self)}): Dependencies initialized.\")\n/app/src/services/implementations/langchain_text_service.py:316:        logger.info(f\"Adapter called for MCP Tool: {tool_name_to_exec}. Langchain raw_pos_args: {args_from_langchain}, raw_kw_args: {kwargs_from_langchain}\")\n/app/src/services/implementations/langchain_text_service.py:352:                        logger.info(f\"Mapped single positional arg from Langchain to '{first_prop_name}' for tool '{tool_name_to_exec}'.\")\n/app/src/services/implementations/langchain_text_service.py:362:                            logger.info(f\"Mapped positional arg from Langchain to '{prop_name}' for tool '{tool_name_to_exec}'.\")\n/app/src/services/implementations/langchain_text_service.py:366:                    logger.info(f\"Tool '{tool_name_to_exec}' expects arguments, but none were provided by Langchain. Passing empty args to MCP tool.\")\n/app/src/services/implementations/langchain_text_service.py:376:            logger.info(f\"Executing MCP Tool: {tool_name_to_exec} with determined args for MCP: {actual_args_for_mcp}\")\n/app/src/services/implementations/langchain_text_service.py:409:                        logger.info(f\"Tool '{tool_name_to_exec}' generated an image. Returning placeholder to agent: '{text_output_for_agent}'\")\n/app/src/services/implementations/langchain_text_service.py:499:        logger.info(\"---CODE AGENT PLANNER NODE: Generating execution plan---\")\n/app/src/services/implementations/langchain_text_service.py:561:                logger.info(\"Planner determined no specific plan is needed.\")\n/app/src/services/implementations/langchain_text_service.py:564:                logger.info(f\"Generated Plan:\\n{plan_text}\")\n/app/src/services/implementations/langchain_text_service.py:660:            logger.info(f\"Agent decided to finish. Final output: {state['output'][:100]}...\")\n/app/src/services/implementations/langchain_text_service.py:662:            logger.info(f\"Agent decided to call tool: {agent_outcome.tool} with args: {agent_outcome.tool_input}\")\n/app/src/services/implementations/langchain_text_service.py:689:                logger.info(f\"Executing tool '{tool_name}' with input: {tool_input}\")\n/app/src/services/implementations/langchain_text_service.py:698:                logger.info(f\"Tool '{tool_name}' executed. Observation: {observation_str[:200]}...\")\n/app/src/services/implementations/langchain_text_service.py:711:        logger.info(f\"Executing {self.name} (id={id(self)}) with input: {input_data}\")\n/app/src/services/implementations/langchain_text_service.py:742:                logger.info(f\"{self.name}: Found {len(tools_for_this_invocation)} relevant tools.\")\n/app/src/services/implementations/langchain_text_service.py:761:                    logger.info(f\"{self.name}: {len(combined_history_messages)} messages exceed limit of {self._history_limit}, summarizing...\")\n/app/src/services/implementations/langchain_text_service.py:779:                    logger.info(f\"{self.name}: Summarized to {len(combined_history_messages)} messages with summary\")\n/app/src/services/implementations/langchain_text_service.py:781:                logger.info(f\"{self.name}: Using {len(combined_history_messages)} combined history messages for agent.\")\n/app/src/services/implementations/langchain_text_service.py:832:                logger.info(f\"Enhanced agent state with code analysis. Code task: {state.get('is_code_task', False)}\")\n/app/src/services/implementations/langchain_text_service.py:893:            logger.info(f\"{self.name}: LangGraph agent execution completed. Response: {final_response_text}\")\n/app/src/services/implementations/langchain_text_service.py:907:            logger.info(f\"{self.name}: Returning formatted service response: {json.dumps(formatted_response, default=str)}\")\n/app/src/services/implementations/langchain_text_service.py:912:            logger.info(f\"{self.name}: Returning error service response: {json.dumps(error_response, default=str)}\")\n/app/src/services/implementations/langchain_text_service.py:918:                logger.info(f\"Saved execution trace for conversation {conversation_id}\")\n/app/src/services/implementations/gpt_text_service.py:79:        logger.info(f\"AITextService initialized with ID: {self.id}, Model: {self._model}\")\n/app/src/services/implementations/gpt_text_service.py:80:        # logger.info(f\"kwargs: {kwargs}, args: {args}\") # Potentially verbose\n/app/src/services/implementations/gpt_text_service.py:96:        logger.info(f\"Initializing AITextService: {self.id}\")\n/app/src/services/implementations/gpt_text_service.py:111:            logger.info(f\"AITextService: MyQdrantClient initialized for host: {QDRANT_HOST}:{QDRANT_PORT}\")\n/app/src/services/implementations/gpt_text_service.py:119:            logger.info(\"AITextService: Elastic client initialized.\")\n/app/src/services/implementations/gpt_text_service.py:133:            logger.info(f\"{self.name}: FunctionsManager's MCP servers not fully initialized. Initializing now.\")\n/app/src/services/implementations/gpt_text_service.py:142:                logger.info(f\"AITextService: Loaded {len(self.available_functions)} available functions from MCP for LLM.\")\n/app/src/services/implementations/gpt_text_service.py:150:        logger.info(\"AITextService: Added local tool 'get_similar_messages_tool'.\")\n/app/src/services/implementations/gpt_text_service.py:249:        logger.info(f\"AITextService: Attempting to execute tool: {func_name} with arguments: {args}\")\n/app/src/services/implementations/gpt_text_service.py:312:                    logger.info(f\"Extracted text from MCP tool '{func_name}' specific structure: '{func_response_content}'\")\n/app/src/services/implementations/gpt_text_service.py:316:                    logger.info(f\"Extracted text from MCP tool '{func_name}' .text attribute: '{func_response_content}'\")\n/app/src/services/implementations/gpt_text_service.py:337:        logger.info(f\"Executing {self.name} with input: {input_data}\")\n/app/src/services/implementations/gpt_text_service.py:361:                logger.info(f\"AITextService: Detected summary command: '{current_user_message}'\")\n/app/src/services/implementations/gpt_text_service.py:401:                logger.info(f\"AITextService: Using system message from input data for dynamic prompt generation.\")\n/app/src/services/implementations/gpt_text_service.py:409:                        logger.info(f\"AITextService: Using system message for assistant_id '{assistant_id}' from ES for dynamic prompt generation.\")\n/app/src/services/implementations/gpt_text_service.py:438:                logger.info(f\"AITextService: Final list of {len(tools_for_llm_call)} tools for LLM.\")\n/app/src/services/implementations/gpt_text_service.py:442:            logger.info(f\"AITextService: Making first LLM call. Msgs: {len(messages_for_llm_turn1)}, Tools: {len(tools_for_llm_call or [])}\")\n/app/src/services/implementations/gpt_text_service.py:457:                logger.info(f\"AITextService: LLM requested tool calls: {assistant_message_turn1.tool_calls}\")\n/app/src/services/implementations/gpt_text_service.py:519:                    logger.info(f\"AITextService: Added explicit user instruction for 2nd LLM call: {user_instruction_for_turn2}\")\n/app/src/services/implementations/gpt_text_service.py:522:                logger.info(\"AITextService: Making second LLM call with tool responses and potentially explicit instruction.\")\n/app/src/services/implementations/gpt_text_service.py:533:                logger.info(\"AITextService: LLM responded directly (no tool calls).\")\n/app/src/services/implementations/gpt_text_service.py:550:                    logger.info(\"AITextService: Final RAG interaction saved to Qdrant.\")\n/app/src/services/implementations/gpt_code_agent.py:87:        logger.info(f\"AICodeAnalyserService initialized with ID: {self.id}, Model: {self._model}\")\n/app/src/services/implementations/gpt_code_agent.py:97:        logger.info(f\"Initializing AICodeAnalyserService: {self.id}\")\n/app/src/services/implementations/gpt_code_agent.py:110:            logger.info(f\"AICodeAnalyserService: Qdrant client initialized for code search at {QDRANT_HOST}:{QDRANT_PORT}\")\n/app/src/services/implementations/gpt_code_agent.py:111:            logger.info(f\"AICodeAnalyserService: Code Embedding Generator configured to use server: {embedding_server_url} (intended model: {code_embedding_model_name})\")\n/app/src/services/implementations/gpt_code_agent.py:120:            logger.info(f\"AICodeAnalyserService: Ensured Qdrant collection '{self._codebase_collection_name}' exists with vector size {CODE_EMBEDDING_DIMENSION}.\")\n/app/src/services/implementations/gpt_code_agent.py:152:        logger.info(\"AICodeAnalyserService: Added local tool 'propose_code_modification'.\")\n/app/src/services/implementations/gpt_code_agent.py:175:        logger.info(f\"AICodeAnalyserService: Attempting to execute tool: {func_name} with arguments: {args}\")\n/app/src/services/implementations/gpt_code_agent.py:195:                logger.info(f\"Recorded proposal for {file_path}. Notes: {notes}\")\n/app/src/services/implementations/gpt_code_agent.py:248:        logger.info(f\"Sending workflow status update: Phase='{phase}', Message='{status_message}', TargetRoom='{room_target}'\")\n/app/src/services/implementations/gpt_code_agent.py:262:        logger.info(f\"Executing AICodeAnalyserService ({self.name}) with input: {input_data}\")\n/app/src/services/implementations/gpt_code_agent.py:278:                logger.info(f\"Current Workflow State: {current_state.name}\")\n/app/src/services/implementations/gpt_code_agent.py:373:                        logger.info(f\"[Workflow] Clarified Goal: {clarified_goal}\")\n/app/src/services/implementations/gpt_code_agent.py:393:                                logger.info(f\"[Workflow] Fetched {len(qdrant_history_messages)} history messages for planning.\")\n/app/src/services/implementations/gpt_code_agent.py:437:                        logger.info(f\"[Workflow] Generated Plan:\\n{generated_plan}\")\n/app/src/services/implementations/gpt_code_agent.py:480:                    logger.info(f\"[Workflow] Phase 3 Finished. Output: {final_implementation_output[:200]}...\")\n/app/src/services/implementations/gpt_code_agent.py:532:                logger.info(f\"[Workflow] Awaiting user clarification. Questions: {clarifying_questions}\")\n/app/src/services/implementations/gpt_code_agent.py:551:                        logger.info(\"AICodeAnalyserService: Final workflow interaction saved to Qdrant.\")\n/app/src/services/implementations/gpt_code_agent.py:555:                logger.info(\"AICodeAnalyserService: Workflow finished successfully.\")\n/app/src/services/implementations/gpt_code_agent.py:633:            logger.info(f\"Conceptual save of workflow step '{step_name}' for user '{username}', convo '{conversation_id}'. Status: {status}\")\n/app/src/managers/AssistantManager.py:33:            logger.info(f\"GET OR CREATE USER : {username} {assistant_id}\")\n/app/src/managers/AssistantManager.py:40:            logger.info(f\"IN GET USER WITH  : {assistant}\")\n/app/src/managers/AssistantManager.py:64:                    logger.info(f\"Stored assistant in Neo4j: {assistant_id}\")\n/app/src/managers/AssistantManager.py:68:            logger.info(f\"Created/retrieved user: {username} with key: {user_key}\")\n/app/src/managers/AssistantManager.py:88:                    logger.info(\"Would get assistants from Neo4j if method was implemented\")\n/app/src/managers/AssistantManager.py:132:        logger.info(\"UPDATE ASSISTANT\")\n/app/src/managers/AssistantManager.py:153:            logger.info(f\"Before store Assistant {updated_data}\")\n/app/src/managers/AssistantManager.py:165:                    logger.info(f\"Updated assistant in Neo4j: {assistant_id}\")\n/app/src/managers/AssistantManager.py:178:            logger.info(f\"Getting Assistant: {assistant_id}\")\n/app/src/managers/AssistantManager.py:188:                    logger.info(f\"Would get assistant {assistant_id} from Neo4j if method was implemented\")\n/app/src/managers/AssistantManager.py:216:                logger.info(f\"Stored assistant in Neo4j: {data['id']}\")\n/app/src/managers/ServicesManager.py:46:        logger.info(f\"ServicesManager __init__ effective base_service_config keys: {list(self._internal_base_service_config.keys())}\")\n/app/src/managers/ServicesManager.py:55:        logger.info(\n/app/src/managers/ServicesManager.py:78:            logger.info(\n/app/src/managers/ServicesManager.py:99:            logger.info(\"ServicesManager already initialized. Skipping.\")\n/app/src/managers/ServicesManager.py:102:        logger.info(\"ServicesManager: Starting initialization process...\")\n/app/src/managers/ServicesManager.py:108:            logger.info(\"ServicesManager: FunctionsManager dependency not initialized. Initializing now...\")\n/app/src/managers/ServicesManager.py:115:            logger.info(\"ServicesManager: FunctionsManager already initialized.\")\n/app/src/managers/ServicesManager.py:117:        logger.info(\"ServicesManager: Proceeding to discover services...\")\n/app/src/managers/ServicesManager.py:124:            logger.info(f\"ServicesManager.initialize: Initializing {len(self.services)} discovered services...\")\n/app/src/managers/ServicesManager.py:135:                    logger.info(f\"ServicesManager: Service '{service_id_res}' initialized successfully (Result: {result}).\")\n/app/src/managers/ServicesManager.py:142:        logger.info(f\"ServicesManager: Initialization complete. Total services loaded and initialized: {final_service_count}.\")\n/app/src/managers/ServicesManager.py:157:                logger.info(\"ServicesManager: Default workflow already exists\")\n/app/src/managers/ServicesManager.py:160:            logger.info(\"ServicesManager: Creating default workflow for assistants...\")\n/app/src/managers/ServicesManager.py:186:                logger.info(f\"ServicesManager: Default workflow created successfully: {workflow_id}\")\n/app/src/managers/ServicesManager.py:196:            logger.info(f\"Initializing service: {service_id}\")\n/app/src/managers/ServicesManager.py:203:                    logger.info(f\"Would store service initialization start in Neo4j: {service_id}\")\n/app/src/managers/ServicesManager.py:219:                    logger.info(f\"Would store service initialization result in Neo4j: {service_id}, result: {result}\")\n/app/src/managers/ServicesManager.py:229:            logger.info(f\"Service {service_id} initialized: {result}\")\n/app/src/managers/ServicesManager.py:237:        logger.info(f\"ServicesManager._discover_services: Starting. Dependencies status: \"\n/app/src/managers/ServicesManager.py:248:                logger.info(f\"ServicesManager._discover_services: Created missing services directory: {services_path}\")\n/app/src/managers/ServicesManager.py:251:            logger.info(f\"ServicesManager._discover_services: Scanning for services in: {services_path}\")\n/app/src/managers/ServicesManager.py:258:            logger.info(f\"ServicesManager._discover_services: Found potential service files: {service_files}\")\n/app/src/managers/ServicesManager.py:263:                    logger.info(f\"ServicesManager._discover_services: Attempting to import module: {module_name}\")\n/app/src/managers/ServicesManager.py:274:                            logger.info(f\"ServicesManager._discover_services: Found service class '{name}' in {module_name}.\")\n/app/src/managers/ServicesManager.py:321:                                logger.info(f\"ServicesManager._discover_services: Successfully loaded and added service '{service_instance.id}' from class '{name}'. Total services now: {len(self.services)}\")\n/app/src/managers/ServicesManager.py:335:        logger.info(f\"ServicesManager._discover_services: Discovery finished. Total services in manager: {len(self.services)}\")\n/app/src/managers/ServicesManager.py:351:            logger.info(f\"ServicesManager: Backward compatibility - mapping '{service_id}' to '{mapped_id}'\")\n/app/src/managers/ServicesManager.py:380:        logger.info(f\"ServicesManager.get_all_services: Returning {len(services_data)} services.\")\n/app/src/managers/ServicesManager.py:396:        logger.info(f\"ServicesManager.execute_service: Executing service '{service_id}' with input: {input_data}\")\n/app/src/managers/ServicesManager.py:405:                    logger.info(f\"Would store service execution start in Neo4j: {execution_id}\")\n/app/src/managers/ServicesManager.py:421:            logger.info(f\"ServicesManager.execute_service: Service '{service_id}' executed. Success: {success_status}, Message: {message_content}\")\n/app/src/managers/ServicesManager.py:428:                    logger.info(f\"Would store service execution result in Neo4j: {execution_id}, success: {success_status}\")\n/app/src/managers/WorkflowManager.py:30:        logger.info(f\"Executing (MANAGER) workflow: {workflow_id} with input: {input_data}\")\n/app/src/managers/WorkflowManager.py:45:                    logger.info(f\"Would store workflow execution start in Neo4j: {execution_id}\")\n/app/src/managers/WorkflowManager.py:57:            logger.info(f\"Executing (MANAGER) Result: {result}\")\n/app/src/managers/WorkflowManager.py:64:                    logger.info(f\"Would store workflow execution result in Neo4j: {execution_id}\")\n/app/src/managers/WorkflowManager.py:98:        logger.info(f\"Workflow Manager: Starting _execute_workflow_blocks for workflow_id: '{workflow.get('id', 'N/A')}'. Blocks: {len(blocks)}, Connections: {len(connections)}\")\n/app/src/managers/WorkflowManager.py:100:        logger.info(f\"Executing workflow with {len(blocks)} blocks and {len(connections)} connections\")\n/app/src/managers/WorkflowManager.py:108:        logger.info(f\"Block execution order: {execution_order}\")\n/app/src/managers/WorkflowManager.py:122:            logger.info(f\"Executing block {block_id} with service_id: {service_id}\")\n/app/src/managers/WorkflowManager.py:123:            logger.info(f\"Block data: {block}\")\n/app/src/managers/WorkflowManager.py:131:            logger.info(f\"Available services: {available_services}\")\n/app/src/managers/WorkflowManager.py:159:                        logger.info(f\"Would store block execution start in Neo4j: {execution_id}, block: {block_id}\")\n/app/src/managers/WorkflowManager.py:181:                        logger.info(f\"Would store block execution result in Neo4j: {execution_id}, block: {block_id}\")\n/app/src/managers/WorkflowManager.py:193:                logger.info(f\"Block {block_id} execution result: {result}\")\n/app/src/managers/WorkflowManager.py:202:            logger.info(f\"Returning result from last block {execution_order[-1]}: {result}\")\n/app/src/managers/WorkflowManager.py:208:            logger.info(f\"Returning result from block {last_block_id}: {result}\")\n/app/src/managers/SettingsManager.py:16:        logger.info(\"[SettingsManager] Initialized\")\n/app/src/managers/SettingsManager.py:30:                    logger.info(f\"Would get user settings from Neo4j for user: {username}\")\n/app/src/managers/SettingsManager.py:60:                    logger.info(f\"Would update user settings in Neo4j for user: {username}\")\n/app/src/managers/scraper/whatsapp_manager.py:25:                logger.info(\"\\n=== WhatsApp Bot Started ===\")\n/app/src/managers/scraper/whatsapp_manager.py:28:                    logger.info(\"Failed to load chat history, continuing anyway...\")\n/app/src/managers/scraper/whatsapp_manager.py:31:                    logger.info(f\"Failed to find chat with {contact_name}\")\n/app/src/managers/scraper/whatsapp_manager.py:46:                                logger.info(f\"\\nReceived new message: {message}\")\n/app/src/managers/scraper/whatsapp_manager.py:53:                                    logger.info(\"Response sent successfully\")\n/app/src/managers/scraper/whatsapp_manager.py:55:                                    logger.info(\"Failed to send response\")\n/app/src/managers/scraper/whatsapp_manager.py:58:                        logger.info(f\"\\nError in message loop: {str(e)}\")\n/app/src/managers/scraper/whatsapp_manager.py:66:                logger.info(\"\\n=== WhatsApp Bot Stopped ===\")\n/app/src/managers/scraper/whatsapp_bot.py:36:            logger.info(\"Please scan QR Code and press Enter\")\n/app/src/managers/scraper/whatsapp_bot.py:40:            logger.info(f\"Error initializing WhatsApp bot: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:81:                            logger.info(f\"Successfully entered chat: {contact_name}\")\n/app/src/managers/scraper/whatsapp_bot.py:87:                    logger.info(f\"Retry {attempt + 1} finding chat...\")\n/app/src/managers/scraper/whatsapp_bot.py:92:                    logger.info(f\"Error finding chat (attempt {attempt + 1}): {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:95:        logger.info(f\"Failed to find chat after {max_retries} attempts: {contact_name}\")\n/app/src/managers/scraper/whatsapp_bot.py:119:                    logger.info(f\"Retrying chat find... (attempt {attempt + 1})\")\n/app/src/managers/scraper/whatsapp_bot.py:132:                    logger.info(\"Could not focus main chat view\")\n/app/src/managers/scraper/whatsapp_bot.py:148:                        logger.info(f\"Trying selector: {selector}\")\n/app/src/managers/scraper/whatsapp_bot.py:165:                            logger.info(f\"Successfully found and clicked input box with selector: {selector}\")\n/app/src/managers/scraper/whatsapp_bot.py:172:                        logger.info(f\"Failed with selector {selector}: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:176:                    logger.info(\"Could not find or click message input box\")\n/app/src/managers/scraper/whatsapp_bot.py:193:                    logger.info(\"Message sent successfully\")\n/app/src/managers/scraper/whatsapp_bot.py:199:                    logger.info(f\"Error while typing message: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:203:                logger.info(f\"Error sending message (attempt {attempt + 1}): {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:240:            logger.info(f\"Error sending file: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:257:                        logger.info(f\"Could not parse timestamp: {timestamp_str}\")\n/app/src/managers/scraper/whatsapp_bot.py:260:            logger.info(f\"Error extracting timestamp: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:267:                logger.info(\"Chat not properly loaded, retrying...\")\n/app/src/managers/scraper/whatsapp_bot.py:304:                        logger.info(f\"Found new message: {message_text} (Timestamp: {timestamp})\")\n/app/src/managers/scraper/whatsapp_bot.py:310:                    logger.info(f\"Error processing individual message {idx}: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:316:            logger.info(f\"Error in check_for_new_messages: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:326:            logger.info(\"Attempting to reconnect...\")\n/app/src/managers/scraper/whatsapp_bot.py:337:            logger.info(\"Please scan QR Code again and press Enter\")\n/app/src/managers/scraper/whatsapp_bot.py:342:            logger.info(f\"Reconnection failed: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:347:        logger.info(\"\\nLoading chat history...\")\n/app/src/managers/scraper/whatsapp_bot.py:361:                        logger.info(f\"Found {len(messages)} historical messages\")\n/app/src/managers/scraper/whatsapp_bot.py:375:                                    logger.info(f\"Added to history: {message_text[:50]}... (Timestamp: {timestamp})\")\n/app/src/managers/scraper/whatsapp_bot.py:377:                                logger.info(f\"Error processing historical message {idx}: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:379:                        logger.info(f\"Latest message timestamp: {self.last_message_timestamp}\")\n/app/src/managers/scraper/whatsapp_bot.py:384:            logger.info(\"No historical messages found\")\n/app/src/managers/scraper/whatsapp_bot.py:388:            logger.info(f\"Error loading chat history: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:400:            logger.info(\"\\n=== WhatsApp Chat Bot Started ===\")\n/app/src/managers/scraper/whatsapp_bot.py:404:                logger.info(\"Failed to load chat history, continuing anyway...\")\n/app/src/managers/scraper/whatsapp_bot.py:406:            logger.info(\"Monitoring for new messages... (Press Ctrl+C to stop)\")\n/app/src/managers/scraper/whatsapp_bot.py:409:                logger.info(f\"Failed to find chat with {contact_name}\")\n/app/src/managers/scraper/whatsapp_bot.py:419:                        logger.info(\"\\nChat view not loaded, attempting to reload...\")\n/app/src/managers/scraper/whatsapp_bot.py:425:                    logger.info(\"\\nChecking for new messages...\")  # Debug line\n/app/src/managers/scraper/whatsapp_bot.py:430:                        logger.info(f\"\\nProcessing {len(new_messages)} new messages\")\n/app/src/managers/scraper/whatsapp_bot.py:433:                            logger.info(f\"\\nReceived new message: {message}\")\n/app/src/managers/scraper/whatsapp_bot.py:439:                                logger.info(\"Response sent successfully\")\n/app/src/managers/scraper/whatsapp_bot.py:441:                                logger.info(\"Failed to send response, will retry on next loop\")\n/app/src/managers/scraper/whatsapp_bot.py:445:                        logger.info(\"No new messages found\")  # Debug line\n/app/src/managers/scraper/whatsapp_bot.py:450:                    logger.info(f\"\\nError in message loop: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:457:            logger.info(\"\\n\\n=== Bot stopped by user ===\")\n/app/src/managers/scraper/whatsapp_bot.py:460:            logger.info(f\"\\nFatal error: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:462:                logger.info(\"Restarting bot...\")\n/app/src/managers/scraper/whatsapp_bot.py:470:            logger.info(\"\\n=== Closing bot... ===\")\n/app/src/managers/ChatManager.py:16:        logger.info(\"[ChatManager] Initialized\")\n/app/src/managers/ChatManager.py:20:        logger.info(f\"ChatManager: Getting conversation history for user_id='{user_id}', conversation_id='{conversation_id}', limit={limit}\")\n/app/src/managers/ChatManager.py:28:            logger.info(f\"ChatManager: Fetched messages from Qdrant for conversation_id='{conversation_id}'\")\n/app/src/managers/ChatManager.py:38:                    logger.info(f\"ChatManager: Fetched {len(neo4j_messages) if neo4j_messages else 0} messages from Neo4j for conversation_id='{conversation_id}'\")\n/app/src/managers/ChatManager.py:70:        logger.info(f\"ChatManager: Storing message for user_id='{user_id}', assistant_id='{assistant_id}', conversation_id='{conversation_id}'\")\n/app/src/managers/ChatManager.py:102:                    logger.info(f\"ChatManager: Stored messages in Neo4j for conversation_id='{conversation_id}'\")\n/app/src/managers/FunctionsManager.py:50:        logger.info(\"Initializing FunctionsManager (Qdrant RAG system for functions)\")\n/app/src/managers/FunctionsManager.py:62:            logger.info(\"FunctionsManager: Initializing MCP servers for its internal MCPClient...\")\n/app/src/managers/FunctionsManager.py:67:                logger.info(f\"FunctionsManager: {initialized_server_count} MCP servers initialized for its internal MCPClient.\")\n/app/src/managers/FunctionsManager.py:69:            logger.info(\"FunctionsManager: Synchronizing tools to Qdrant using its internal MCPClient...\")\n/app/src/managers/FunctionsManager.py:71:            logger.info(\"FunctionsManager: Tool synchronization to Qdrant completed.\")\n/app/src/managers/FunctionsManager.py:84:                logger.info(f\"Creating Qdrant collection for functions: {self.FUNCTIONS_COLLECTION_NAME}\")\n/app/src/managers/FunctionsManager.py:92:                logger.info(f\"Qdrant collection for functions '{self.FUNCTIONS_COLLECTION_NAME}' already exists.\")\n/app/src/managers/FunctionsManager.py:97:                logger.info(\"FunctionsManager: MyNeo4jClient initialized successfully.\")\n/app/src/managers/FunctionsManager.py:107:            logger.info(\"FunctionsManager initialized successfully.\")\n/app/src/managers/FunctionsManager.py:141:                logger.info(f\"No functions found in Qdrant for query: '{query}'\")\n/app/src/managers/FunctionsManager.py:156:            logger.info(f\"Found function '{tool_name_to_execute}' (ID: {best_hit.id}, Score: {best_hit.score:.4f}) for query '{query}'.\")\n/app/src/managers/FunctionsManager.py:161:            logger.info(f\"Executing MCP tool '{tool_name_to_execute}' with arguments: {input_data}\")\n/app/src/managers/FunctionsManager.py:164:            logger.info(f\"Execution result for '{tool_name_to_execute}': {execution_result}\")\n/app/src/managers/FunctionsManager.py:217:                    logger.info(\"FunctionsManager: Attempting to list functions from MyNeo4jClient.\")\n/app/src/managers/FunctionsManager.py:227:                    logger.info(f\"FunctionsManager: Added {len(neo4j_function_names)} functions from MyNeo4jClient to the list.\")\n/app/src/managers/FunctionsManager.py:231:                logger.info(\"FunctionsManager: MyNeo4jClient not available, skipping listing its functions.\")\n/app/src/servers/server.py:34:            logger.info(f\"Detected change in {event.src_path}. Reloading server...\")\n/app/src/servers/server.py:100:            logger.info(\"Reloading server...\")\n/app/src/servers/server.py:111:        logger.info(\"Starting SocketIO server...\")\n/app/src/servers/server.py:139:            logger.info(f\"Starting {'secure ' if ssl_context else ''}server on port {port}...\")\n/app/src/servers/fastApi.py:51:        logger.info(f\"Reload mode enabled. Uvicorn will watch for changes. App target: {app_target}\")\n/app/src/servers/fastApi.py:58:        logger.info(f\"Starting secure FastAPI server on https://{host}:{port}\")\n/app/src/servers/fastApi.py:61:        logger.info(f\"Starting FastAPI server on http://{host}:{port} (SSL not configured).\")\n/app/src/servers/fastApi.py:63:        logger.info(f\"Starting FastAPI server on http://{host}:{port} (SSL not configured).\")\n/app/src/servers/mcpservers/socket_bridge.py:29:    logger.info(f\"SocketBridgeMCP server '{server.name}' starting up.\")\n/app/src/servers/mcpservers/socket_bridge.py:33:    logger.info(f\"SocketBridgeMCP server '{server.name}' shutting down.\")\n/app/src/servers/mcpservers/socket_bridge.py:90:    logger.info(f\"MCP Tool 'emit_socket_event' (Call ID: {tool_call_id}): Received request to emit event '{event_name}' in namespace '{namespace or '/'}'.\")\n/app/src/servers/mcpservers/socket_bridge.py:102:        logger.info(f\"MCP Tool 'emit_socket_event': {success_msg}\")\n/app/src/servers/mcpservers/socket_bridge.py:115:    logger.info(f\"Socket.IO: Client connected: SID='{sid}'\")\n/app/src/servers/mcpservers/socket_bridge.py:122:    #     logger.info(f\"Socket.IO: Client {sid} joined room '{user_room}'\")\n/app/src/servers/mcpservers/socket_bridge.py:126:    logger.info(f\"Socket.IO: Client disconnected: SID='{sid}'\")\n/app/src/servers/mcpservers/socket_bridge.py:131:    logger.info(f\"Socket.IO: Received 'client_event' from SID='{sid}' with data: {data}\")\n/app/src/servers/mcpservers/freecad.py:74:                logger.info(\"Screenshot unavailable in current view (likely Spreadsheet or TechDraw view)\")\n/app/src/servers/mcpservers/freecad.py:97:        logger.info(\"FreeCADMCP server starting up\")\n/app/src/servers/mcpservers/freecad.py:100:            logger.info(\"Successfully connected to FreeCAD on startup\")\n/app/src/servers/mcpservers/freecad.py:111:            logger.info(\"Disconnecting from FreeCAD on shutdown\")\n/app/src/servers/mcpservers/freecad.py:113:        logger.info(\"FreeCADMCP server shut down\")\n/app/src/servers/mcpservers/freecad.py:634:    logger.info(f\"Only text feedback: {_only_text_feedback}\")\n/app/src/servers/mcpservers/freecad.py:635:    logger.info(f\"Calling mcp.run() in {__file__}\")\n/app/src/servers/mcpservers/freecad.py:640:    logger.info(f\"Starting {__file__} script directly...\")\n/app/src/servers/mcpservers/pythonExecServer.py:82:        logger.info(f\"Calling mcp.run() in {__file__} for QdrantService\")\n/app/src/servers/mcpservers/diff.py:63:    logger.info(\"Initializing mcp-server-diff-python\")\n/app/src/servers/mcpservers/dockercompose.py:17:        logger.info(\"Docker Compose MCP server starting up\")\n/app/src/servers/mcpservers/dockercompose.py:20:            logger.info(\"Docker Compose MCP server initialized successfully on startup.\")\n/app/src/servers/mcpservers/dockercompose.py:29:        logger.info(\"Docker Compose MCP server shut down\")\n/app/src/servers/mcpservers/dockercompose.py:195:    logger.info(f\"Starting {__file__} script directly...\")\n/app/src/servers/mcpservers/dockercompose.py:197:        logger.info(f\"Calling mcp.run() in {__file__}\")\n/app/src/servers/mcpservers/trace_service.py:56:        logger.info(f\"Trace saved to {fname}\")\n/app/src/servers/mcpservers/echo.py:24:        logger.info(\"Echo server starting up\")\n/app/src/servers/mcpservers/echo.py:26:            logger.info(\"Successfully connected to Echo server on startup\")\n/app/src/servers/mcpservers/echo.py:31:        logger.info(f\"Echo MCP server '{server.name}' shut down.\")\n/app/src/servers/mcpservers/echo.py:38:logger.info(\"[echo.py] Registering Echo server handlers.\")\n/app/src/servers/mcpservers/echo.py:117:    logger.info(f\"Starting {__file__} script directly...\")\n/app/src/servers/mcpservers/echo.py:119:        logger.info(f\"Calling mcp.run() in {__file__}\")\n/app/src/servers/mcpservers/neoj.py:28:    logger.info(\"Initializing Neo4j MCP server state...\")\n/app/src/servers/mcpservers/neoj.py:33:            logger.info(\"Neo4j client initialized.\")\n/app/src/servers/mcpservers/neoj.py:39:        logger.info(\"Neo4j MCP server state initialized successfully\")\n/app/src/servers/mcpservers/neoj.py:45:        logger.info(\"Cleaning up Neo4j MCP server resources...\")\n/app/src/servers/mcpservers/neoj.py:52:logger.info(\"[neo4j_service.py] Registering Neo4j MCP handlers.\")\n/app/src/servers/mcpservers/neoj.py:148:            logger.info(f\"Retrieved {len(neo4j_messages)} messages from Neo4j for conversation {conversation_id}\")\n/app/src/servers/mcpservers/neoj.py:189:        logger.info(f\"Stored message and response in Neo4j for conversation {conversation_id}\")\n/app/src/servers/mcpservers/neoj.py:217:        logger.info(f\"Generated graph-based prompt for {node_type} {node_id}\")\n/app/src/servers/mcpservers/neoj.py:252:        logger.info(f\"Generated graph-based summary for conversation {conversation_id}\")\n/app/src/servers/mcpservers/neoj.py:334:        logger.info(f\"Adjusted context window for conversation {conversation_id}, found {len(adjusted_messages)} relevant messages\")\n/app/src/servers/mcpservers/neoj.py:344:    logger.info(f\"Starting {__file__} script directly for Neo4j MCP server...\")\n/app/src/servers/mcpservers/neoj.py:346:        logger.info(f\"Calling mcp.run() in {__file__} for Neo4j MCP server\")\n/app/src/servers/mcpservers/qdrant_service.py:44:        logger.info(f\"LLMClient (placeholder) initialized. Base URL: {self.base_url}\")\n/app/src/servers/mcpservers/qdrant_service.py:76:    logger.info(\"Initializing QdrantService server state...\")\n/app/src/servers/mcpservers/qdrant_service.py:97:            logger.info(\"Neo4j client initialized for code structure sync.\")\n/app/src/servers/mcpservers/qdrant_service.py:103:        logger.info(\"QdrantService server state initialized successfully\")\n/app/src/servers/mcpservers/qdrant_service.py:109:        logger.info(\"Cleaning up QdrantService server resources...\")\n/app/src/servers/mcpservers/qdrant_service.py:118:logger.info(\"[qdrant_service.py] Registering QdrantService MCP handlers.\")\n/app/src/servers/mcpservers/qdrant_service.py:481:    logger.info(f\"Starting codebase indexing for collection '{collection_name}' with dimension {vector_dimension}.\")\n/app/src/servers/mcpservers/qdrant_service.py:482:    logger.info(f\"Root directory: {root_dir}, Extensions: {extensions or FILE_EXTENSIONS_DEFAULT}\")\n/app/src/servers/mcpservers/qdrant_service.py:483:    logger.info(f\"Using embedding generator from MyQdrantClient: {type(embedding_gen)}\")\n/app/src/servers/mcpservers/qdrant_service.py:519:                        logger.info(f\"Neo4j: Added code structure for {file_path} ({len(classes)} classes)\")\n/app/src/servers/mcpservers/qdrant_service.py:523:        logger.info(msg)\n/app/src/servers/mcpservers/qdrant_service.py:541:    logger.info(f\"Searching codebase in collection '{collection_name}' for query: '{query[:50]}...' (top_k={top_k})\")\n/app/src/servers/mcpservers/qdrant_service.py:643:    logger.info(f\"Starting {__file__} script directly for QdrantService MCP server...\")\n/app/src/servers/mcpservers/qdrant_service.py:649:        logger.info(f\"Calling mcp.run() in {__file__} for QdrantService\")\n/app/src/servers/mcpservers/time.py:71:        logger.info(\"Time MCP server starting up. Verifying locale and timezone utilities.\")\n/app/src/servers/mcpservers/time.py:75:        logger.info(\"Time MCP server utilities appear to be available.\")\n/app/src/servers/mcpservers/time.py:78:        logger.info(\"Time MCP server shut down.\")\n/app/src/servers/mcpservers/time.py:81:logger.info(\"[time.py] Simple Time Server\")\n/app/src/servers/mcpservers/time.py:153:    logger.info(f\"Starting {__file__} script directly...\")\n/app/src/servers/mcpservers/time.py:155:        logger.info(f\"Calling mcp.run() in {__file__}\")\n/app/src/servers/mcpservers/weather.py:51:            logger.info(f\"api: {url}\")\n/app/src/servers/mcpservers/weather.py:93:            logger.info(f\"api: {url}\")\n/app/src/servers/embedding_server.py:27:    logger.info(f\"Successfully loaded SentenceTransformer model: {MODEL_NAME}\")\n/app/src/servers/embedding_server.py:48:    logger.info(f\"Received request to embed text (first 50 chars): {request.text[:50]}...\")\n/app/src/servers/embedding_server.py:51:        logger.info(f\"Generated embedding of dimension: {len(embedding_vector)}\")\n/app/src/qdrant/operations/workflows.py:46:                logger.info(f\"Creating Qdrant collection: {self.collection_name}\")\n/app/src/qdrant/operations/workflows.py:54:                logger.info(f\"Created Qdrant collection: {self.collection_name}\")\n/app/src/qdrant/operations/workflows.py:56:                logger.info(f\"Qdrant collection already exists: {self.collection_name}\")\n/app/src/qdrant/operations/workflows.py:101:            logger.info(f\"Workflow saved to Qdrant: {workflow['id']}\")\n/app/src/qdrant/operations/workflows.py:129:                logger.info(f\"Retrieved workflow from Qdrant: {workflow_id}\")\n/app/src/qdrant/operations/workflows.py:159:            logger.info(f\"Found {len(workflows)} workflows for query: {query}\")\n/app/src/qdrant/operations/workflows.py:197:            logger.info(f\"Listed {len(workflows)} workflows from Qdrant\")\n/app/src/qdrant/operations/workflows.py:213:            logger.info(f\"Deleted workflow from Qdrant: {workflow_id}\")\n/app/src/qdrant/operations/assistants.py:54:                    logger.info(f\"Creating Qdrant collection: {collection_name}\")\n/app/src/qdrant/operations/assistants.py:62:                    logger.info(f\"Created Qdrant collection: {collection_name}\")\n/app/src/qdrant/operations/assistants.py:64:                    logger.info(f\"Qdrant collection already exists: {collection_name}\")\n/app/src/qdrant/operations/assistants.py:111:            logger.info(f\"Assistant stored in Qdrant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:138:                logger.info(f\"Retrieved assistant from Qdrant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:191:            logger.info(f\"Retrieved {len(assistants)} assistants from Qdrant\")\n/app/src/qdrant/operations/assistants.py:232:            logger.info(f\"System message stored in Qdrant for assistant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:259:                logger.info(f\"Retrieved system message from Qdrant for assistant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:299:            logger.info(f\"Avatar stored in Qdrant for assistant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:326:                logger.info(f\"Retrieved avatar from Qdrant for assistant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:329:                logger.info(f\"Avatar not found in Qdrant for assistant: {assistant_id}\")\n/app/src/clients/qdrant.py:84:        logger.info(f\"[Embedding] Sending to server: {self.embedding_server_url}/embed | Payload: {{'text': {text!r}}}\")\n/app/src/clients/qdrant.py:90:            #logger.info(f\"[Embedding] Server response status: {response.status}\")\n/app/src/clients/qdrant.py:93:                # logger.info(f\"[Embedding] Server response: {result}\")\n/app/src/clients/qdrant.py:112:        logger.info(f\"[Embedding] Sending to server (sync): {self.embedding_server_url}/embed | Payload: {{'text': {text!r}}}\")\n/app/src/clients/qdrant.py:163:        logger.info(f\"MyQdrantClient initializing with vector_size: {vector_size}. EmbeddingGenerator: {type(self.embedding_generator).__name__}\")\n/app/src/clients/qdrant.py:173:            logger.info(f\"Successfully connected to Qdrant at {_qdrant_host}:{_qdrant_port}\")\n/app/src/clients/qdrant.py:186:                logger.info(f\"Creating collection '{self.collection_name}' with vector size {self.vector_size}...\")\n/app/src/clients/qdrant.py:191:                logger.info(f\"Collection '{self.collection_name}' created.\")\n/app/src/clients/qdrant.py:193:                logger.info(f\"Collection '{self.collection_name}' already exists. Using existing collection.\")\n/app/src/clients/qdrant.py:197:            logger.info(f\"Ensuring 'datetime' payload index for 'timestamp' field in collection '{self.collection_name}'.\")\n/app/src/clients/qdrant.py:203:            logger.info(f\"Payload index for 'timestamp' (datetime type) ensured for collection '{self.collection_name}'.\")\n/app/src/clients/qdrant.py:207:                logger.info(f\"Creating collection '{self.functions_collection_name}' for MCP functions...\")\n/app/src/clients/qdrant.py:215:                logger.info(f\"Collection '{self.functions_collection_name}' created with indices.\")\n/app/src/clients/qdrant.py:217:                logger.info(f\"Collection '{self.functions_collection_name}' already exists.\")\n/app/src/clients/qdrant.py:221:                logger.info(f\"Creating collection '{self.user_analysis_collection_name}' for user psychological analysis...\")\n/app/src/clients/qdrant.py:231:                logger.info(f\"Collection '{self.user_analysis_collection_name}' created with indices.\")\n/app/src/clients/qdrant.py:233:                logger.info(f\"Collection '{self.user_analysis_collection_name}' already exists.\")\n/app/src/clients/qdrant.py:237:                logger.info(f\"Creating collection '{self.conversation_topics_collection_name}' for conversation topics...\")\n/app/src/clients/qdrant.py:248:                logger.info(f\"Collection '{self.conversation_topics_collection_name}' created with indices.\")\n/app/src/clients/qdrant.py:250:                logger.info(f\"Collection '{self.conversation_topics_collection_name}' already exists.\")\n/app/src/clients/qdrant.py:254:                logger.info(f\"Creating collection '{self.workflow_steps_collection_name}' for workflow steps...\")\n/app/src/clients/qdrant.py:265:                logger.info(f\"Collection '{self.workflow_steps_collection_name}' created with indices.\")\n/app/src/clients/qdrant.py:267:                logger.info(f\"Collection '{self.workflow_steps_collection_name}' already exists.\")\n/app/src/clients/qdrant.py:291:        logger.info(\"IN QDRANT SEND MESSAGE\")\n/app/src/clients/qdrant.py:330:            logger.info(f\"Stored message from '{username}' (assistant: {assistant_id}, convo: {conversation_id}): {message[:100]}...\")\n/app/src/clients/qdrant.py:338:        logger.info(\"IN QDRANT GET MESSAGE\")\n/app/src/clients/qdrant.py:360:        logger.info(f\"Qdrant get_messages: Constructed filter: {filter_obj.model_dump_json() if filter_obj else 'None'}\")\n/app/src/clients/qdrant.py:398:        logger.info(f\"Qdrant get_messages: Fetched {len(all_points)} raw points from Qdrant before applying limit.\")\n/app/src/clients/qdrant.py:417:        logger.info(f\"Qdrant get_messages: Processing {len(final_points_to_return)} points after limit of {limit}.\")\n/app/src/clients/qdrant.py:514:        logger.info(f\"Qdrant get_messages: Returning {len(conversations_list)} conversations for sender='{sender}', conversation_id='{conversation_id}', assistant_id='{assistant_id}'.\")\n/app/src/clients/qdrant.py:561:            # logger.info(f\"Upserted MCP function schema for '{name}' into '{self.functions_collection_name}'.\")\n/app/src/clients/qdrant.py:570:            logger.info(\"MyQdrantClient.search_mcp_function_schemas: query_text is empty, returning empty list.\")\n/app/src/clients/qdrant.py:573:        logger.info(f\"MyQdrantClient.search_mcp_function_schemas: Generating embedding for query: '{query_text[:100]}...'\")\n/app/src/clients/qdrant.py:592:            logger.info(f\"MyQdrantClient.search_mcp_function_schemas: Raw search_results count from Qdrant: {len(search_results)}\")\n/app/src/clients/qdrant.py:607:            logger.info(f\"MyQdrantClient.search_mcp_function_schemas: Returning {len(results_with_scores)} results with scores after filtering invalid content.\")\n/app/src/clients/qdrant.py:654:            logger.info(f\"Stored user psychological analysis for user '{username}', convo '{conversation_id}' in '{self.user_analysis_collection_name}'.\")\n/app/src/clients/qdrant.py:686:            logger.info(f\"Successfully set topic in Qdrant for conversation {conversation_id}: {topic}\")\n/app/src/clients/qdrant.py:710:                logger.info(f\"Retrieved topic from Qdrant for conversation {conversation_id}: {topic}\")\n/app/src/clients/qdrant.py:712:            logger.info(f\"No topic found in Qdrant for conversation {conversation_id}\")\n/app/src/clients/types.py:76:        logger.info(\n/app/src/clients/types.py:92:            logger.info(f\"Sending to LLM: {messages}\")\n/app/src/clients/neoj.py:64:            logger.info(f\"Successfully connected to Neo4j at {self._uri}\")\n/app/src/clients/neoj.py:99:            logger.info(\"Neo4j schema initialized successfully\")\n/app/src/clients/neoj.py:109:            logger.info(\"Neo4j driver closed\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:25:    logger.info(\n/app/src/clients/qdrant_utils/dynamic_system_message.py:52:                        logger.info(f\"generate_dynamic_system_message: Filtering out 'Workflow execution failed' message from history for dynamic prompt context.\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:62:                    logger.info(f\"generate_dynamic_system_message: Using recent history for prompt (first 200 chars): {recent_history_str[:200]}...\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:64:                    logger.info(\"generate_dynamic_system_message: Formatted history parts were empty.\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:104:    logger.info(f\"generate_dynamic_system_message: Generation prompt for LLM: {json.dumps(generation_prompt_messages, indent=2)}\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:107:        logger.info(\"generate_dynamic_system_message: Calling LLM to generate dynamic system message.\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:116:                logger.info(f\"generate_dynamic_system_message: Generated and escaped dynamic message: '{dynamic_system_msg}'\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:124:        logger.info(\"generate_dynamic_system_message: Falling back to base system message due to LLM call error.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:36:    logger.info(\n/app/src/clients/qdrant_utils/summarize_conv_history.py:61:            logger.info(f\"summarize_conversation_history: Retrieved {len(history_messages_for_prompt)} messages.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:65:        logger.info(\"summarize_conversation_history: No conversation history found.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:69:        logger.info(\"summarize_conversation_history: Extracted message list for prompt is empty.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:78:    logger.info(f\"summarize_conversation_history: Sending {len(llm_messages)} messages to LLM.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:84:            logger.info(f\"summarize_conversation_history: LLM generated summary. Length: {len(summary)}\")\n/app/src/clients/qdrant_utils/get_similar_messages.py:10:    logger.info(\"IN QDRANT SIMILAR MESSAGE\")\n/app/src/clients/qdrant_utils/get_conv_history.py:49:                            logger.info(\"Filtering 'Workflow execution failed' from Qdrant history.\")\n/app/src/clients/qdrant_utils/get_conv_history.py:55:                logger.info(f\"Fetched {len(history_messages)} messages from Qdrant for RAG context.\")\n/app/src/clients/qdrant_utils/get_conv_history.py:57:                logger.info(f\"No conversation history found in Qdrant for user '{username}', convo_id '{conversation_id}'.\")\n/app/src/clients/qdrant_utils/get_conv_history.py:62:        logger.info(\"Username or conversation_id missing, skipping Qdrant history fetch.\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:21:    logger.info(f\"get_relevant_tool_objects_from_qdrant: Fetching for query: '{query_text[:50]}...' (k={k})\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:30:        logger.info(\"get_relevant_tool_objects_from_qdrant: No relevant function schemas found in Qdrant.\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:37:    logger.info(\"get_relevant_tool_objects_from_qdrant: Listing all found tool candidates and their Qdrant scores:\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:97:            logger.info(f\"get_relevant_tool_objects_from_qdrant: Reconstructed Tool '{name}' with Qdrant score: {score:.4f} ({score*100:.2f}%)\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:99:            logger.info(f\"get_relevant_tool_objects_from_qdrant: Reconstructed Tool '{name}' (score not available from Qdrant item)\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:117:            logger.info(f\"get_relevant_tool_objects_from_qdrant: Filesystem intent detected, returning only Filesystem tools: {[t.name for t in fs_tools]}\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:120:            logger.info(\"get_relevant_tool_objects_from_qdrant: Filesystem intent detected, but no Filesystem tools found in Qdrant results.\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:123:    logger.info(f\"get_relevant_tool_objects_from_qdrant: Reconstructed {len(all_tool_objects)} relevant Tool objects from Qdrant.\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:128:        logger.info(f\"get_relevant_tool_objects_from_qdrant: Statistics for {len(all_tool_objects)} reconstructed tools - Min Score: {min_score:.4f}, Max Score: {max_score:.4f}, Avg Score: {avg_score:.4f}\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:130:        logger.info(f\"get_relevant_tool_objects_from_qdrant: Reconstructed {len(all_tool_objects)} tools, but no scores were available for statistics.\")\n/app/src/clients/qdrant_utils/code_search.py:49:        logger.info(f\"Collection '{collection_name}' already exists.\")\n/app/src/clients/qdrant_utils/code_search.py:51:        logger.info(f\"Collection '{collection_name}' not found. Creating...\")\n/app/src/clients/qdrant_utils/code_search.py:60:        logger.info(f\"Collection '{collection_name}' created.\")\n/app/src/clients/qdrant_utils/code_search.py:69:    logger.info(f\"Discovered {len(code_files)} code files.\")\n/app/src/clients/qdrant_utils/code_search.py:122:    logger.info(f\"Processing file: {file_path}\")\n/app/src/clients/qdrant_utils/code_search.py:187:            logger.info(f\"Indexed {len(points_to_upsert)} chunks from {file_path}\")\n/app/src/clients/qdrant_utils/code_search.py:216:    logger.info(\"Codebase indexing complete.\")\n/app/src/clients/qdrant_utils/code_search.py:228:    logger.info(f\"\\nSearching for: '{query}'\")\n/app/src/clients/qdrant_utils/code_search.py:305:    logger.info(\"\\n--- Example Searches ---\")\n/app/src/clients/qdrant_utils/code_search.py:323:            logger.info(f\"Top results for '{q}':\")\n/app/src/clients/qdrant_utils/code_search.py:325:                logger.info(f\"  {res_idx+1}. Score: {res['score']:.4f}, File: {res.get('file_path', 'N/A')}, Identifier: {res.get('identifier', 'N/A')}\")\n/app/src/clients/qdrant_utils/code_search.py:326:                logger.info(f\"     Code Snippet (first 80 chars): {res.get('code_chunk', '')[:80].replace(os.linesep, ' ')}...\")\n/app/src/clients/qdrant_utils/code_search.py:328:            logger.info(f\"No results found for '{q}'.\")\n/app/src/clients/qdrant_utils/code_search.py:329:        logger.info(\"-\" * 20)\n/app/src/clients/qdrant_utils/code_search.py:334:        logger.info(f\"\\nCollection '{CODEBASE_COLLECTION_NAME}' info: Points count = {collection_info.points_count}\")\n/app/src/clients/mcpClient.py:30:        logger.info(f\"ChatSession initialized. Managing {len(self.servers)} servers.\")\n/app/src/clients/mcpClient.py:39:        #logger.info(f\"ChatSession: Initializing {len(self.servers)} MCP servers...\")\n/app/src/clients/mcpClient.py:52:        #logger.info(f\"ChatSession: Finished server initialization. {initialized_count}/{len(self.servers)} servers initialized.\")\n/app/src/clients/mcpClient.py:68:            #logger.info(\"ChatSession.cleanup_servers: No servers to clean up.\")\n/app/src/clients/mcpClient.py:71:        logger.info(f\"ChatSession: Cleaning up {len(self.servers)} MCP servers...\")\n/app/src/clients/mcpClient.py:74:        #logger.info(\"ChatSession: Finished server cleanup.\")\n/app/src/clients/mcpClient.py:80:        #logger.info(\"MCPClient: Environment variables loaded.\")\n/app/src/clients/mcpClient.py:82:        logger.info(f'MCPClient: Server configuration loaded: {self.server_config}')\n/app/src/clients/mcpClient.py:88:        logger.info(\"MCPClient: Qdrant client initialized.\")\n/app/src/clients/mcpClient.py:90:        # logger.info(\"MCPClient: ChatSession initialized.\")\n/app/src/clients/mcpClient.py:96:        #logger.info(\"MCPClient.list_tools_from_all_servers: Starting to list tools.\")\n/app/src/clients/mcpClient.py:108:                logger.info(f\"MCPClient.list_tools_from_all_servers: Server '{server.name}' (Index {server_idx}) returned {len(tools_from_server)} tools: {[tool.name for tool in tools_from_server]}\")\n/app/src/clients/mcpClient.py:114:        logger.info(f\"MCPClient.list_tools_from_all_servers: Finished. Total tools collected: {len(all_tools)}.\")\n/app/src/clients/mcpClient.py:121:        #logger.info(\"MCPClient: Starting synchronization of tools to Qdrant.\")\n/app/src/clients/mcpClient.py:124:        #logger.info(f\"MCPClient.sync_tools_to_qdrant: Found {len(all_tools)} tools from all servers to potentially sync.\")\n/app/src/clients/mcpClient.py:140:            # logger.info(f\"MCPClient.sync_tools_to_qdrant: Attempted to add/update tool '{tool_obj.name}' to Qdrant.\")\n/app/src/clients/mcpClient.py:142:        #logger.info(f\"MCPClient: Finished synchronizing tools to Qdrant. Attempted to add/update {added_count} tools out of {len(all_tools)} found.\")\n/app/src/clients/mcpClient.py:152:                    logger.info(f\"Executing tool '{tool_name}' on server '{server.name}' with args: {args}\")\n/app/src/clients/mcpClient.py:192:        #logger.info(f\"LOAD: {file_path}\" )\n/app/src/clients/mcpClient.py:194:            #logger.info(f\"LOADED: {f}\" )\n/app/src/clients/mcpClient.py:225:            logger.info(f\"MCPServer {self.name}: Already initialized and session active. Skipping re-initialization.\")\n/app/src/clients/mcpClient.py:228:        logger.info(f\"MCPServer {self.name}: Starting initialization with config: {self.config}\")\n/app/src/clients/mcpClient.py:269:        logger.info(f\"MCPServer {self.name}: Entering _initialize_stdio with command_path: {command_path}\")\n/app/src/clients/mcpClient.py:293:            logger.info(f\"Initializing stdio server {self.name} with command: {command_path} and args: {self.config.get('args', [])}\")\n/app/src/clients/mcpClient.py:294:            # logger.info(f\"Server parameters: {server_params}\")\n/app/src/clients/mcpClient.py:298:            logger.info(f\"MCPServer {self.name}: stdio_client transport established. Read: {read}, Write: {write}\")\n/app/src/clients/mcpClient.py:299:            logger.info(f\"MCPServer {self.name}: Note: Subprocess stderr for '{self.name}' is captured and logged by the 'mcp.client.stdio' logger.\")\n/app/src/clients/mcpClient.py:302:            #logger.info(f\"MCPServer {self.name}: ClientSession context entered. About to call session.initialize().\")\n/app/src/clients/mcpClient.py:304:            #logger.info(f\"MCPServer {self.name}: session.initialize() returned. Verifying session with list_tools...\")\n/app/src/clients/mcpClient.py:308:                # logger.info(f\"MCPServer {self.name}: Session verified successfully with list_tools.\")\n/app/src/clients/mcpClient.py:314:            logger.info(f\"MCPServer {self.name}: Finished _initialize_stdio successfully.\")\n/app/src/clients/mcpClient.py:332:            logger.info(f\"Starting Docker container for server {self.name}: {' '.join(docker_command_parts)}\")\n/app/src/clients/mcpClient.py:341:            logger.info(f\"Docker container {container_name} for server {self.name} started (PID: {process.pid}). Further interaction depends on MCP setup.\")\n/app/src/clients/mcpClient.py:360:            logger.info(f\"Checking HTTP server {self.name} at {url}\")\n/app/src/clients/mcpClient.py:364:            logger.info(f\"HTTP server {self.name} is responsive at {url}.\")\n/app/src/clients/mcpClient.py:385:        logger.info(f\"Listing tools for server {self.name}\")\n/app/src/clients/mcpClient.py:393:            # logger.info(f\"RAW TOOLS : {tools_response}\") # Potentially very verbose\n/app/src/clients/mcpClient.py:409:            logger.info(f\"Successfully listed {len(processed_tools)} tools for server {self.name}.\")\n/app/src/clients/mcpClient.py:453:                logger.info(f\"Executing {tool_name} on server {self.name} (Attempt {attempt_num}/{total_attempts}). Args: {arguments}\")\n/app/src/clients/mcpClient.py:455:                logger.info(f\"Tool {tool_name} executed successfully on server {self.name}.\")\n/app/src/clients/mcpClient.py:475:                    logger.info(f\"Retrying in {delay} seconds...\")\n/app/src/clients/mcpClient.py:491:                    logger.info(f\"Server {self.name}: Session is None and AsyncExitStack has no (or None) callbacks. Assuming already cleaned or not fully initialized.\")\n/app/src/clients/mcpClient.py:495:                    logger.info(f\"Server {self.name}: Session is None, but AsyncExitStack has callbacks. Proceeding with aclose().\")\n/app/src/clients/mcpClient.py:497:            logger.info(f\"MCPServer {self.name}: Starting cleanup. Current session: {self.session is not None}. Exit stack has callbacks: {hasattr(self.exit_stack, '_exit_callbacks') and bool(self.exit_stack._exit_callbacks)}\")\n/app/src/clients/mcpClient.py:501:                logger.info(f\"MCPServer {self.name}: AsyncExitStack.aclose() completed.\")\n/app/src/clients/mcpClient.py:515:                logger.info(f\"MCPServer {self.name}: Cleanup finalized. Session set to None, exit_stack reset.\")\n/app/src/interfaces/BaseService.py:219:             logger.info(\n/app/src/interfaces/Assistant.py:48:        logger.info(f\"CREATE ASSISTANT WITH : {username, assistant_id, language}\")\n/app/src/interfaces/Assistant.py:75:            logger.info(f\"[User] Initialized with username: {username}, system message: {preview}\")\n/app/src/interfaces/Assistant.py:89:            logger.info(f\"constructing user with : {es_system_message}\")\n/app/src/interfaces/Assistant.py:120:            logger.info(f\"[Assistant.chat] Method called. User: {current_username}, Assistant ID: {current_assistant_id}, Conversation ID: {conversation_id}, Message: '{message[:50]}...'\")\n/app/src/interfaces/Assistant.py:124:            logger.info(f\"[Assistant.chat] Fetched assistant_data: {assistant_data}\")\n/app/src/interfaces/Assistant.py:129:            logger.info(f\"[Assistant.chat] Workflow ID for assistant '{current_assistant_id}': {workflow_id}\")\n/app/src/interfaces/Assistant.py:140:                    logger.info(f\"[Assistant.chat] Fetched {len(history_messages_neo4j) if history_messages_neo4j else 0} history messages from Neo4j for workflow.\")\n/app/src/interfaces/Assistant.py:144:                logger.info(f\"[Assistant] Using workflow '{workflow_id}' for response for assistant '{current_assistant_id}'\")\n/app/src/interfaces/Assistant.py:156:                logger.info(f\"[Assistant.chat] Fetched {len(history_messages_qdrant) if history_messages_qdrant else 0} history messages from Qdrant for workflow.\")\n/app/src/interfaces/Assistant.py:194:                logger.info(f\"[Assistant.chat] Executing workflow '{workflow_id}' with input: {json.dumps(workflow_input_data, default=str, indent=2)}\")\n/app/src/interfaces/Assistant.py:199:                logger.info(f\"[Assistant.chat] Workflow '{workflow_id}' execution result object: {workflow_result_obj}\")\n/app/src/interfaces/Assistant.py:216:                logger.info(f\"[Assistant.chat] Workflow '{workflow_id}' processed. Derived final_response_content: '{str(final_response_content)[:100]}...'\")\n/app/src/interfaces/Assistant.py:221:                logger.info(f\"[Assistant.chat] final_response_content is not None. Saving to Qdrant: '{str(final_response_content)[:100]}...'\")\n/app/src/interfaces/Assistant.py:252:                        logger.info(f\"[Assistant.chat] Stored messages in Neo4j for conversation: {conversation_id}\")\n/app/src/interfaces/Assistant.py:295:            logger.info(f\"Generated topic for conversation: '{topic}'\")\n/app/src/interfaces/Assistant.py:303:        logger.info(f\"[User] Looking for initial conversation ID for user: {username}\")\n/app/src/interfaces/Assistant.py:309:                logger.info(f\"[User] Found existing conversation ID: {existing_id}\")\n/app/src/interfaces/Assistant.py:320:                        logger.info(f\"[User] Ensured conversation exists in Neo4j: {existing_id}\")\n/app/src/interfaces/Assistant.py:328:            logger.info(f\"[User] Creating initial conversation ID: {new_conversation_id}\")\n/app/src/interfaces/Assistant.py:339:                    logger.info(f\"[User] Created conversation in Neo4j: {new_conversation_id}\")\n/app/src/interfaces/Assistant.py:346:            logger.info(f\"[User] Error in get_or_create_initial_conversation_id: {e}\")\n/app/src/interfaces/Assistant.py:351:        logger.info(f\"Getting context for {self._username}\")\n/app/src/interfaces/Assistant.py:362:        logger.info(f\"Making train data. Current context type: {type(self._context)}\")\n/app/src/interfaces/Assistant.py:363:        logger.info(f\"Context content: {self._context}\")\n/app/src/interfaces/Assistant.py:367:            logger.info(\"Context is empty!\")\n/app/src/interfaces/Assistant.py:378:            logger.info(f\"Unexpected context type: {type(self._context)}\")\n/app/src/interfaces/Assistant.py:394:                logger.info(f\"Created training pair: {pair_data}\")\n/app/src/interfaces/Assistant.py:397:        logger.info(f\"Final training data count: {len(training_data)}\")\n/app/src/interfaces/Assistant.py:404:        logger.info(\"Preparing training data from conversation context...\")\n/app/src/interfaces/Assistant.py:406:        logger.info(f\"Final training data count: {len(training_data)}\")\n/app/src/interfaces/Assistant.py:413:                logger.info(f\"Invalid message format: {message}. Each message should be a dictionary.\")\n/app/src/interfaces/Assistant.py:416:                logger.info(f\"Message missing 'role' or 'content': {message}\")\n/app/src/scraper/scrape_all_video_urls.py:115:        logger.info(\"Bot initialized.\")\n/app/src/scraper/scrape_all_video_urls.py:130:                    logger.info(\"Clicked 'Allow all cookies' button.\")\n/app/src/utils/tool_utils.py:28:        logger.info(f\"Added {len(dynamically_fetched_tools)} dynamically fetched tools.\")\n/app/src/utils/tool_utils.py:38:        logger.info(f\"Added {num_static_added} unique static tools.\")\n/app/src/utils/request_id_manager.py:58:                logger.info(f\"[RequestIDManager] Cleaned up all requests for SID {sid}\")\n/app/src/utils/request_id_manager.py:96:        logger.info(f\"[AUTH] Setting session for SID: {sid}\")\n/app/src/utils/request_id_manager.py:102:        logger.info(f\"[AUTH] Removing session for SID: {sid}\")\n/app/src/utils/request_id_manager.py:105:            logger.info(f\"[AUTH] Session cleared for SID: {sid}\")\n/app/src/utils/ssl_utils.py:28:            logger.info(f\"Successfully loaded certificate from {cert_file}\")\n/app/src/routes/http/files.py:26:        logger.info(f\"READ PATH : {requested_path}\")\n/app/src/routes/socket/assistant_events.py:34:    logger.info(\"🚀 [SOCKET] Initializing assistant routes\")\n/app/src/routes/socket/assistant_events.py:39:        logger.info(\"====== Get Assistants Request Started ======\")\n/app/src/routes/socket/assistant_events.py:69:        logger.info(f\"====== Get Assistant Request Started ====== {data}\", )\n/app/src/routes/socket/assistant_events.py:122:            logger.info(f\"[CREATE_ASSISTANT] Formatted assistant data: {assistant_data}\")\n/app/src/routes/socket/assistant_events.py:156:            logger.info(f\"[CREATE_ASSISTANT] Successfully created assistant {assistant_data}\")\n/app/src/routes/socket/assistant_events.py:176:            logger.info(\"====== Get Assistant System Message Request Started ======\")\n/app/src/routes/socket/assistant_events.py:178:            logger.info(f\"Getting system message for assistant_id: {assistant_id}\")\n/app/src/routes/socket/assistant_events.py:188:            logger.info(f\"Retrieved system message: {system_message}\")\n/app/src/routes/socket/assistant_events.py:197:            logger.info(f\"Sending system message for {assistant_id}: {system_message}\")\n/app/src/routes/socket/assistant_events.py:230:            logger.info(f\"[UPDATE_ASSISTANT] Updating assistant {assistant_id} with data: {updates}\")\n/app/src/routes/socket/auth_events.py:17:    logger.info(\"🚀 [AUTH_ROUTES] Initializing authentication event routes...\")\n/app/src/routes/socket/auth_events.py:22:        logger.info(f\"🏁 [AUTH_ROUTES] Entered 'connect' handler for SID: {sid}\")\n/app/src/routes/socket/auth_events.py:48:        logger.info(f\"[AUTH_ROUTES] Client connected. SID: {sid}\")\n/app/src/routes/socket/auth_events.py:72:            logger.info(f\"[AUTH_ROUTES] Token refreshed for user: {token_info.get('preferred_username')} with SID: {sid}\")\n/app/src/routes/socket/auth_events.py:81:        logger.info(f\"🏁 [AUTH_ROUTES] Entered 'authenticate' handler for SID: {sid}\")\n/app/src/routes/socket/auth_events.py:104:            logger.info(f\"[AUTH_ROUTES] Client authenticated. SID: {sid}\")\n/app/src/routes/socket/auth_events.py:113:        logger.info(f\"[AUTH_ROUTES] Client disconnected. SID: {sid}\")\n/app/src/routes/socket/auth_events.py:127:        logger.info(f\"[AUTH_ROUTES] Ping received from {sid}\")\n/app/src/routes/socket/auth_events.py:137:        logger.info(f\"[AUTH_ROUTES] Client attempting to reconnect. SID: {sid}\")\n/app/src/routes/socket/auth_events.py:153:            logger.info(f\"[AUTH_ROUTES] Client reconnected. SID: {sid}\")\n/app/src/routes/socket/system_events.py:69:            logger.info(\"====== Get System Message History Request Started ======\")\n/app/src/routes/socket/system_events.py:73:            logger.info(\"Fetching system message changes...\")\n/app/src/routes/socket/system_events.py:76:            logger.info(f\"Retrieved changes: {json.dumps(changes, indent=2)}\")\n/app/src/routes/socket/system_events.py:96:            logger.info(f\"Processing updateSystemMessage data: {data}\")\n/app/src/routes/socket/system_events.py:120:                logger.info(f\"Successfully stored system message for {assistant_id}\")\n/app/src/routes/socket/system_events.py:138:        logger.info(\"====== Update Settings Request Started ======\")\n/app/src/routes/socket/system_events.py:139:        logger.info(f\"Received data: {data}\")\n/app/src/routes/socket/system_events.py:146:            logger.info(f\"Using username: {username}, assistant_id: {assistant_id}\")\n/app/src/routes/socket/system_events.py:153:            logger.info(f\"Retrieving system message for {assistant_id}\")\n/app/src/routes/socket/system_events.py:169:            logger.info(f\"Sending settings update for {assistant_id}\")\n/app/src/routes/socket/workflow_events.py:17:            logger.info(\"Received request to save workflow\")\n/app/src/routes/socket/workflow_events.py:32:            logger.info(f\"Workflow saved successfully with ID: {result['workflow_id']}\")\n/app/src/routes/socket/workflow_events.py:56:            logger.info(\"Received request for workflows\")\n/app/src/routes/socket/workflow_events.py:65:            logger.info(f\"Returning {len(workflows)} workflows\")\n/app/src/routes/socket/workflow_events.py:86:            logger.info(f\"Received request for workflow: {data.get('workflowId')}\")\n/app/src/routes/socket/workflow_events.py:113:            logger.info(f\"Returning workflow: {workflow_id}\")\n/app/src/routes/socket/workflow_events.py:134:            logger.info(f\"Received request to execute workflow: {data.get('workflowId')}\")\n/app/src/routes/socket/workflow_events.py:171:            logger.info(f\"Executing workflow: {workflow_id}\")\n/app/src/routes/socket/workflow_events.py:188:                logger.info(f\"Workflow execution result: {result}\")\n/app/src/routes/socket/workflow_events.py:218:            logger.info(f\"Received request to delete workflow: {data.get('workflowId')}\")\n/app/src/routes/socket/workflow_events.py:245:            logger.info(f\"Workflow deleted: {workflow_id}\")\n/app/src/routes/socket/service_events.py:15:            logger.info(\"Received request for services\")\n/app/src/routes/socket/service_events.py:22:            logger.info(f\"Returning {len(services)} services\")\n/app/src/routes/socket/service_events.py:43:            logger.info(f\"Received request to execute service: {data.get('serviceId')}\")\n/app/src/routes/socket/service_events.py:60:                    logger.info(f\"Service {service_id} executed successfully in background.\")\n/app/src/routes/socket/user_events.py:32:        logger.info(user)\n/app/src/routes/socket/user_events.py:37:        logger.info(user_groups)\n/app/src/routes/socket/user_events.py:38:        logger.info(user_roles)\n/app/src/routes/socket/user_events.py:40:        logger.info(f'Attributes: {attributes}')\n/app/src/routes/socket/user_events.py:55:        logger.info(f\"ASSISTANTS RENDER : {assistants[0].split(',')}\")\n/app/src/routes/socket/user_events.py:99:            logger.info(f\"RAW USERS : {users}\")\n/app/src/routes/socket/user_events.py:114:                    logger.info(f\"[USERS] Processed user: {transformed_user['username']}\")\n/app/src/routes/socket/user_events.py:116:            logger.info(f\"[USERS] Successfully processed {len(transformed_users)} users\")\n/app/src/routes/socket/user_events.py:211:        logger.info(id)\n/app/src/routes/socket/user_events.py:212:        logger.info(assistants)\n/app/src/routes/socket/user_events.py:213:        logger.info(f\"IN CUSTOM ATT: {data}\" )\n/app/src/routes/socket/user_events.py:217:        logger.info(user)\n/app/src/routes/socket/user_events.py:224:        logger.info(list)\n/app/src/routes/socket/user_events.py:228:        logger.info(list)\n/app/src/routes/socket/user_events.py:234:            logger.info(f\"Successfully updated {joint_list} for user {id}\")\n/app/src/routes/socket/user_events.py:237:            logger.info(\"Failed to update user attribute:\", e)\n/app/src/routes/socket/user_events.py:247:        logger.info(id)\n/app/src/routes/socket/user_events.py:248:        logger.info(assistant)\n/app/src/routes/socket/user_events.py:249:        logger.info(f\"IN UNASSIGN ATT: {data}\" ) # Corrected log message\n/app/src/routes/socket/user_events.py:260:        logger.info(list)\n/app/src/routes/socket/user_events.py:264:        logger.info(list)\n/app/src/routes/socket/user_events.py:270:            logger.info(f\"Successfully updated '{joint_list}' for user {id}\")\n/app/src/routes/socket/user_events.py:273:            logger.info(\"Failed to update user attribute:\", e)\n/app/src/routes/socket/model_events.py:60:        logger.info(f\"[MODEL] Model {model_id} set for assistant {assistant_id}\")\n/app/src/routes/socket/model_events.py:79:    logger.info(f\"Received deleteModel request: {data}\")\n/app/src/routes/socket/model_events.py:101:            logger.info(f\"[MODEL_DELETE] Successfully deleted model: {model_id_to_delete}\")\n/app/src/routes/socket/model_events.py:127:    logger.info(\"Received getModels request\")\n/app/src/routes/socket/model_events.py:159:        logger.info(f\"[MODEL] Base models prepared: {len(base_models)} models\")\n/app/src/routes/socket/model_events.py:163:            logger.info(\"[MODEL] Attempting to fetch custom models from OpenAI\")\n/app/src/routes/socket/model_events.py:170:            logger.info(\"[MODEL] Calling OpenAI API to list fine-tuned models\")\n/app/src/routes/socket/model_events.py:187:            logger.info(f\"[MODEL] Total models prepared: {len(all_models)} ({len(base_models)} base, {len(trained_models)} custom)\")\n/app/src/routes/socket/model_events.py:193:            logger.info(\"[MODEL] Falling back to base models only due to error\")\n/app/src/routes/socket/model_events.py:227:        logger.info(f\"[MODEL] Current model for assistant {assistant_id}: {current_model}\")\n/app/src/routes/socket/socket_server.py:23:        #     logger.info(f\"[SOCKET_SERVER] Client connected. SID: {sid}\")\n/app/src/routes/socket/socket_server.py:29:        #         logger.info(f\"[SOCKET_SERVER] Client disconnected. SID: {sid}\")\n/app/src/routes/socket/socket_server.py:49:                    logger.info(f\"[SOCKET_SERVER] IN HANDLE EVENT: {data}\")\n/app/src/routes/socket/socket_server.py:74:                    logger.info(f\"SocketServer: Handler for '{event_name}' (SID: {sid}) executed. Result type: {type(result)}\")\n/app/src/routes/socket/socket_server.py:100:            # logger.info(f\"[SOCKET_SERVER] Registered event: {event_name}\")\n/app/src/routes/socket/socket_server.py:109:        logger.info(f\"[SOCKET_SERVER] Re-registering event handlers... {self.event_handlers.keys()}\")\n/app/src/routes/socket/socket_server.py:112:            logger.info(f\"[SOCKET_SERVER] Re-registered handler for event: {event_name}\")\n/app/src/routes/socket/socket_server.py:120:                logger.info(f\"[SOCKET_SERVER] IN Emitting: {data}\")\n/app/src/routes/socket/socket_server.py:138:                logger.info(f\"[SOCKET_SERVER] Emitting getServices: {response}\")\n/app/src/routes/socket/function_events.py:17:            logger.info(\"Received request for functions\")\n/app/src/routes/socket/function_events.py:27:            logger.info(f\"Returning {len(functions_data)} functions\")\n/app/src/routes/socket/function_events.py:52:            logger.info(f\"Received request to execute function with query: {function_query}\")\n/app/src/routes/socket/function_events.py:81:                    logger.info(f\"Function '{function_query}' executed in background. Result: {result_obj.message}\")\n/app/src/routes/socket/chat_events.py:48:            logger.info(f\"[CHAT] Received message request for SID: {sid} with data: {data}\")\n/app/src/routes/socket/chat_events.py:78:            #     logger.info(\"[CHAT] No conversation_id provided, creating new conversation\")\n/app/src/routes/socket/chat_events.py:82:            logger.info(f\"[CHAT_HANDLER] Calling assistant.chat for SID: {sid}, User: {username}, Assistant: {assistant_id}, Convo: {conversation_id}, Msg ID: {message_id}\")\n/app/src/routes/socket/chat_events.py:89:            logger.info(f\"[CHAT_HANDLER] Raw response from assistant.chat for SID {sid}: '{ai_response_content}' (Type: {type(ai_response_content)})\")\n/app/src/routes/socket/chat_events.py:109:                    logger.info(f\"Conversation {conversation_id} has {len(messages_history)} messages. Checking for topic generation.\")\n/app/src/routes/socket/chat_events.py:112:                        logger.info(f\"No existing topic for {conversation_id}. Generating new topic.\")\n/app/src/routes/socket/chat_events.py:116:                            logger.info(f\"Successfully generated and stored topic '{new_topic}' for conversation {conversation_id}\")\n/app/src/routes/socket/chat_events.py:118:                        logger.info(f\"Topic already exists for conversation {conversation_id} in Qdrant: '{existing_topic}'\")\n/app/src/routes/socket/chat_events.py:137:            logger.info(f\"[CHAT_HANDLER] Prepared data for emission to SID {sid}: {json.dumps(final_return_for_socket_server, indent=2)}\")\n/app/src/routes/socket/chat_events.py:152:            logger.info(f\"[DEBUG] Received loadInitialMessages request with data: {data}\")\n/app/src/routes/socket/chat_events.py:158:            logger.info(f\"[CHAT] Loading initial messages for user: {username}, assistant: {assistant_id}, conversation: {conversation_id}\")\n/app/src/routes/socket/chat_events.py:183:            logger.info(f\"QDRANT : {qdrantMessages}\")\n/app/src/routes/socket/chat_events.py:209:            logger.info((f\"NEW CONVERSATION : {username} {assistant_id}\"))\n/app/src/routes/socket/chat_events.py:236:        logger.info(f\"[CHAT] Received getChatHistory request with data: {data}\")\n/app/src/routes/socket/chat_events.py:252:            logger.info(f\"MESSAGES {msg_qdr}\")\n/app/src/routes/socket/chat_events.py:284:            logger.info(f\"[DELETE] Attempting to delete chat with ID: {chat_id}\")\n/app/src/routes/socket/chat_events.py:299:                logger.info(f\"[DELETE] Deleted {result.get('deleted', 0)} messages\")\n/app/src/routes/socket/chat_events.py:312:                    logger.info(f\"[DELETE] Deleted assistant {chat_id} from assistants index\")\n/app/src/routes/socket/chat_events.py:333:        logger.info(f\"[CHAT] Received delete conversation request with data: {json.dumps(data, indent=2)}\")\n/app/src/routes/socket/chat_events.py:347:            logger.info(f\"[CHAT] Delete operation completed. Success: {success}\")\n/app/src/routes/socket/chat_events.py:371:            logger.info(f\"[Assistants] Received request for all assistants from {request_id}\")\n/app/src/routes/socket/chat_events.py:388:            logger.info(f\"[Assistants] Found assistants: {assistant_data}\")\n/app/src/routes/socket/avatar_routes.py:26:            logger.info(\"[AVATAR] Starting avatar upload process\")\n/app/src/routes/socket/avatar_routes.py:70:                logger.info(f\"[AVATAR] Successfully saved avatar for user: {username}\")\n/app/src/routes/socket/avatar_routes.py:99:            logger.info(\"[AVATAR] Starting assistant avatar upload process\")\n/app/src/routes/socket/avatar_routes.py:128:                    logger.info(f\"[AVATAR] Successfully saved/updated assistant avatar for: {ai_user}\")\n/app/src/routes/socket/avatar_routes.py:181:                        logger.info(f\"[AVATAR] No assistant avatar found for {username}\")\n/app/src/routes/socket/avatar_routes.py:203:                        logger.info(f\"[AVATAR] No user avatar found for {username}\")\n/app/src/routes/socket/avatar_routes.py:238:            logger.info(f\"[AVATAR] Received batch avatar request for {len(usernames)} users\")\n/app/src/routes/socket/avatar_routes.py:291:                logger.info(f\"[AVATAR] Created index: {index}\")\n/app/src/elastic/core/connection.py:47:                    logger.info(\"Successfully connected to Elasticsearch\")\n/app/src/elastic/elastic.py:18:            logger.info(\"Using injected Elasticsearch client.\")\n/app/src/elastic/elastic.py:26:        logger.info(\"Initializing Elasticsearch connection\")\n/app/src/elastic/elastic.py:53:                    logger.info(\"Successfully connected to Elasticsearch\")\n/app/src/elastic/elastic.py:60:                logger.info(f\"Retrying in {retry_delay}s...\")\n/app/src/elastic/elastic.py:77:            logger.info(f\"[Elastic] Attempting to index document to '{index}'\")\n/app/src/elastic/elastic.py:85:            logger.info(f\"[Elastic] Successfully indexed document: {result}\")\n/app/src/elastic/elastic.py:119:            logger.info(f\"[Elastic] Attempting to retrieve avatar from '{index_name}' for user '{username}' using query: {json.dumps(query)}\")\n/app/src/elastic/elastic.py:124:                logger.info(f\"[Elastic] Avatar found for '{username}' in '{index_name}'.\")\n/app/src/elastic/elastic.py:127:                logger.info(f\"[Elastic] Avatar not found for '{username}' in '{index_name}'.\")\n/app/src/elastic/operations/assistants/Assistant.py:25:            logger.info(\"🤖 [ASSISTANTS] Fetching all assistants from Qdrant\")\n/app/src/elastic/operations/assistants/Assistant.py:31:            logger.info(f\"✅ [ASSISTANTS] Successfully fetched {len(assistants)} assistants from Qdrant\")\n/app/src/elastic/operations/assistants/Assistant.py:40:        logger.info(\"IN STORE ASSISTANT\")\n/app/src/elastic/operations/assistants/Assistant.py:45:                logger.info(f\"Assistant {assistant_data.get('id')} stored successfully in Qdrant\")\n/app/src/elastic/operations/assistants/Assistant.py:66:                logger.info(f\"[Qdrant] Found message by ID: {assistant_id} {message}\")\n/app/src/elastic/operations/assistants/Assistant.py:82:                logger.info(f\"[Qdrant] System message stored successfully for assistant: {assistant_id}\")\n/app/src/elastic/operations/assistants/Assistant.py:91:            logger.info(f\"[Qdrant] Starting model update for assistant {assistant_id} to model {model}\")\n/app/src/elastic/operations/assistants/Assistant.py:101:            logger.info(f\"[Qdrant] Current model: {assistant.get('model', 'not set')}\")\n/app/src/elastic/operations/assistants/Assistant.py:102:            logger.info(f\"[Qdrant] New model: {model}\")\n/app/src/elastic/operations/assistants/Assistant.py:112:                logger.info(f\"[Qdrant] Assistant model updated successfully\")\n/app/src/elastic/operations/assistants/Assistant.py:122:                logger.info(f\"[Elastic] Attempting to index document to '{index}'\")\n/app/src/elastic/operations/assistants/Assistant.py:123:                logger.info(f\"[Elastic] Document content: {json.dumps(document, indent=2)}\")\n/app/src/elastic/operations/assistants/Assistant.py:135:                logger.info(f\"[Elastic] Successfully indexed document to '{index}'\")\n/app/src/elastic/operations/assistants/Assistant.py:136:                logger.info(f\"[Elastic] Response: {json.dumps(response_dict, indent=2)}\")\n/app/src/elastic/operations/assistants/Assistant.py:146:            logger.info(f\"[Elastic] Getting chat history - username: {username}, assistant: {assistant_id}, conversationId: {conversation_id}\")\n/app/src/elastic/operations/assistants/Assistant.py:159:            logger.info(f\"GET CHAT HISTORY FOR : {username, assistant_id, conversation_id}\" )\n/app/src/elastic/operations/assistants/Assistant.py:176:                logger.info(f\"DEBUG GET History {source},{conv_id}\")\n/app/src/elastic/operations/assistants/Assistant.py:208:            logger.info(f\"Sort: {conversations}\" )\n/app/src/elastic/operations/assistants/Assistant.py:211:                logger.info (f\"CONV {conv}\")\n/app/src/elastic/operations/assistants/Assistant.py:215:            logger.info(f\"[Elastic] Returning {len(conversations)} conversations\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:61:            logger.info(f\"[Elastic] Setting system message for {assistant_id}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:62:            logger.info(f\"[Elastic] Connection info: {self.es.info()}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:68:            logger.info(f\"[Elastic] Index {index_name} exists: {index_exists}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:82:                logger.info(f\"[Elastic] Create index response: {create_response}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:99:                logger.info(f\"[Elastic] Index response: {index_response}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:107:                logger.info(f\"[Elastic] Refresh response: {refresh_response}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:118:                logger.info(f\"[Elastic] Get response: {get_response}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:166:            logger.info(f\"[Elastic] DEBUG: Inspecting index for assistant_id: {assistant_id}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:170:            logger.info(f\"[Elastic] Index {self.SYSTEM_MESSAGES_INDEX} exists: {exists}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:175:                logger.info(f\"[Elastic] Index info: {index_info}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:186:                logger.info(f\"[Elastic] Total documents: {result['hits']['total']['value']}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:187:                logger.info(f\"[Elastic] Sample documents: {result['hits']['hits']}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:201:                logger.info(f\"[Elastic] Documents for assistant_id {assistant_id}: {specific_result['hits']['hits']}\")\n/app/src/elastic/setup_indices.py:20:        #     logger.info(\"[Setup] No assistants found. Initializing default assistants...\")\n/app/src/elastic/setup_indices.py:40:        #             logger.info(f\"[Setup] Initialized default assistant: {default['id']}\")\n/app/src/elastic/setup_indices.py:46:        #     logger.info(\"[Setup] Assistants already exist in Elasticsearch\")\n/app/src/elastic/setup_indices.py:173:                logger.info(f\"[Setup] Creating {index_name} index\")\n/app/src/elastic/setup_indices.py:175:                logger.info(f\"[Setup] Created {index_name} index\")\n/app/src/elastic/setup_indices.py:181:                    logger.info(f\"[Setup] Updated mapping for existing index {index_name}\")\n/app/src/elastic/setup_indices.py:201:            logger.info(\"Created 'models' index\")\n/app/src/elastic/setup_indices.py:222:            logger.info(\"[Setup] Initializing default users...(not implemented)\")\n/app/src/elastic/setup_indices.py:225:        logger.info(\"[Setup] All indices created and verified successfully\")\n/app/src/elastic/setup_indices.py:260:            logger.info(\"[Setup] Created assistants index with mapping\")\n/app/src/elastic/setup_indices.py:264:            logger.info(\"[Setup] Updated assistants index mapping\")\n/app/src/elastic/init_elastic.py:13:    logger.info(\"[Main] Verifying Elasticsearch indices...\")\n/app/src/elastic/init_elastic.py:32:            logger.info(f\"[Main] Index '{index}' exists: {exists}\")\n"]]}, "timestamp": "2025-06-30T22:48:39.279561"}, {"role": "assistant", "event_type": "agent_llm_response", "llm_output": "\nInvoking: `search_code_patterns` with `{'query': 'logger.info', 'file_pattern': 'main.py'}`\n\n\n", "timestamp": "2025-06-30T22:48:39.933783"}, {"role": "assistant", "event_type": "agent_llm_call", "llm_input": {"input": "in /app/src/main.py?", "chat_history": ["content=\"Previous conversation summary: The user initially encountered errors with the assistant's workflow and requested information on a broad topic. They then asked for help locating logger.info operations in their code, but the assistant was unable to find relevant code snippets. The conversation continued with the user specifying the directory to search in, but ultimately ended with a workflow execution failure.\" additional_kwargs={} response_metadata={}", "content='Workflow execution failed: An unexpected error occurred with LangGraph agent.' additional_kwargs={} response_metadata={}", "content='You are a helpful assistant. The user seems to be referencing file paths frequently. Consider using the `search_files` tool to locate specific files or the `read_file` tool to view their contents.' additional_kwargs={} response_metadata={}"], "intermediate_steps": [["tool='search_code_patterns' tool_input={'query': 'logger.info', 'file_pattern': 'main.py'} log=\"\\nInvoking: `search_code_patterns` with `{'query': 'logger.info', 'file_pattern': 'main.py'}`\\n\\n\\n\" message_log=[AIMessage(content='', additional_kwargs={'function_call': {'arguments': '{\"query\":\"logger.info\",\"file_pattern\":\"main.py\"}', 'name': 'search_code_patterns'}}, response_metadata={'token_usage': {'completion_tokens': 22, 'prompt_tokens': 757, 'total_tokens': 779, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-3.5-turbo', 'system_fingerprint': None, 'finish_reason': 'function_call', 'logprobs': None}, id='run--ed9df926-bdf0-4f9d-a6f5-fc0da9e81ce0-0')]", "Code search results for 'logger.info':\n/app/src/agent/code_agent_tools.py:116:                logger.info(\"ripgrep not available, falling back to grep\")\n/app/src/agent/post_process.py:6:    logger.info(f\"[Workflow] Postprocessing output: {state.get('output')}\")\n/app/src/agent/combine_conversation_history.py:23:            logger.info(f\"Retrieved {len(qdrant_history)} messages from Qdrant for conversation {conversation_id}\")\n/app/src/agent/combine_conversation_history.py:74:    logger.info(f\"Combined conversation history has {len(combined_history)} messages\")\n/app/src/agent/preprocess_node.py:8:    logger.info(f\"[Workflow] Preprocessing input: {state.get('input_text')}\")\n/app/src/agent/clever_retrieve_file_content.py:15:            logger.info(f\"[clever_retrieve_file_content] MCPClient read_file_content result: {result!r}\")\n/app/src/agent/clever_retrieve_file_content.py:53:        logger.info(f\"Filesystem MCP could not retrieve '{file_path}': {e}\")\n/app/src/agent/clever_retrieve_file_content.py:85:                        logger.info(f\"Neo4j file path '{neo4j_path}' not found in filesystem: {e2}\")\n/app/src/agent/clever_retrieve_file_content.py:87:        logger.info(f\"Neo4j lookup failed for '{file_path}': {e}\")\n/app/src/agent/clever_retrieve_file_content.py:113:        logger.info(f\"Qdrant lookup failed for '{file_path}': {e}\")\n/app/src/agent/context_management.py:37:            logger.info(f\"Added summary of older chat history to context.\")\n/app/src/agent/context_management.py:68:                    logger.info(f\"Adding graph context to agent: {graph_context[:100]}...\")\n/app/src/agent/code_agent_preprocessing.py:258:    logger.info(f\"Enhanced agent state for code task. Intent: {intent_analysis['primary_intent']}, \"\n/app/src/agent/get_relevant_langchain_tools.py:25:            logger.info(f\"LangChainService (id={id(self)}): Fetched {len(relevant_mcp_tools)} relevant MCP tools for query: '{current_user_message[:50]}...'\")\n/app/src/agent/get_relevant_langchain_tools.py:103:        logger.info(f\"LangChainService (id={id(self)}): Added {len(essential_tools)} essential code agent tools\")\n/app/src/agent/get_relevant_langchain_tools.py:109:        # logger.info(f\"LangChainService (id={id(self)}): Added {len(essential_qdrant_tools)} essential Qdrant tools\")\n/app/src/agent/get_relevant_langchain_tools.py:114:    logger.info(f\"LangChainService (id={id(self)}): Final list of {len(relevant_langchain_tools)} Langchain tools for this invocation: {[t.name for t in relevant_langchain_tools]}\")\n/app/src/agent/summarize_node.py:8:    logger.info(f\"[Workflow] Summarizing history if needed.\")\n/app/src/agent/summarize_node.py:36:                logger.info(f\"[Workflow] Added summary of older chat history to context.\")\n/app/src/agent/store_conversation.py:22:            logger.info(f\"{self.name}: Final Langchain interaction saved to Neo4j.\")\n/app/src/agent/store_conversation.py:37:            logger.info(f\"{self.name}: Final Langchain interaction saved to Qdrant.\")\n/app/src/main.py:94:    logger.info(\"Lifespan: Initializing application components in worker...\")\n/app/src/main.py:99:    logger.info(\"Lifespan: Initializing routes...\")\n/app/src/main.py:111:    logger.info(\"Lifespan: Application components and routes initialized.\")\n/app/src/main.py:115:    #     logger.info(\"Lifespan: Cleaning up MCP servers during application shutdown...\")\n/app/src/main.py:117:    #     logger.info(\"Lifespan: MCP servers cleanup completed.\")\n/app/src/main.py:171:logger.info(\"Initializing application components...\")\n/app/src/main.py:180:        logger.info(\"Creating SSL context...\")\n/app/src/main.py:181:        logger.info(f\"Certificate file: {args.cert}\")\n/app/src/main.py:182:        logger.info(f\"Key file: {args.key}\")\n/app/src/main.py:184:        logger.info(\"SSL context created successfully\")\n/app/src/main.py:196:    logger.info(\"Keycloak initialized successfully\")\n/app/src/main.py:204:logger.info(\"python-socketio AsyncServer (sio) initialized.\")\n/app/src/main.py:234:        logger.info(\"Initializing MCPClient and its components...\")\n/app/src/main.py:236:        logger.info(f\"Global MCPClient created. Its chat_session ID: {id(mcp_client.chat_session)}. Servers for this instance will NOT be initialized here.\")\n/app/src/main.py:238:        logger.info(\"Initializing service managers...\")\n/app/src/main.py:281:        logger.info(\"All async initializations in main_initialization completed.\")\n/app/src/main.py:304:    logger.info(\"Preparing to start Uvicorn server via run_server_task...\")\n/app/src/main.py:311:        logger.info(f\"Uvicorn will run with app_target (import string for reload): {app_target_for_uvicorn}\")\n/app/src/main.py:314:        logger.info(f\"Uvicorn will run with app_target (ASGI app object): {app_target_for_uvicorn}\")\n/app/src/main.py:322:        logger.info(\"Reload mode is ON. Calling create_and_run_server directly for Uvicorn reloader.\")\n/app/src/main.py:333:        logger.info(\"Reload mode is OFF. Calling create_and_run_server via anyio.to_thread.run_sync.\")\n/app/src/main.py:344:    logger.info(\"Uvicorn server task (run_server_task) finished.\")\n/app/src/main.py:352:        logger.info(\"Initializing Elasticsearch (synchronous part)...\")\n/app/src/main.py:355:        logger.info(\"Starting main_initialization (asynchronous part)...\")\n/app/src/main.py:357:        logger.info(\"Asynchronous initializations finished.\")\n/app/src/main.py:359:        logger.info(\"Starting server components...\")\n/app/src/main.py:363:            logger.info(\"Uvicorn server task started via run_server_task. Main async runner will now wait for shutdown signal.\")\n/app/src/main.py:365:            logger.info(\"Shutdown signal received, cancelling server task group.\")\n/app/src/main.py:373:        logger.info(\"main_async_runner finished.\")\n/app/src/main.py:376:    logger.info(f\"Signal {signum} received, initiating shutdown...\")\n/app/src/main.py:388:        logger.info(\"Application main_async_runner finished.\")\n/app/src/main.py:390:        logger.info(\"Application shutting down due to KeyboardInterrupt (main)...\")\n/app/src/main.py:397:        logger.info(f\"Application exiting with code {exit_code}.\")\n/app/src/services/utils/code_agent/phase3_implementation.py:47:    logger.info(f\"[Phase 3] Starting Implementation for plan: {generated_plan[:150]}...\")\n/app/src/services/utils/code_agent/phase3_implementation.py:75:        logger.info(f\"[Phase 3] Implementation Turn {i+1}/{max_implementation_turns}. Message count: {len(messages)}\")\n/app/src/services/utils/code_agent/phase3_implementation.py:83:                logger.info(f\"[Phase 3] Pruned messages for LLM call. Original: {len(messages)}, Sent: {len(messages_for_llm_call)}\")\n/app/src/services/utils/code_agent/phase3_implementation.py:100:                logger.info(f\"[Phase 3] LLM requested tool calls: {tool_calls}\")\n/app/src/services/utils/code_agent/phase3_implementation.py:132:                                logger.info(f\"[Phase 3] Recorded proposal for {file_path}. Notes: {notes}\")\n/app/src/services/utils/code_agent/phase3_implementation.py:172:                logger.info(f\"[Phase 3] LLM provided content (assumed to be progress or completion). Content: {response_content[:200]}...\")\n/app/src/services/utils/code_agent/phase3_implementation.py:215:    logger.info(f\"[Phase 3] Successfully completed implementation. Proposed changes: {len(current_proposed_changes)}\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:49:    logger.info(f\"[Phase 2] Starting Plan Generation for goal: {clarified_goal[:100]}...\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:59:        logger.info(f\"[Phase 2] Added {len(conversation_history)} history messages to planning prompt.\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:68:        logger.info(f\"[Phase 2] Planning Turn {i+1}/{max_planning_turns}. Message count: {len(messages)}\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:82:                logger.info(f\"[Phase 2] LLM requested tool calls: {tool_calls}\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:106:                logger.info(f\"[Phase 2] LLM provided content (assumed to be the plan). Content: {response_content[:200]}...\")\n/app/src/services/utils/code_agent/phase2_plan_generation.py:137:    logger.info(f\"[Phase 2] Successfully generated plan:\\n{generated_plan}\")\n/app/src/services/utils/code_agent/phase4_review.py:29:    logger.info(\"[Phase 4] Starting: Reviewing and Presenting Changes.\")\n/app/src/services/utils/code_agent/phase4_review.py:33:        logger.info(\"[Phase 4] No proposed changes to review.\")\n/app/src/services/utils/code_agent/phase4_review.py:44:    logger.info(f\"[Phase 4] Generating diffs for {len(current_proposed_changes)} proposed changes.\")\n/app/src/services/utils/code_agent/phase4_review.py:66:                logger.info(f\"[Phase 4] Read current content of {file_path} for diff.\")\n/app/src/services/utils/code_agent/phase4_review.py:68:                logger.info(f\"[Phase 4] File {file_path} not found for diff. Assuming it's a new file proposal.\")\n/app/src/services/utils/code_agent/phase4_review.py:86:                logger.info(f\"[Phase 4] Calling MCP 'get-unified-diff' for {file_path}\")\n/app/src/services/utils/code_agent/phase4_review.py:96:                    logger.info(f\"[Phase 4] Extracted diff text from CallToolResult for {file_path}\")\n/app/src/services/utils/code_agent/phase4_review.py:123:    logger.info(f\"[Phase 4] Review and presentation phase finished.\")\n/app/src/services/utils/code_agent/phase1_clarification.py:27:    logger.info(f\"[Phase 1 Logic] Clarifying request: '{initial_request}'\")\n/app/src/services/utils/code_agent/phase1_clarification.py:69:        logger.info(f\"[Phase 1 Logic] Raw LLM response for clarification: '{response_content}'\")\n/app/src/services/utils/code_agent/phase1_clarification.py:84:            logger.info(f\"[Phase 1 Logic] Successfully clarified goal: '{parsed_result['clarified_goal']}'\")\n/app/src/services/utils/code_agent/phase1_clarification.py:99:            logger.info(f\"[Phase 1 Logic] Clarifying questions identified: {parsed_result['clarifying_questions_needed']}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:173:        logger.info(f\"AICodeAgentServiceLangchain initialized with ID: {self.id}, Model: {self._model}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:176:        logger.info(f\"AICodeAgentServiceLangchain.initialize (id={id(self)}): Starting initialization.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:178:            logger.info(f\"AICodeAgentServiceLangchain.initialize (id={id(self)}): Dependencies already initialized. Skipping re-initialization.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:184:            logger.info(f\"AICodeAgentServiceLangchain (id={id(self)}): MyQdrantClient initialized for host: {os.getenv('QDRANT_HOST', 'localhost')}:{os.getenv('QDRANT_PORT', 6333)}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:194:            logger.info(f\"AICodeAgentServiceLangchain: Qdrant client initialized for code search at {QDRANT_CODE_HOST}:{QDRANT_CODE_PORT}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:201:            logger.info(f\"AICodeAgentServiceLangchain: Ensured Qdrant collection '{self._codebase_collection_name}' exists.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:269:        logger.info(\"AICodeAgentServiceLangchain: Added local tool 'propose_code_modification'.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:272:        logger.info(f\"AICodeAgentServiceLangchain.initialize (id={id(self)}): Dependencies initialized.\")\n/app/src/services/implementations/aic_code_agent_langchain.py:285:        logger.info(f\"Recorded proposal for {file_path}. Notes: {notes}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:308:        logger.info(f\"AICodeAgentServiceLangchain: Attempting to execute tool: {func_name} with arguments: {args}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:330:        logger.info(f\"Adapter called for MCP Tool: {tool_name_to_exec}. Langchain raw_pos_args: {args_from_langchain}, raw_kw_args: {kwargs_from_langchain}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:364:            logger.info(f\"Executing MCP Tool: {tool_name_to_exec} with determined args for MCP: {actual_args_for_mcp}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:473:        logger.info(f\"Sending workflow status update: Phase='{phase}', Message='{status_message}', TargetRoom='{room_target}'\")\n/app/src/services/implementations/aic_code_agent_langchain.py:514:            logger.info(f\"Conceptual save of workflow step '{step_name}' for user '{username}', convo '{conversation_id}'. Status: {status}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:678:        logger.info(f\"Executing {self.name} (id={id(self)}) with input: {input_data}\")\n/app/src/services/implementations/aic_code_agent_langchain.py:815:            logger.info(f\"{self.name}: Returning formatted service response: {json.dumps(formatted_response, default=str)}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:100:            logger.info(f\"[red]Could not read config {path}: {exc}[/red]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:127:        logger.info(f\"[green]Started MCP server:[/green] {name}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:142:            logger.info(f\"[red]tools/list error from {server_name}: {rsp['error']}[/red]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:253:                logger.info(f\"[green]Discovered {len(server_tools)} tools from {server_name}.[/green]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:255:                logger.info(f\"[yellow]Could not list tools from {server_name}: {exc}[/yellow]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:258:        logger.info(f\"[bold blue]Total available tools:[/bold blue] {len(self.oa_tools)}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:264:        logger.info(\"[bold magenta]MCP Agent (OpenAI edition)[/bold magenta]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:265:        logger.info(\"Type 'exit' to quit.\\n\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:269:            logger.info(\"[yellow]No tools found – continuing with plain chat.[/yellow]\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:289:            logger.info(f\"\\n[bold green]Assistant:[/bold green] {asst_msg.content}\\n\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:331:                logger.info(f\"\\n[bold green]Assistant:[/bold green] {follow.content}\\n\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:386:        logger.info(f\"\\n[bold yellow]Tool Call:[/bold yellow] {tool_call.name}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:387:        logger.info(f\"[bold yellow]Arguments:[/bold yellow] {json.dumps(tool_call.arguments, indent=2)}\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:407:            logger.info(f\"\\n[bold yellow]Reasoning:[/bold yellow]\\n{message.reasoning_content}\\n\")\n/app/src/services/implementations/gpt_fine_tuning_trace_agent.py:479:        logger.info(f\"\\n[bold blue]Conversation trace saved to:[/bold blue] {trace_path}\")\n/app/src/services/implementations/langchain_text_service.py:127:            logger.info(f\"LangChainService: Backward compatibility - mapping 'langchain_service' to '{self.DEFAULT_SERVICE_ID}'\")\n/app/src/services/implementations/langchain_text_service.py:156:        logger.info(f\"LangChainCodeAgent initialized with ID: {self.id}, Model: {self._model}\")\n/app/src/services/implementations/langchain_text_service.py:157:        # logger.info(f\"kwargs: {kwargs}, args: {args}\") # Potentially verbose\n/app/src/services/implementations/langchain_text_service.py:206:            logger.info(f\"Generated conversation summary: {summary[:100]}...\")\n/app/src/services/implementations/langchain_text_service.py:228:        logger.info(f\"LangChainService.initialize (id={id(self)}): Starting initialization.\")\n/app/src/services/implementations/langchain_text_service.py:230:            logger.info(f\"LangChainService.initialize (id={id(self)}): Dependencies (clients, MCP tool defs) already initialized. Skipping re-initialization.\")\n/app/src/services/implementations/langchain_text_service.py:236:            logger.info(f\"LangChainService (id={id(self)}): MyQdrantClient initialized for host: {QDRANT_HOST}:{QDRANT_PORT}\")\n/app/src/services/implementations/langchain_text_service.py:247:                logger.info(f\"LangChainService (id={id(self)}): Neo4jGraphAnalytics initialized with Neo4j client\")\n/app/src/services/implementations/langchain_text_service.py:259:            logger.info(f\"LangChainService (id={id(self)}): Elastic client initialized.\")\n/app/src/services/implementations/langchain_text_service.py:273:                logger.info(f\"LangChainService (id={id(self)}): FunctionsManager's MCP servers not fully initialized. Attempting to initialize them now via mcp_cli.chat_session.initialize_servers().\")\n/app/src/services/implementations/langchain_text_service.py:277:            logger.info(f\"LangChainService (id={id(self)}): Status of MCPClient servers before listing tools:\")\n/app/src/services/implementations/langchain_text_service.py:280:                logger.info(f\"  - Server {s_idx} ('{s_server.name}' id={id(s_server)}): Session {session_status}\")\n/app/src/services/implementations/langchain_text_service.py:289:                logger.info(f\"LangChainService (id={id(self)}): Fetched and stored {len(self._all_mcp_tool_definitions)} MCPClientTool definitions.\")\n/app/src/services/implementations/langchain_text_service.py:293:                logger.info(f\"LangChainService (id={id(self)}): Fetched and stored {len(self._all_mcp_tool_definitions)} MCPClientTool definitions.\")\n/app/src/services/implementations/langchain_text_service.py:311:        logger.info(f\"LangChainService.initialize (id={id(self)}): Dependencies initialized.\")\n/app/src/services/implementations/langchain_text_service.py:316:        logger.info(f\"Adapter called for MCP Tool: {tool_name_to_exec}. Langchain raw_pos_args: {args_from_langchain}, raw_kw_args: {kwargs_from_langchain}\")\n/app/src/services/implementations/langchain_text_service.py:352:                        logger.info(f\"Mapped single positional arg from Langchain to '{first_prop_name}' for tool '{tool_name_to_exec}'.\")\n/app/src/services/implementations/langchain_text_service.py:362:                            logger.info(f\"Mapped positional arg from Langchain to '{prop_name}' for tool '{tool_name_to_exec}'.\")\n/app/src/services/implementations/langchain_text_service.py:366:                    logger.info(f\"Tool '{tool_name_to_exec}' expects arguments, but none were provided by Langchain. Passing empty args to MCP tool.\")\n/app/src/services/implementations/langchain_text_service.py:376:            logger.info(f\"Executing MCP Tool: {tool_name_to_exec} with determined args for MCP: {actual_args_for_mcp}\")\n/app/src/services/implementations/langchain_text_service.py:409:                        logger.info(f\"Tool '{tool_name_to_exec}' generated an image. Returning placeholder to agent: '{text_output_for_agent}'\")\n/app/src/services/implementations/langchain_text_service.py:499:        logger.info(\"---CODE AGENT PLANNER NODE: Generating execution plan---\")\n/app/src/services/implementations/langchain_text_service.py:561:                logger.info(\"Planner determined no specific plan is needed.\")\n/app/src/services/implementations/langchain_text_service.py:564:                logger.info(f\"Generated Plan:\\n{plan_text}\")\n/app/src/services/implementations/langchain_text_service.py:660:            logger.info(f\"Agent decided to finish. Final output: {state['output'][:100]}...\")\n/app/src/services/implementations/langchain_text_service.py:662:            logger.info(f\"Agent decided to call tool: {agent_outcome.tool} with args: {agent_outcome.tool_input}\")\n/app/src/services/implementations/langchain_text_service.py:689:                logger.info(f\"Executing tool '{tool_name}' with input: {tool_input}\")\n/app/src/services/implementations/langchain_text_service.py:698:                logger.info(f\"Tool '{tool_name}' executed. Observation: {observation_str[:200]}...\")\n/app/src/services/implementations/langchain_text_service.py:711:        logger.info(f\"Executing {self.name} (id={id(self)}) with input: {input_data}\")\n/app/src/services/implementations/langchain_text_service.py:742:                logger.info(f\"{self.name}: Found {len(tools_for_this_invocation)} relevant tools.\")\n/app/src/services/implementations/langchain_text_service.py:761:                    logger.info(f\"{self.name}: {len(combined_history_messages)} messages exceed limit of {self._history_limit}, summarizing...\")\n/app/src/services/implementations/langchain_text_service.py:779:                    logger.info(f\"{self.name}: Summarized to {len(combined_history_messages)} messages with summary\")\n/app/src/services/implementations/langchain_text_service.py:781:                logger.info(f\"{self.name}: Using {len(combined_history_messages)} combined history messages for agent.\")\n/app/src/services/implementations/langchain_text_service.py:832:                logger.info(f\"Enhanced agent state with code analysis. Code task: {state.get('is_code_task', False)}\")\n/app/src/services/implementations/langchain_text_service.py:893:            logger.info(f\"{self.name}: LangGraph agent execution completed. Response: {final_response_text}\")\n/app/src/services/implementations/langchain_text_service.py:907:            logger.info(f\"{self.name}: Returning formatted service response: {json.dumps(formatted_response, default=str)}\")\n/app/src/services/implementations/langchain_text_service.py:912:            logger.info(f\"{self.name}: Returning error service response: {json.dumps(error_response, default=str)}\")\n/app/src/services/implementations/langchain_text_service.py:918:                logger.info(f\"Saved execution trace for conversation {conversation_id}\")\n/app/src/services/implementations/gpt_text_service.py:79:        logger.info(f\"AITextService initialized with ID: {self.id}, Model: {self._model}\")\n/app/src/services/implementations/gpt_text_service.py:80:        # logger.info(f\"kwargs: {kwargs}, args: {args}\") # Potentially verbose\n/app/src/services/implementations/gpt_text_service.py:96:        logger.info(f\"Initializing AITextService: {self.id}\")\n/app/src/services/implementations/gpt_text_service.py:111:            logger.info(f\"AITextService: MyQdrantClient initialized for host: {QDRANT_HOST}:{QDRANT_PORT}\")\n/app/src/services/implementations/gpt_text_service.py:119:            logger.info(\"AITextService: Elastic client initialized.\")\n/app/src/services/implementations/gpt_text_service.py:133:            logger.info(f\"{self.name}: FunctionsManager's MCP servers not fully initialized. Initializing now.\")\n/app/src/services/implementations/gpt_text_service.py:142:                logger.info(f\"AITextService: Loaded {len(self.available_functions)} available functions from MCP for LLM.\")\n/app/src/services/implementations/gpt_text_service.py:150:        logger.info(\"AITextService: Added local tool 'get_similar_messages_tool'.\")\n/app/src/services/implementations/gpt_text_service.py:249:        logger.info(f\"AITextService: Attempting to execute tool: {func_name} with arguments: {args}\")\n/app/src/services/implementations/gpt_text_service.py:312:                    logger.info(f\"Extracted text from MCP tool '{func_name}' specific structure: '{func_response_content}'\")\n/app/src/services/implementations/gpt_text_service.py:316:                    logger.info(f\"Extracted text from MCP tool '{func_name}' .text attribute: '{func_response_content}'\")\n/app/src/services/implementations/gpt_text_service.py:337:        logger.info(f\"Executing {self.name} with input: {input_data}\")\n/app/src/services/implementations/gpt_text_service.py:361:                logger.info(f\"AITextService: Detected summary command: '{current_user_message}'\")\n/app/src/services/implementations/gpt_text_service.py:401:                logger.info(f\"AITextService: Using system message from input data for dynamic prompt generation.\")\n/app/src/services/implementations/gpt_text_service.py:409:                        logger.info(f\"AITextService: Using system message for assistant_id '{assistant_id}' from ES for dynamic prompt generation.\")\n/app/src/services/implementations/gpt_text_service.py:438:                logger.info(f\"AITextService: Final list of {len(tools_for_llm_call)} tools for LLM.\")\n/app/src/services/implementations/gpt_text_service.py:442:            logger.info(f\"AITextService: Making first LLM call. Msgs: {len(messages_for_llm_turn1)}, Tools: {len(tools_for_llm_call or [])}\")\n/app/src/services/implementations/gpt_text_service.py:457:                logger.info(f\"AITextService: LLM requested tool calls: {assistant_message_turn1.tool_calls}\")\n/app/src/services/implementations/gpt_text_service.py:519:                    logger.info(f\"AITextService: Added explicit user instruction for 2nd LLM call: {user_instruction_for_turn2}\")\n/app/src/services/implementations/gpt_text_service.py:522:                logger.info(\"AITextService: Making second LLM call with tool responses and potentially explicit instruction.\")\n/app/src/services/implementations/gpt_text_service.py:533:                logger.info(\"AITextService: LLM responded directly (no tool calls).\")\n/app/src/services/implementations/gpt_text_service.py:550:                    logger.info(\"AITextService: Final RAG interaction saved to Qdrant.\")\n/app/src/services/implementations/gpt_code_agent.py:87:        logger.info(f\"AICodeAnalyserService initialized with ID: {self.id}, Model: {self._model}\")\n/app/src/services/implementations/gpt_code_agent.py:97:        logger.info(f\"Initializing AICodeAnalyserService: {self.id}\")\n/app/src/services/implementations/gpt_code_agent.py:110:            logger.info(f\"AICodeAnalyserService: Qdrant client initialized for code search at {QDRANT_HOST}:{QDRANT_PORT}\")\n/app/src/services/implementations/gpt_code_agent.py:111:            logger.info(f\"AICodeAnalyserService: Code Embedding Generator configured to use server: {embedding_server_url} (intended model: {code_embedding_model_name})\")\n/app/src/services/implementations/gpt_code_agent.py:120:            logger.info(f\"AICodeAnalyserService: Ensured Qdrant collection '{self._codebase_collection_name}' exists with vector size {CODE_EMBEDDING_DIMENSION}.\")\n/app/src/services/implementations/gpt_code_agent.py:152:        logger.info(\"AICodeAnalyserService: Added local tool 'propose_code_modification'.\")\n/app/src/services/implementations/gpt_code_agent.py:175:        logger.info(f\"AICodeAnalyserService: Attempting to execute tool: {func_name} with arguments: {args}\")\n/app/src/services/implementations/gpt_code_agent.py:195:                logger.info(f\"Recorded proposal for {file_path}. Notes: {notes}\")\n/app/src/services/implementations/gpt_code_agent.py:248:        logger.info(f\"Sending workflow status update: Phase='{phase}', Message='{status_message}', TargetRoom='{room_target}'\")\n/app/src/services/implementations/gpt_code_agent.py:262:        logger.info(f\"Executing AICodeAnalyserService ({self.name}) with input: {input_data}\")\n/app/src/services/implementations/gpt_code_agent.py:278:                logger.info(f\"Current Workflow State: {current_state.name}\")\n/app/src/services/implementations/gpt_code_agent.py:373:                        logger.info(f\"[Workflow] Clarified Goal: {clarified_goal}\")\n/app/src/services/implementations/gpt_code_agent.py:393:                                logger.info(f\"[Workflow] Fetched {len(qdrant_history_messages)} history messages for planning.\")\n/app/src/services/implementations/gpt_code_agent.py:437:                        logger.info(f\"[Workflow] Generated Plan:\\n{generated_plan}\")\n/app/src/services/implementations/gpt_code_agent.py:480:                    logger.info(f\"[Workflow] Phase 3 Finished. Output: {final_implementation_output[:200]}...\")\n/app/src/services/implementations/gpt_code_agent.py:532:                logger.info(f\"[Workflow] Awaiting user clarification. Questions: {clarifying_questions}\")\n/app/src/services/implementations/gpt_code_agent.py:551:                        logger.info(\"AICodeAnalyserService: Final workflow interaction saved to Qdrant.\")\n/app/src/services/implementations/gpt_code_agent.py:555:                logger.info(\"AICodeAnalyserService: Workflow finished successfully.\")\n/app/src/services/implementations/gpt_code_agent.py:633:            logger.info(f\"Conceptual save of workflow step '{step_name}' for user '{username}', convo '{conversation_id}'. Status: {status}\")\n/app/src/managers/AssistantManager.py:33:            logger.info(f\"GET OR CREATE USER : {username} {assistant_id}\")\n/app/src/managers/AssistantManager.py:40:            logger.info(f\"IN GET USER WITH  : {assistant}\")\n/app/src/managers/AssistantManager.py:64:                    logger.info(f\"Stored assistant in Neo4j: {assistant_id}\")\n/app/src/managers/AssistantManager.py:68:            logger.info(f\"Created/retrieved user: {username} with key: {user_key}\")\n/app/src/managers/AssistantManager.py:88:                    logger.info(\"Would get assistants from Neo4j if method was implemented\")\n/app/src/managers/AssistantManager.py:132:        logger.info(\"UPDATE ASSISTANT\")\n/app/src/managers/AssistantManager.py:153:            logger.info(f\"Before store Assistant {updated_data}\")\n/app/src/managers/AssistantManager.py:165:                    logger.info(f\"Updated assistant in Neo4j: {assistant_id}\")\n/app/src/managers/AssistantManager.py:178:            logger.info(f\"Getting Assistant: {assistant_id}\")\n/app/src/managers/AssistantManager.py:188:                    logger.info(f\"Would get assistant {assistant_id} from Neo4j if method was implemented\")\n/app/src/managers/AssistantManager.py:216:                logger.info(f\"Stored assistant in Neo4j: {data['id']}\")\n/app/src/managers/ServicesManager.py:46:        logger.info(f\"ServicesManager __init__ effective base_service_config keys: {list(self._internal_base_service_config.keys())}\")\n/app/src/managers/ServicesManager.py:55:        logger.info(\n/app/src/managers/ServicesManager.py:78:            logger.info(\n/app/src/managers/ServicesManager.py:99:            logger.info(\"ServicesManager already initialized. Skipping.\")\n/app/src/managers/ServicesManager.py:102:        logger.info(\"ServicesManager: Starting initialization process...\")\n/app/src/managers/ServicesManager.py:108:            logger.info(\"ServicesManager: FunctionsManager dependency not initialized. Initializing now...\")\n/app/src/managers/ServicesManager.py:115:            logger.info(\"ServicesManager: FunctionsManager already initialized.\")\n/app/src/managers/ServicesManager.py:117:        logger.info(\"ServicesManager: Proceeding to discover services...\")\n/app/src/managers/ServicesManager.py:124:            logger.info(f\"ServicesManager.initialize: Initializing {len(self.services)} discovered services...\")\n/app/src/managers/ServicesManager.py:135:                    logger.info(f\"ServicesManager: Service '{service_id_res}' initialized successfully (Result: {result}).\")\n/app/src/managers/ServicesManager.py:142:        logger.info(f\"ServicesManager: Initialization complete. Total services loaded and initialized: {final_service_count}.\")\n/app/src/managers/ServicesManager.py:157:                logger.info(\"ServicesManager: Default workflow already exists\")\n/app/src/managers/ServicesManager.py:160:            logger.info(\"ServicesManager: Creating default workflow for assistants...\")\n/app/src/managers/ServicesManager.py:186:                logger.info(f\"ServicesManager: Default workflow created successfully: {workflow_id}\")\n/app/src/managers/ServicesManager.py:196:            logger.info(f\"Initializing service: {service_id}\")\n/app/src/managers/ServicesManager.py:203:                    logger.info(f\"Would store service initialization start in Neo4j: {service_id}\")\n/app/src/managers/ServicesManager.py:219:                    logger.info(f\"Would store service initialization result in Neo4j: {service_id}, result: {result}\")\n/app/src/managers/ServicesManager.py:229:            logger.info(f\"Service {service_id} initialized: {result}\")\n/app/src/managers/ServicesManager.py:237:        logger.info(f\"ServicesManager._discover_services: Starting. Dependencies status: \"\n/app/src/managers/ServicesManager.py:248:                logger.info(f\"ServicesManager._discover_services: Created missing services directory: {services_path}\")\n/app/src/managers/ServicesManager.py:251:            logger.info(f\"ServicesManager._discover_services: Scanning for services in: {services_path}\")\n/app/src/managers/ServicesManager.py:258:            logger.info(f\"ServicesManager._discover_services: Found potential service files: {service_files}\")\n/app/src/managers/ServicesManager.py:263:                    logger.info(f\"ServicesManager._discover_services: Attempting to import module: {module_name}\")\n/app/src/managers/ServicesManager.py:274:                            logger.info(f\"ServicesManager._discover_services: Found service class '{name}' in {module_name}.\")\n/app/src/managers/ServicesManager.py:321:                                logger.info(f\"ServicesManager._discover_services: Successfully loaded and added service '{service_instance.id}' from class '{name}'. Total services now: {len(self.services)}\")\n/app/src/managers/ServicesManager.py:335:        logger.info(f\"ServicesManager._discover_services: Discovery finished. Total services in manager: {len(self.services)}\")\n/app/src/managers/ServicesManager.py:351:            logger.info(f\"ServicesManager: Backward compatibility - mapping '{service_id}' to '{mapped_id}'\")\n/app/src/managers/ServicesManager.py:380:        logger.info(f\"ServicesManager.get_all_services: Returning {len(services_data)} services.\")\n/app/src/managers/ServicesManager.py:396:        logger.info(f\"ServicesManager.execute_service: Executing service '{service_id}' with input: {input_data}\")\n/app/src/managers/ServicesManager.py:405:                    logger.info(f\"Would store service execution start in Neo4j: {execution_id}\")\n/app/src/managers/ServicesManager.py:421:            logger.info(f\"ServicesManager.execute_service: Service '{service_id}' executed. Success: {success_status}, Message: {message_content}\")\n/app/src/managers/ServicesManager.py:428:                    logger.info(f\"Would store service execution result in Neo4j: {execution_id}, success: {success_status}\")\n/app/src/managers/WorkflowManager.py:30:        logger.info(f\"Executing (MANAGER) workflow: {workflow_id} with input: {input_data}\")\n/app/src/managers/WorkflowManager.py:45:                    logger.info(f\"Would store workflow execution start in Neo4j: {execution_id}\")\n/app/src/managers/WorkflowManager.py:57:            logger.info(f\"Executing (MANAGER) Result: {result}\")\n/app/src/managers/WorkflowManager.py:64:                    logger.info(f\"Would store workflow execution result in Neo4j: {execution_id}\")\n/app/src/managers/WorkflowManager.py:98:        logger.info(f\"Workflow Manager: Starting _execute_workflow_blocks for workflow_id: '{workflow.get('id', 'N/A')}'. Blocks: {len(blocks)}, Connections: {len(connections)}\")\n/app/src/managers/WorkflowManager.py:100:        logger.info(f\"Executing workflow with {len(blocks)} blocks and {len(connections)} connections\")\n/app/src/managers/WorkflowManager.py:108:        logger.info(f\"Block execution order: {execution_order}\")\n/app/src/managers/WorkflowManager.py:122:            logger.info(f\"Executing block {block_id} with service_id: {service_id}\")\n/app/src/managers/WorkflowManager.py:123:            logger.info(f\"Block data: {block}\")\n/app/src/managers/WorkflowManager.py:131:            logger.info(f\"Available services: {available_services}\")\n/app/src/managers/WorkflowManager.py:159:                        logger.info(f\"Would store block execution start in Neo4j: {execution_id}, block: {block_id}\")\n/app/src/managers/WorkflowManager.py:181:                        logger.info(f\"Would store block execution result in Neo4j: {execution_id}, block: {block_id}\")\n/app/src/managers/WorkflowManager.py:193:                logger.info(f\"Block {block_id} execution result: {result}\")\n/app/src/managers/WorkflowManager.py:202:            logger.info(f\"Returning result from last block {execution_order[-1]}: {result}\")\n/app/src/managers/WorkflowManager.py:208:            logger.info(f\"Returning result from block {last_block_id}: {result}\")\n/app/src/managers/SettingsManager.py:16:        logger.info(\"[SettingsManager] Initialized\")\n/app/src/managers/SettingsManager.py:30:                    logger.info(f\"Would get user settings from Neo4j for user: {username}\")\n/app/src/managers/SettingsManager.py:60:                    logger.info(f\"Would update user settings in Neo4j for user: {username}\")\n/app/src/managers/scraper/whatsapp_manager.py:25:                logger.info(\"\\n=== WhatsApp Bot Started ===\")\n/app/src/managers/scraper/whatsapp_manager.py:28:                    logger.info(\"Failed to load chat history, continuing anyway...\")\n/app/src/managers/scraper/whatsapp_manager.py:31:                    logger.info(f\"Failed to find chat with {contact_name}\")\n/app/src/managers/scraper/whatsapp_manager.py:46:                                logger.info(f\"\\nReceived new message: {message}\")\n/app/src/managers/scraper/whatsapp_manager.py:53:                                    logger.info(\"Response sent successfully\")\n/app/src/managers/scraper/whatsapp_manager.py:55:                                    logger.info(\"Failed to send response\")\n/app/src/managers/scraper/whatsapp_manager.py:58:                        logger.info(f\"\\nError in message loop: {str(e)}\")\n/app/src/managers/scraper/whatsapp_manager.py:66:                logger.info(\"\\n=== WhatsApp Bot Stopped ===\")\n/app/src/managers/scraper/whatsapp_bot.py:36:            logger.info(\"Please scan QR Code and press Enter\")\n/app/src/managers/scraper/whatsapp_bot.py:40:            logger.info(f\"Error initializing WhatsApp bot: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:81:                            logger.info(f\"Successfully entered chat: {contact_name}\")\n/app/src/managers/scraper/whatsapp_bot.py:87:                    logger.info(f\"Retry {attempt + 1} finding chat...\")\n/app/src/managers/scraper/whatsapp_bot.py:92:                    logger.info(f\"Error finding chat (attempt {attempt + 1}): {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:95:        logger.info(f\"Failed to find chat after {max_retries} attempts: {contact_name}\")\n/app/src/managers/scraper/whatsapp_bot.py:119:                    logger.info(f\"Retrying chat find... (attempt {attempt + 1})\")\n/app/src/managers/scraper/whatsapp_bot.py:132:                    logger.info(\"Could not focus main chat view\")\n/app/src/managers/scraper/whatsapp_bot.py:148:                        logger.info(f\"Trying selector: {selector}\")\n/app/src/managers/scraper/whatsapp_bot.py:165:                            logger.info(f\"Successfully found and clicked input box with selector: {selector}\")\n/app/src/managers/scraper/whatsapp_bot.py:172:                        logger.info(f\"Failed with selector {selector}: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:176:                    logger.info(\"Could not find or click message input box\")\n/app/src/managers/scraper/whatsapp_bot.py:193:                    logger.info(\"Message sent successfully\")\n/app/src/managers/scraper/whatsapp_bot.py:199:                    logger.info(f\"Error while typing message: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:203:                logger.info(f\"Error sending message (attempt {attempt + 1}): {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:240:            logger.info(f\"Error sending file: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:257:                        logger.info(f\"Could not parse timestamp: {timestamp_str}\")\n/app/src/managers/scraper/whatsapp_bot.py:260:            logger.info(f\"Error extracting timestamp: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:267:                logger.info(\"Chat not properly loaded, retrying...\")\n/app/src/managers/scraper/whatsapp_bot.py:304:                        logger.info(f\"Found new message: {message_text} (Timestamp: {timestamp})\")\n/app/src/managers/scraper/whatsapp_bot.py:310:                    logger.info(f\"Error processing individual message {idx}: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:316:            logger.info(f\"Error in check_for_new_messages: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:326:            logger.info(\"Attempting to reconnect...\")\n/app/src/managers/scraper/whatsapp_bot.py:337:            logger.info(\"Please scan QR Code again and press Enter\")\n/app/src/managers/scraper/whatsapp_bot.py:342:            logger.info(f\"Reconnection failed: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:347:        logger.info(\"\\nLoading chat history...\")\n/app/src/managers/scraper/whatsapp_bot.py:361:                        logger.info(f\"Found {len(messages)} historical messages\")\n/app/src/managers/scraper/whatsapp_bot.py:375:                                    logger.info(f\"Added to history: {message_text[:50]}... (Timestamp: {timestamp})\")\n/app/src/managers/scraper/whatsapp_bot.py:377:                                logger.info(f\"Error processing historical message {idx}: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:379:                        logger.info(f\"Latest message timestamp: {self.last_message_timestamp}\")\n/app/src/managers/scraper/whatsapp_bot.py:384:            logger.info(\"No historical messages found\")\n/app/src/managers/scraper/whatsapp_bot.py:388:            logger.info(f\"Error loading chat history: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:400:            logger.info(\"\\n=== WhatsApp Chat Bot Started ===\")\n/app/src/managers/scraper/whatsapp_bot.py:404:                logger.info(\"Failed to load chat history, continuing anyway...\")\n/app/src/managers/scraper/whatsapp_bot.py:406:            logger.info(\"Monitoring for new messages... (Press Ctrl+C to stop)\")\n/app/src/managers/scraper/whatsapp_bot.py:409:                logger.info(f\"Failed to find chat with {contact_name}\")\n/app/src/managers/scraper/whatsapp_bot.py:419:                        logger.info(\"\\nChat view not loaded, attempting to reload...\")\n/app/src/managers/scraper/whatsapp_bot.py:425:                    logger.info(\"\\nChecking for new messages...\")  # Debug line\n/app/src/managers/scraper/whatsapp_bot.py:430:                        logger.info(f\"\\nProcessing {len(new_messages)} new messages\")\n/app/src/managers/scraper/whatsapp_bot.py:433:                            logger.info(f\"\\nReceived new message: {message}\")\n/app/src/managers/scraper/whatsapp_bot.py:439:                                logger.info(\"Response sent successfully\")\n/app/src/managers/scraper/whatsapp_bot.py:441:                                logger.info(\"Failed to send response, will retry on next loop\")\n/app/src/managers/scraper/whatsapp_bot.py:445:                        logger.info(\"No new messages found\")  # Debug line\n/app/src/managers/scraper/whatsapp_bot.py:450:                    logger.info(f\"\\nError in message loop: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:457:            logger.info(\"\\n\\n=== Bot stopped by user ===\")\n/app/src/managers/scraper/whatsapp_bot.py:460:            logger.info(f\"\\nFatal error: {str(e)}\")\n/app/src/managers/scraper/whatsapp_bot.py:462:                logger.info(\"Restarting bot...\")\n/app/src/managers/scraper/whatsapp_bot.py:470:            logger.info(\"\\n=== Closing bot... ===\")\n/app/src/managers/ChatManager.py:16:        logger.info(\"[ChatManager] Initialized\")\n/app/src/managers/ChatManager.py:20:        logger.info(f\"ChatManager: Getting conversation history for user_id='{user_id}', conversation_id='{conversation_id}', limit={limit}\")\n/app/src/managers/ChatManager.py:28:            logger.info(f\"ChatManager: Fetched messages from Qdrant for conversation_id='{conversation_id}'\")\n/app/src/managers/ChatManager.py:38:                    logger.info(f\"ChatManager: Fetched {len(neo4j_messages) if neo4j_messages else 0} messages from Neo4j for conversation_id='{conversation_id}'\")\n/app/src/managers/ChatManager.py:70:        logger.info(f\"ChatManager: Storing message for user_id='{user_id}', assistant_id='{assistant_id}', conversation_id='{conversation_id}'\")\n/app/src/managers/ChatManager.py:102:                    logger.info(f\"ChatManager: Stored messages in Neo4j for conversation_id='{conversation_id}'\")\n/app/src/managers/FunctionsManager.py:50:        logger.info(\"Initializing FunctionsManager (Qdrant RAG system for functions)\")\n/app/src/managers/FunctionsManager.py:62:            logger.info(\"FunctionsManager: Initializing MCP servers for its internal MCPClient...\")\n/app/src/managers/FunctionsManager.py:67:                logger.info(f\"FunctionsManager: {initialized_server_count} MCP servers initialized for its internal MCPClient.\")\n/app/src/managers/FunctionsManager.py:69:            logger.info(\"FunctionsManager: Synchronizing tools to Qdrant using its internal MCPClient...\")\n/app/src/managers/FunctionsManager.py:71:            logger.info(\"FunctionsManager: Tool synchronization to Qdrant completed.\")\n/app/src/managers/FunctionsManager.py:84:                logger.info(f\"Creating Qdrant collection for functions: {self.FUNCTIONS_COLLECTION_NAME}\")\n/app/src/managers/FunctionsManager.py:92:                logger.info(f\"Qdrant collection for functions '{self.FUNCTIONS_COLLECTION_NAME}' already exists.\")\n/app/src/managers/FunctionsManager.py:97:                logger.info(\"FunctionsManager: MyNeo4jClient initialized successfully.\")\n/app/src/managers/FunctionsManager.py:107:            logger.info(\"FunctionsManager initialized successfully.\")\n/app/src/managers/FunctionsManager.py:141:                logger.info(f\"No functions found in Qdrant for query: '{query}'\")\n/app/src/managers/FunctionsManager.py:156:            logger.info(f\"Found function '{tool_name_to_execute}' (ID: {best_hit.id}, Score: {best_hit.score:.4f}) for query '{query}'.\")\n/app/src/managers/FunctionsManager.py:161:            logger.info(f\"Executing MCP tool '{tool_name_to_execute}' with arguments: {input_data}\")\n/app/src/managers/FunctionsManager.py:164:            logger.info(f\"Execution result for '{tool_name_to_execute}': {execution_result}\")\n/app/src/managers/FunctionsManager.py:217:                    logger.info(\"FunctionsManager: Attempting to list functions from MyNeo4jClient.\")\n/app/src/managers/FunctionsManager.py:227:                    logger.info(f\"FunctionsManager: Added {len(neo4j_function_names)} functions from MyNeo4jClient to the list.\")\n/app/src/managers/FunctionsManager.py:231:                logger.info(\"FunctionsManager: MyNeo4jClient not available, skipping listing its functions.\")\n/app/src/servers/server.py:34:            logger.info(f\"Detected change in {event.src_path}. Reloading server...\")\n/app/src/servers/server.py:100:            logger.info(\"Reloading server...\")\n/app/src/servers/server.py:111:        logger.info(\"Starting SocketIO server...\")\n/app/src/servers/server.py:139:            logger.info(f\"Starting {'secure ' if ssl_context else ''}server on port {port}...\")\n/app/src/servers/fastApi.py:51:        logger.info(f\"Reload mode enabled. Uvicorn will watch for changes. App target: {app_target}\")\n/app/src/servers/fastApi.py:58:        logger.info(f\"Starting secure FastAPI server on https://{host}:{port}\")\n/app/src/servers/fastApi.py:61:        logger.info(f\"Starting FastAPI server on http://{host}:{port} (SSL not configured).\")\n/app/src/servers/fastApi.py:63:        logger.info(f\"Starting FastAPI server on http://{host}:{port} (SSL not configured).\")\n/app/src/servers/mcpservers/socket_bridge.py:29:    logger.info(f\"SocketBridgeMCP server '{server.name}' starting up.\")\n/app/src/servers/mcpservers/socket_bridge.py:33:    logger.info(f\"SocketBridgeMCP server '{server.name}' shutting down.\")\n/app/src/servers/mcpservers/socket_bridge.py:90:    logger.info(f\"MCP Tool 'emit_socket_event' (Call ID: {tool_call_id}): Received request to emit event '{event_name}' in namespace '{namespace or '/'}'.\")\n/app/src/servers/mcpservers/socket_bridge.py:102:        logger.info(f\"MCP Tool 'emit_socket_event': {success_msg}\")\n/app/src/servers/mcpservers/socket_bridge.py:115:    logger.info(f\"Socket.IO: Client connected: SID='{sid}'\")\n/app/src/servers/mcpservers/socket_bridge.py:122:    #     logger.info(f\"Socket.IO: Client {sid} joined room '{user_room}'\")\n/app/src/servers/mcpservers/socket_bridge.py:126:    logger.info(f\"Socket.IO: Client disconnected: SID='{sid}'\")\n/app/src/servers/mcpservers/socket_bridge.py:131:    logger.info(f\"Socket.IO: Received 'client_event' from SID='{sid}' with data: {data}\")\n/app/src/servers/mcpservers/freecad.py:74:                logger.info(\"Screenshot unavailable in current view (likely Spreadsheet or TechDraw view)\")\n/app/src/servers/mcpservers/freecad.py:97:        logger.info(\"FreeCADMCP server starting up\")\n/app/src/servers/mcpservers/freecad.py:100:            logger.info(\"Successfully connected to FreeCAD on startup\")\n/app/src/servers/mcpservers/freecad.py:111:            logger.info(\"Disconnecting from FreeCAD on shutdown\")\n/app/src/servers/mcpservers/freecad.py:113:        logger.info(\"FreeCADMCP server shut down\")\n/app/src/servers/mcpservers/freecad.py:634:    logger.info(f\"Only text feedback: {_only_text_feedback}\")\n/app/src/servers/mcpservers/freecad.py:635:    logger.info(f\"Calling mcp.run() in {__file__}\")\n/app/src/servers/mcpservers/freecad.py:640:    logger.info(f\"Starting {__file__} script directly...\")\n/app/src/servers/mcpservers/pythonExecServer.py:82:        logger.info(f\"Calling mcp.run() in {__file__} for QdrantService\")\n/app/src/servers/mcpservers/diff.py:63:    logger.info(\"Initializing mcp-server-diff-python\")\n/app/src/servers/mcpservers/dockercompose.py:17:        logger.info(\"Docker Compose MCP server starting up\")\n/app/src/servers/mcpservers/dockercompose.py:20:            logger.info(\"Docker Compose MCP server initialized successfully on startup.\")\n/app/src/servers/mcpservers/dockercompose.py:29:        logger.info(\"Docker Compose MCP server shut down\")\n/app/src/servers/mcpservers/dockercompose.py:195:    logger.info(f\"Starting {__file__} script directly...\")\n/app/src/servers/mcpservers/dockercompose.py:197:        logger.info(f\"Calling mcp.run() in {__file__}\")\n/app/src/servers/mcpservers/trace_service.py:56:        logger.info(f\"Trace saved to {fname}\")\n/app/src/servers/mcpservers/echo.py:24:        logger.info(\"Echo server starting up\")\n/app/src/servers/mcpservers/echo.py:26:            logger.info(\"Successfully connected to Echo server on startup\")\n/app/src/servers/mcpservers/echo.py:31:        logger.info(f\"Echo MCP server '{server.name}' shut down.\")\n/app/src/servers/mcpservers/echo.py:38:logger.info(\"[echo.py] Registering Echo server handlers.\")\n/app/src/servers/mcpservers/echo.py:117:    logger.info(f\"Starting {__file__} script directly...\")\n/app/src/servers/mcpservers/echo.py:119:        logger.info(f\"Calling mcp.run() in {__file__}\")\n/app/src/servers/mcpservers/neoj.py:28:    logger.info(\"Initializing Neo4j MCP server state...\")\n/app/src/servers/mcpservers/neoj.py:33:            logger.info(\"Neo4j client initialized.\")\n/app/src/servers/mcpservers/neoj.py:39:        logger.info(\"Neo4j MCP server state initialized successfully\")\n/app/src/servers/mcpservers/neoj.py:45:        logger.info(\"Cleaning up Neo4j MCP server resources...\")\n/app/src/servers/mcpservers/neoj.py:52:logger.info(\"[neo4j_service.py] Registering Neo4j MCP handlers.\")\n/app/src/servers/mcpservers/neoj.py:148:            logger.info(f\"Retrieved {len(neo4j_messages)} messages from Neo4j for conversation {conversation_id}\")\n/app/src/servers/mcpservers/neoj.py:189:        logger.info(f\"Stored message and response in Neo4j for conversation {conversation_id}\")\n/app/src/servers/mcpservers/neoj.py:217:        logger.info(f\"Generated graph-based prompt for {node_type} {node_id}\")\n/app/src/servers/mcpservers/neoj.py:252:        logger.info(f\"Generated graph-based summary for conversation {conversation_id}\")\n/app/src/servers/mcpservers/neoj.py:334:        logger.info(f\"Adjusted context window for conversation {conversation_id}, found {len(adjusted_messages)} relevant messages\")\n/app/src/servers/mcpservers/neoj.py:344:    logger.info(f\"Starting {__file__} script directly for Neo4j MCP server...\")\n/app/src/servers/mcpservers/neoj.py:346:        logger.info(f\"Calling mcp.run() in {__file__} for Neo4j MCP server\")\n/app/src/servers/mcpservers/qdrant_service.py:44:        logger.info(f\"LLMClient (placeholder) initialized. Base URL: {self.base_url}\")\n/app/src/servers/mcpservers/qdrant_service.py:76:    logger.info(\"Initializing QdrantService server state...\")\n/app/src/servers/mcpservers/qdrant_service.py:97:            logger.info(\"Neo4j client initialized for code structure sync.\")\n/app/src/servers/mcpservers/qdrant_service.py:103:        logger.info(\"QdrantService server state initialized successfully\")\n/app/src/servers/mcpservers/qdrant_service.py:109:        logger.info(\"Cleaning up QdrantService server resources...\")\n/app/src/servers/mcpservers/qdrant_service.py:118:logger.info(\"[qdrant_service.py] Registering QdrantService MCP handlers.\")\n/app/src/servers/mcpservers/qdrant_service.py:481:    logger.info(f\"Starting codebase indexing for collection '{collection_name}' with dimension {vector_dimension}.\")\n/app/src/servers/mcpservers/qdrant_service.py:482:    logger.info(f\"Root directory: {root_dir}, Extensions: {extensions or FILE_EXTENSIONS_DEFAULT}\")\n/app/src/servers/mcpservers/qdrant_service.py:483:    logger.info(f\"Using embedding generator from MyQdrantClient: {type(embedding_gen)}\")\n/app/src/servers/mcpservers/qdrant_service.py:519:                        logger.info(f\"Neo4j: Added code structure for {file_path} ({len(classes)} classes)\")\n/app/src/servers/mcpservers/qdrant_service.py:523:        logger.info(msg)\n/app/src/servers/mcpservers/qdrant_service.py:541:    logger.info(f\"Searching codebase in collection '{collection_name}' for query: '{query[:50]}...' (top_k={top_k})\")\n/app/src/servers/mcpservers/qdrant_service.py:643:    logger.info(f\"Starting {__file__} script directly for QdrantService MCP server...\")\n/app/src/servers/mcpservers/qdrant_service.py:649:        logger.info(f\"Calling mcp.run() in {__file__} for QdrantService\")\n/app/src/servers/mcpservers/time.py:71:        logger.info(\"Time MCP server starting up. Verifying locale and timezone utilities.\")\n/app/src/servers/mcpservers/time.py:75:        logger.info(\"Time MCP server utilities appear to be available.\")\n/app/src/servers/mcpservers/time.py:78:        logger.info(\"Time MCP server shut down.\")\n/app/src/servers/mcpservers/time.py:81:logger.info(\"[time.py] Simple Time Server\")\n/app/src/servers/mcpservers/time.py:153:    logger.info(f\"Starting {__file__} script directly...\")\n/app/src/servers/mcpservers/time.py:155:        logger.info(f\"Calling mcp.run() in {__file__}\")\n/app/src/servers/mcpservers/weather.py:51:            logger.info(f\"api: {url}\")\n/app/src/servers/mcpservers/weather.py:93:            logger.info(f\"api: {url}\")\n/app/src/servers/embedding_server.py:27:    logger.info(f\"Successfully loaded SentenceTransformer model: {MODEL_NAME}\")\n/app/src/servers/embedding_server.py:48:    logger.info(f\"Received request to embed text (first 50 chars): {request.text[:50]}...\")\n/app/src/servers/embedding_server.py:51:        logger.info(f\"Generated embedding of dimension: {len(embedding_vector)}\")\n/app/src/qdrant/operations/workflows.py:46:                logger.info(f\"Creating Qdrant collection: {self.collection_name}\")\n/app/src/qdrant/operations/workflows.py:54:                logger.info(f\"Created Qdrant collection: {self.collection_name}\")\n/app/src/qdrant/operations/workflows.py:56:                logger.info(f\"Qdrant collection already exists: {self.collection_name}\")\n/app/src/qdrant/operations/workflows.py:101:            logger.info(f\"Workflow saved to Qdrant: {workflow['id']}\")\n/app/src/qdrant/operations/workflows.py:129:                logger.info(f\"Retrieved workflow from Qdrant: {workflow_id}\")\n/app/src/qdrant/operations/workflows.py:159:            logger.info(f\"Found {len(workflows)} workflows for query: {query}\")\n/app/src/qdrant/operations/workflows.py:197:            logger.info(f\"Listed {len(workflows)} workflows from Qdrant\")\n/app/src/qdrant/operations/workflows.py:213:            logger.info(f\"Deleted workflow from Qdrant: {workflow_id}\")\n/app/src/qdrant/operations/assistants.py:54:                    logger.info(f\"Creating Qdrant collection: {collection_name}\")\n/app/src/qdrant/operations/assistants.py:62:                    logger.info(f\"Created Qdrant collection: {collection_name}\")\n/app/src/qdrant/operations/assistants.py:64:                    logger.info(f\"Qdrant collection already exists: {collection_name}\")\n/app/src/qdrant/operations/assistants.py:111:            logger.info(f\"Assistant stored in Qdrant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:138:                logger.info(f\"Retrieved assistant from Qdrant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:191:            logger.info(f\"Retrieved {len(assistants)} assistants from Qdrant\")\n/app/src/qdrant/operations/assistants.py:232:            logger.info(f\"System message stored in Qdrant for assistant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:259:                logger.info(f\"Retrieved system message from Qdrant for assistant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:299:            logger.info(f\"Avatar stored in Qdrant for assistant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:326:                logger.info(f\"Retrieved avatar from Qdrant for assistant: {assistant_id}\")\n/app/src/qdrant/operations/assistants.py:329:                logger.info(f\"Avatar not found in Qdrant for assistant: {assistant_id}\")\n/app/src/clients/qdrant.py:84:        logger.info(f\"[Embedding] Sending to server: {self.embedding_server_url}/embed | Payload: {{'text': {text!r}}}\")\n/app/src/clients/qdrant.py:90:            #logger.info(f\"[Embedding] Server response status: {response.status}\")\n/app/src/clients/qdrant.py:93:                # logger.info(f\"[Embedding] Server response: {result}\")\n/app/src/clients/qdrant.py:112:        logger.info(f\"[Embedding] Sending to server (sync): {self.embedding_server_url}/embed | Payload: {{'text': {text!r}}}\")\n/app/src/clients/qdrant.py:163:        logger.info(f\"MyQdrantClient initializing with vector_size: {vector_size}. EmbeddingGenerator: {type(self.embedding_generator).__name__}\")\n/app/src/clients/qdrant.py:173:            logger.info(f\"Successfully connected to Qdrant at {_qdrant_host}:{_qdrant_port}\")\n/app/src/clients/qdrant.py:186:                logger.info(f\"Creating collection '{self.collection_name}' with vector size {self.vector_size}...\")\n/app/src/clients/qdrant.py:191:                logger.info(f\"Collection '{self.collection_name}' created.\")\n/app/src/clients/qdrant.py:193:                logger.info(f\"Collection '{self.collection_name}' already exists. Using existing collection.\")\n/app/src/clients/qdrant.py:197:            logger.info(f\"Ensuring 'datetime' payload index for 'timestamp' field in collection '{self.collection_name}'.\")\n/app/src/clients/qdrant.py:203:            logger.info(f\"Payload index for 'timestamp' (datetime type) ensured for collection '{self.collection_name}'.\")\n/app/src/clients/qdrant.py:207:                logger.info(f\"Creating collection '{self.functions_collection_name}' for MCP functions...\")\n/app/src/clients/qdrant.py:215:                logger.info(f\"Collection '{self.functions_collection_name}' created with indices.\")\n/app/src/clients/qdrant.py:217:                logger.info(f\"Collection '{self.functions_collection_name}' already exists.\")\n/app/src/clients/qdrant.py:221:                logger.info(f\"Creating collection '{self.user_analysis_collection_name}' for user psychological analysis...\")\n/app/src/clients/qdrant.py:231:                logger.info(f\"Collection '{self.user_analysis_collection_name}' created with indices.\")\n/app/src/clients/qdrant.py:233:                logger.info(f\"Collection '{self.user_analysis_collection_name}' already exists.\")\n/app/src/clients/qdrant.py:237:                logger.info(f\"Creating collection '{self.conversation_topics_collection_name}' for conversation topics...\")\n/app/src/clients/qdrant.py:248:                logger.info(f\"Collection '{self.conversation_topics_collection_name}' created with indices.\")\n/app/src/clients/qdrant.py:250:                logger.info(f\"Collection '{self.conversation_topics_collection_name}' already exists.\")\n/app/src/clients/qdrant.py:254:                logger.info(f\"Creating collection '{self.workflow_steps_collection_name}' for workflow steps...\")\n/app/src/clients/qdrant.py:265:                logger.info(f\"Collection '{self.workflow_steps_collection_name}' created with indices.\")\n/app/src/clients/qdrant.py:267:                logger.info(f\"Collection '{self.workflow_steps_collection_name}' already exists.\")\n/app/src/clients/qdrant.py:291:        logger.info(\"IN QDRANT SEND MESSAGE\")\n/app/src/clients/qdrant.py:330:            logger.info(f\"Stored message from '{username}' (assistant: {assistant_id}, convo: {conversation_id}): {message[:100]}...\")\n/app/src/clients/qdrant.py:338:        logger.info(\"IN QDRANT GET MESSAGE\")\n/app/src/clients/qdrant.py:360:        logger.info(f\"Qdrant get_messages: Constructed filter: {filter_obj.model_dump_json() if filter_obj else 'None'}\")\n/app/src/clients/qdrant.py:398:        logger.info(f\"Qdrant get_messages: Fetched {len(all_points)} raw points from Qdrant before applying limit.\")\n/app/src/clients/qdrant.py:417:        logger.info(f\"Qdrant get_messages: Processing {len(final_points_to_return)} points after limit of {limit}.\")\n/app/src/clients/qdrant.py:514:        logger.info(f\"Qdrant get_messages: Returning {len(conversations_list)} conversations for sender='{sender}', conversation_id='{conversation_id}', assistant_id='{assistant_id}'.\")\n/app/src/clients/qdrant.py:561:            # logger.info(f\"Upserted MCP function schema for '{name}' into '{self.functions_collection_name}'.\")\n/app/src/clients/qdrant.py:570:            logger.info(\"MyQdrantClient.search_mcp_function_schemas: query_text is empty, returning empty list.\")\n/app/src/clients/qdrant.py:573:        logger.info(f\"MyQdrantClient.search_mcp_function_schemas: Generating embedding for query: '{query_text[:100]}...'\")\n/app/src/clients/qdrant.py:592:            logger.info(f\"MyQdrantClient.search_mcp_function_schemas: Raw search_results count from Qdrant: {len(search_results)}\")\n/app/src/clients/qdrant.py:607:            logger.info(f\"MyQdrantClient.search_mcp_function_schemas: Returning {len(results_with_scores)} results with scores after filtering invalid content.\")\n/app/src/clients/qdrant.py:654:            logger.info(f\"Stored user psychological analysis for user '{username}', convo '{conversation_id}' in '{self.user_analysis_collection_name}'.\")\n/app/src/clients/qdrant.py:686:            logger.info(f\"Successfully set topic in Qdrant for conversation {conversation_id}: {topic}\")\n/app/src/clients/qdrant.py:710:                logger.info(f\"Retrieved topic from Qdrant for conversation {conversation_id}: {topic}\")\n/app/src/clients/qdrant.py:712:            logger.info(f\"No topic found in Qdrant for conversation {conversation_id}\")\n/app/src/clients/types.py:76:        logger.info(\n/app/src/clients/types.py:92:            logger.info(f\"Sending to LLM: {messages}\")\n/app/src/clients/neoj.py:64:            logger.info(f\"Successfully connected to Neo4j at {self._uri}\")\n/app/src/clients/neoj.py:99:            logger.info(\"Neo4j schema initialized successfully\")\n/app/src/clients/neoj.py:109:            logger.info(\"Neo4j driver closed\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:25:    logger.info(\n/app/src/clients/qdrant_utils/dynamic_system_message.py:52:                        logger.info(f\"generate_dynamic_system_message: Filtering out 'Workflow execution failed' message from history for dynamic prompt context.\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:62:                    logger.info(f\"generate_dynamic_system_message: Using recent history for prompt (first 200 chars): {recent_history_str[:200]}...\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:64:                    logger.info(\"generate_dynamic_system_message: Formatted history parts were empty.\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:104:    logger.info(f\"generate_dynamic_system_message: Generation prompt for LLM: {json.dumps(generation_prompt_messages, indent=2)}\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:107:        logger.info(\"generate_dynamic_system_message: Calling LLM to generate dynamic system message.\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:116:                logger.info(f\"generate_dynamic_system_message: Generated and escaped dynamic message: '{dynamic_system_msg}'\")\n/app/src/clients/qdrant_utils/dynamic_system_message.py:124:        logger.info(\"generate_dynamic_system_message: Falling back to base system message due to LLM call error.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:36:    logger.info(\n/app/src/clients/qdrant_utils/summarize_conv_history.py:61:            logger.info(f\"summarize_conversation_history: Retrieved {len(history_messages_for_prompt)} messages.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:65:        logger.info(\"summarize_conversation_history: No conversation history found.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:69:        logger.info(\"summarize_conversation_history: Extracted message list for prompt is empty.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:78:    logger.info(f\"summarize_conversation_history: Sending {len(llm_messages)} messages to LLM.\")\n/app/src/clients/qdrant_utils/summarize_conv_history.py:84:            logger.info(f\"summarize_conversation_history: LLM generated summary. Length: {len(summary)}\")\n/app/src/clients/qdrant_utils/get_similar_messages.py:10:    logger.info(\"IN QDRANT SIMILAR MESSAGE\")\n/app/src/clients/qdrant_utils/get_conv_history.py:49:                            logger.info(\"Filtering 'Workflow execution failed' from Qdrant history.\")\n/app/src/clients/qdrant_utils/get_conv_history.py:55:                logger.info(f\"Fetched {len(history_messages)} messages from Qdrant for RAG context.\")\n/app/src/clients/qdrant_utils/get_conv_history.py:57:                logger.info(f\"No conversation history found in Qdrant for user '{username}', convo_id '{conversation_id}'.\")\n/app/src/clients/qdrant_utils/get_conv_history.py:62:        logger.info(\"Username or conversation_id missing, skipping Qdrant history fetch.\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:21:    logger.info(f\"get_relevant_tool_objects_from_qdrant: Fetching for query: '{query_text[:50]}...' (k={k})\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:30:        logger.info(\"get_relevant_tool_objects_from_qdrant: No relevant function schemas found in Qdrant.\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:37:    logger.info(\"get_relevant_tool_objects_from_qdrant: Listing all found tool candidates and their Qdrant scores:\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:97:            logger.info(f\"get_relevant_tool_objects_from_qdrant: Reconstructed Tool '{name}' with Qdrant score: {score:.4f} ({score*100:.2f}%)\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:99:            logger.info(f\"get_relevant_tool_objects_from_qdrant: Reconstructed Tool '{name}' (score not available from Qdrant item)\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:117:            logger.info(f\"get_relevant_tool_objects_from_qdrant: Filesystem intent detected, returning only Filesystem tools: {[t.name for t in fs_tools]}\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:120:            logger.info(\"get_relevant_tool_objects_from_qdrant: Filesystem intent detected, but no Filesystem tools found in Qdrant results.\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:123:    logger.info(f\"get_relevant_tool_objects_from_qdrant: Reconstructed {len(all_tool_objects)} relevant Tool objects from Qdrant.\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:128:        logger.info(f\"get_relevant_tool_objects_from_qdrant: Statistics for {len(all_tool_objects)} reconstructed tools - Min Score: {min_score:.4f}, Max Score: {max_score:.4f}, Avg Score: {avg_score:.4f}\")\n/app/src/clients/qdrant_utils/get_qdrant_tools.py:130:        logger.info(f\"get_relevant_tool_objects_from_qdrant: Reconstructed {len(all_tool_objects)} tools, but no scores were available for statistics.\")\n/app/src/clients/qdrant_utils/code_search.py:49:        logger.info(f\"Collection '{collection_name}' already exists.\")\n/app/src/clients/qdrant_utils/code_search.py:51:        logger.info(f\"Collection '{collection_name}' not found. Creating...\")\n/app/src/clients/qdrant_utils/code_search.py:60:        logger.info(f\"Collection '{collection_name}' created.\")\n/app/src/clients/qdrant_utils/code_search.py:69:    logger.info(f\"Discovered {len(code_files)} code files.\")\n/app/src/clients/qdrant_utils/code_search.py:122:    logger.info(f\"Processing file: {file_path}\")\n/app/src/clients/qdrant_utils/code_search.py:187:            logger.info(f\"Indexed {len(points_to_upsert)} chunks from {file_path}\")\n/app/src/clients/qdrant_utils/code_search.py:216:    logger.info(\"Codebase indexing complete.\")\n/app/src/clients/qdrant_utils/code_search.py:228:    logger.info(f\"\\nSearching for: '{query}'\")\n/app/src/clients/qdrant_utils/code_search.py:305:    logger.info(\"\\n--- Example Searches ---\")\n/app/src/clients/qdrant_utils/code_search.py:323:            logger.info(f\"Top results for '{q}':\")\n/app/src/clients/qdrant_utils/code_search.py:325:                logger.info(f\"  {res_idx+1}. Score: {res['score']:.4f}, File: {res.get('file_path', 'N/A')}, Identifier: {res.get('identifier', 'N/A')}\")\n/app/src/clients/qdrant_utils/code_search.py:326:                logger.info(f\"     Code Snippet (first 80 chars): {res.get('code_chunk', '')[:80].replace(os.linesep, ' ')}...\")\n/app/src/clients/qdrant_utils/code_search.py:328:            logger.info(f\"No results found for '{q}'.\")\n/app/src/clients/qdrant_utils/code_search.py:329:        logger.info(\"-\" * 20)\n/app/src/clients/qdrant_utils/code_search.py:334:        logger.info(f\"\\nCollection '{CODEBASE_COLLECTION_NAME}' info: Points count = {collection_info.points_count}\")\n/app/src/clients/mcpClient.py:30:        logger.info(f\"ChatSession initialized. Managing {len(self.servers)} servers.\")\n/app/src/clients/mcpClient.py:39:        #logger.info(f\"ChatSession: Initializing {len(self.servers)} MCP servers...\")\n/app/src/clients/mcpClient.py:52:        #logger.info(f\"ChatSession: Finished server initialization. {initialized_count}/{len(self.servers)} servers initialized.\")\n/app/src/clients/mcpClient.py:68:            #logger.info(\"ChatSession.cleanup_servers: No servers to clean up.\")\n/app/src/clients/mcpClient.py:71:        logger.info(f\"ChatSession: Cleaning up {len(self.servers)} MCP servers...\")\n/app/src/clients/mcpClient.py:74:        #logger.info(\"ChatSession: Finished server cleanup.\")\n/app/src/clients/mcpClient.py:80:        #logger.info(\"MCPClient: Environment variables loaded.\")\n/app/src/clients/mcpClient.py:82:        logger.info(f'MCPClient: Server configuration loaded: {self.server_config}')\n/app/src/clients/mcpClient.py:88:        logger.info(\"MCPClient: Qdrant client initialized.\")\n/app/src/clients/mcpClient.py:90:        # logger.info(\"MCPClient: ChatSession initialized.\")\n/app/src/clients/mcpClient.py:96:        #logger.info(\"MCPClient.list_tools_from_all_servers: Starting to list tools.\")\n/app/src/clients/mcpClient.py:108:                logger.info(f\"MCPClient.list_tools_from_all_servers: Server '{server.name}' (Index {server_idx}) returned {len(tools_from_server)} tools: {[tool.name for tool in tools_from_server]}\")\n/app/src/clients/mcpClient.py:114:        logger.info(f\"MCPClient.list_tools_from_all_servers: Finished. Total tools collected: {len(all_tools)}.\")\n/app/src/clients/mcpClient.py:121:        #logger.info(\"MCPClient: Starting synchronization of tools to Qdrant.\")\n/app/src/clients/mcpClient.py:124:        #logger.info(f\"MCPClient.sync_tools_to_qdrant: Found {len(all_tools)} tools from all servers to potentially sync.\")\n/app/src/clients/mcpClient.py:140:            # logger.info(f\"MCPClient.sync_tools_to_qdrant: Attempted to add/update tool '{tool_obj.name}' to Qdrant.\")\n/app/src/clients/mcpClient.py:142:        #logger.info(f\"MCPClient: Finished synchronizing tools to Qdrant. Attempted to add/update {added_count} tools out of {len(all_tools)} found.\")\n/app/src/clients/mcpClient.py:152:                    logger.info(f\"Executing tool '{tool_name}' on server '{server.name}' with args: {args}\")\n/app/src/clients/mcpClient.py:192:        #logger.info(f\"LOAD: {file_path}\" )\n/app/src/clients/mcpClient.py:194:            #logger.info(f\"LOADED: {f}\" )\n/app/src/clients/mcpClient.py:225:            logger.info(f\"MCPServer {self.name}: Already initialized and session active. Skipping re-initialization.\")\n/app/src/clients/mcpClient.py:228:        logger.info(f\"MCPServer {self.name}: Starting initialization with config: {self.config}\")\n/app/src/clients/mcpClient.py:269:        logger.info(f\"MCPServer {self.name}: Entering _initialize_stdio with command_path: {command_path}\")\n/app/src/clients/mcpClient.py:293:            logger.info(f\"Initializing stdio server {self.name} with command: {command_path} and args: {self.config.get('args', [])}\")\n/app/src/clients/mcpClient.py:294:            # logger.info(f\"Server parameters: {server_params}\")\n/app/src/clients/mcpClient.py:298:            logger.info(f\"MCPServer {self.name}: stdio_client transport established. Read: {read}, Write: {write}\")\n/app/src/clients/mcpClient.py:299:            logger.info(f\"MCPServer {self.name}: Note: Subprocess stderr for '{self.name}' is captured and logged by the 'mcp.client.stdio' logger.\")\n/app/src/clients/mcpClient.py:302:            #logger.info(f\"MCPServer {self.name}: ClientSession context entered. About to call session.initialize().\")\n/app/src/clients/mcpClient.py:304:            #logger.info(f\"MCPServer {self.name}: session.initialize() returned. Verifying session with list_tools...\")\n/app/src/clients/mcpClient.py:308:                # logger.info(f\"MCPServer {self.name}: Session verified successfully with list_tools.\")\n/app/src/clients/mcpClient.py:314:            logger.info(f\"MCPServer {self.name}: Finished _initialize_stdio successfully.\")\n/app/src/clients/mcpClient.py:332:            logger.info(f\"Starting Docker container for server {self.name}: {' '.join(docker_command_parts)}\")\n/app/src/clients/mcpClient.py:341:            logger.info(f\"Docker container {container_name} for server {self.name} started (PID: {process.pid}). Further interaction depends on MCP setup.\")\n/app/src/clients/mcpClient.py:360:            logger.info(f\"Checking HTTP server {self.name} at {url}\")\n/app/src/clients/mcpClient.py:364:            logger.info(f\"HTTP server {self.name} is responsive at {url}.\")\n/app/src/clients/mcpClient.py:385:        logger.info(f\"Listing tools for server {self.name}\")\n/app/src/clients/mcpClient.py:393:            # logger.info(f\"RAW TOOLS : {tools_response}\") # Potentially very verbose\n/app/src/clients/mcpClient.py:409:            logger.info(f\"Successfully listed {len(processed_tools)} tools for server {self.name}.\")\n/app/src/clients/mcpClient.py:453:                logger.info(f\"Executing {tool_name} on server {self.name} (Attempt {attempt_num}/{total_attempts}). Args: {arguments}\")\n/app/src/clients/mcpClient.py:455:                logger.info(f\"Tool {tool_name} executed successfully on server {self.name}.\")\n/app/src/clients/mcpClient.py:475:                    logger.info(f\"Retrying in {delay} seconds...\")\n/app/src/clients/mcpClient.py:491:                    logger.info(f\"Server {self.name}: Session is None and AsyncExitStack has no (or None) callbacks. Assuming already cleaned or not fully initialized.\")\n/app/src/clients/mcpClient.py:495:                    logger.info(f\"Server {self.name}: Session is None, but AsyncExitStack has callbacks. Proceeding with aclose().\")\n/app/src/clients/mcpClient.py:497:            logger.info(f\"MCPServer {self.name}: Starting cleanup. Current session: {self.session is not None}. Exit stack has callbacks: {hasattr(self.exit_stack, '_exit_callbacks') and bool(self.exit_stack._exit_callbacks)}\")\n/app/src/clients/mcpClient.py:501:                logger.info(f\"MCPServer {self.name}: AsyncExitStack.aclose() completed.\")\n/app/src/clients/mcpClient.py:515:                logger.info(f\"MCPServer {self.name}: Cleanup finalized. Session set to None, exit_stack reset.\")\n/app/src/interfaces/BaseService.py:219:             logger.info(\n/app/src/interfaces/Assistant.py:48:        logger.info(f\"CREATE ASSISTANT WITH : {username, assistant_id, language}\")\n/app/src/interfaces/Assistant.py:75:            logger.info(f\"[User] Initialized with username: {username}, system message: {preview}\")\n/app/src/interfaces/Assistant.py:89:            logger.info(f\"constructing user with : {es_system_message}\")\n/app/src/interfaces/Assistant.py:120:            logger.info(f\"[Assistant.chat] Method called. User: {current_username}, Assistant ID: {current_assistant_id}, Conversation ID: {conversation_id}, Message: '{message[:50]}...'\")\n/app/src/interfaces/Assistant.py:124:            logger.info(f\"[Assistant.chat] Fetched assistant_data: {assistant_data}\")\n/app/src/interfaces/Assistant.py:129:            logger.info(f\"[Assistant.chat] Workflow ID for assistant '{current_assistant_id}': {workflow_id}\")\n/app/src/interfaces/Assistant.py:140:                    logger.info(f\"[Assistant.chat] Fetched {len(history_messages_neo4j) if history_messages_neo4j else 0} history messages from Neo4j for workflow.\")\n/app/src/interfaces/Assistant.py:144:                logger.info(f\"[Assistant] Using workflow '{workflow_id}' for response for assistant '{current_assistant_id}'\")\n/app/src/interfaces/Assistant.py:156:                logger.info(f\"[Assistant.chat] Fetched {len(history_messages_qdrant) if history_messages_qdrant else 0} history messages from Qdrant for workflow.\")\n/app/src/interfaces/Assistant.py:194:                logger.info(f\"[Assistant.chat] Executing workflow '{workflow_id}' with input: {json.dumps(workflow_input_data, default=str, indent=2)}\")\n/app/src/interfaces/Assistant.py:199:                logger.info(f\"[Assistant.chat] Workflow '{workflow_id}' execution result object: {workflow_result_obj}\")\n/app/src/interfaces/Assistant.py:216:                logger.info(f\"[Assistant.chat] Workflow '{workflow_id}' processed. Derived final_response_content: '{str(final_response_content)[:100]}...'\")\n/app/src/interfaces/Assistant.py:221:                logger.info(f\"[Assistant.chat] final_response_content is not None. Saving to Qdrant: '{str(final_response_content)[:100]}...'\")\n/app/src/interfaces/Assistant.py:252:                        logger.info(f\"[Assistant.chat] Stored messages in Neo4j for conversation: {conversation_id}\")\n/app/src/interfaces/Assistant.py:295:            logger.info(f\"Generated topic for conversation: '{topic}'\")\n/app/src/interfaces/Assistant.py:303:        logger.info(f\"[User] Looking for initial conversation ID for user: {username}\")\n/app/src/interfaces/Assistant.py:309:                logger.info(f\"[User] Found existing conversation ID: {existing_id}\")\n/app/src/interfaces/Assistant.py:320:                        logger.info(f\"[User] Ensured conversation exists in Neo4j: {existing_id}\")\n/app/src/interfaces/Assistant.py:328:            logger.info(f\"[User] Creating initial conversation ID: {new_conversation_id}\")\n/app/src/interfaces/Assistant.py:339:                    logger.info(f\"[User] Created conversation in Neo4j: {new_conversation_id}\")\n/app/src/interfaces/Assistant.py:346:            logger.info(f\"[User] Error in get_or_create_initial_conversation_id: {e}\")\n/app/src/interfaces/Assistant.py:351:        logger.info(f\"Getting context for {self._username}\")\n/app/src/interfaces/Assistant.py:362:        logger.info(f\"Making train data. Current context type: {type(self._context)}\")\n/app/src/interfaces/Assistant.py:363:        logger.info(f\"Context content: {self._context}\")\n/app/src/interfaces/Assistant.py:367:            logger.info(\"Context is empty!\")\n/app/src/interfaces/Assistant.py:378:            logger.info(f\"Unexpected context type: {type(self._context)}\")\n/app/src/interfaces/Assistant.py:394:                logger.info(f\"Created training pair: {pair_data}\")\n/app/src/interfaces/Assistant.py:397:        logger.info(f\"Final training data count: {len(training_data)}\")\n/app/src/interfaces/Assistant.py:404:        logger.info(\"Preparing training data from conversation context...\")\n/app/src/interfaces/Assistant.py:406:        logger.info(f\"Final training data count: {len(training_data)}\")\n/app/src/interfaces/Assistant.py:413:                logger.info(f\"Invalid message format: {message}. Each message should be a dictionary.\")\n/app/src/interfaces/Assistant.py:416:                logger.info(f\"Message missing 'role' or 'content': {message}\")\n/app/src/scraper/scrape_all_video_urls.py:115:        logger.info(\"Bot initialized.\")\n/app/src/scraper/scrape_all_video_urls.py:130:                    logger.info(\"Clicked 'Allow all cookies' button.\")\n/app/src/utils/tool_utils.py:28:        logger.info(f\"Added {len(dynamically_fetched_tools)} dynamically fetched tools.\")\n/app/src/utils/tool_utils.py:38:        logger.info(f\"Added {num_static_added} unique static tools.\")\n/app/src/utils/request_id_manager.py:58:                logger.info(f\"[RequestIDManager] Cleaned up all requests for SID {sid}\")\n/app/src/utils/request_id_manager.py:96:        logger.info(f\"[AUTH] Setting session for SID: {sid}\")\n/app/src/utils/request_id_manager.py:102:        logger.info(f\"[AUTH] Removing session for SID: {sid}\")\n/app/src/utils/request_id_manager.py:105:            logger.info(f\"[AUTH] Session cleared for SID: {sid}\")\n/app/src/utils/ssl_utils.py:28:            logger.info(f\"Successfully loaded certificate from {cert_file}\")\n/app/src/routes/http/files.py:26:        logger.info(f\"READ PATH : {requested_path}\")\n/app/src/routes/socket/assistant_events.py:34:    logger.info(\"🚀 [SOCKET] Initializing assistant routes\")\n/app/src/routes/socket/assistant_events.py:39:        logger.info(\"====== Get Assistants Request Started ======\")\n/app/src/routes/socket/assistant_events.py:69:        logger.info(f\"====== Get Assistant Request Started ====== {data}\", )\n/app/src/routes/socket/assistant_events.py:122:            logger.info(f\"[CREATE_ASSISTANT] Formatted assistant data: {assistant_data}\")\n/app/src/routes/socket/assistant_events.py:156:            logger.info(f\"[CREATE_ASSISTANT] Successfully created assistant {assistant_data}\")\n/app/src/routes/socket/assistant_events.py:176:            logger.info(\"====== Get Assistant System Message Request Started ======\")\n/app/src/routes/socket/assistant_events.py:178:            logger.info(f\"Getting system message for assistant_id: {assistant_id}\")\n/app/src/routes/socket/assistant_events.py:188:            logger.info(f\"Retrieved system message: {system_message}\")\n/app/src/routes/socket/assistant_events.py:197:            logger.info(f\"Sending system message for {assistant_id}: {system_message}\")\n/app/src/routes/socket/assistant_events.py:230:            logger.info(f\"[UPDATE_ASSISTANT] Updating assistant {assistant_id} with data: {updates}\")\n/app/src/routes/socket/auth_events.py:17:    logger.info(\"🚀 [AUTH_ROUTES] Initializing authentication event routes...\")\n/app/src/routes/socket/auth_events.py:22:        logger.info(f\"🏁 [AUTH_ROUTES] Entered 'connect' handler for SID: {sid}\")\n/app/src/routes/socket/auth_events.py:48:        logger.info(f\"[AUTH_ROUTES] Client connected. SID: {sid}\")\n/app/src/routes/socket/auth_events.py:72:            logger.info(f\"[AUTH_ROUTES] Token refreshed for user: {token_info.get('preferred_username')} with SID: {sid}\")\n/app/src/routes/socket/auth_events.py:81:        logger.info(f\"🏁 [AUTH_ROUTES] Entered 'authenticate' handler for SID: {sid}\")\n/app/src/routes/socket/auth_events.py:104:            logger.info(f\"[AUTH_ROUTES] Client authenticated. SID: {sid}\")\n/app/src/routes/socket/auth_events.py:113:        logger.info(f\"[AUTH_ROUTES] Client disconnected. SID: {sid}\")\n/app/src/routes/socket/auth_events.py:127:        logger.info(f\"[AUTH_ROUTES] Ping received from {sid}\")\n/app/src/routes/socket/auth_events.py:137:        logger.info(f\"[AUTH_ROUTES] Client attempting to reconnect. SID: {sid}\")\n/app/src/routes/socket/auth_events.py:153:            logger.info(f\"[AUTH_ROUTES] Client reconnected. SID: {sid}\")\n/app/src/routes/socket/system_events.py:69:            logger.info(\"====== Get System Message History Request Started ======\")\n/app/src/routes/socket/system_events.py:73:            logger.info(\"Fetching system message changes...\")\n/app/src/routes/socket/system_events.py:76:            logger.info(f\"Retrieved changes: {json.dumps(changes, indent=2)}\")\n/app/src/routes/socket/system_events.py:96:            logger.info(f\"Processing updateSystemMessage data: {data}\")\n/app/src/routes/socket/system_events.py:120:                logger.info(f\"Successfully stored system message for {assistant_id}\")\n/app/src/routes/socket/system_events.py:138:        logger.info(\"====== Update Settings Request Started ======\")\n/app/src/routes/socket/system_events.py:139:        logger.info(f\"Received data: {data}\")\n/app/src/routes/socket/system_events.py:146:            logger.info(f\"Using username: {username}, assistant_id: {assistant_id}\")\n/app/src/routes/socket/system_events.py:153:            logger.info(f\"Retrieving system message for {assistant_id}\")\n/app/src/routes/socket/system_events.py:169:            logger.info(f\"Sending settings update for {assistant_id}\")\n/app/src/routes/socket/workflow_events.py:17:            logger.info(\"Received request to save workflow\")\n/app/src/routes/socket/workflow_events.py:32:            logger.info(f\"Workflow saved successfully with ID: {result['workflow_id']}\")\n/app/src/routes/socket/workflow_events.py:56:            logger.info(\"Received request for workflows\")\n/app/src/routes/socket/workflow_events.py:65:            logger.info(f\"Returning {len(workflows)} workflows\")\n/app/src/routes/socket/workflow_events.py:86:            logger.info(f\"Received request for workflow: {data.get('workflowId')}\")\n/app/src/routes/socket/workflow_events.py:113:            logger.info(f\"Returning workflow: {workflow_id}\")\n/app/src/routes/socket/workflow_events.py:134:            logger.info(f\"Received request to execute workflow: {data.get('workflowId')}\")\n/app/src/routes/socket/workflow_events.py:171:            logger.info(f\"Executing workflow: {workflow_id}\")\n/app/src/routes/socket/workflow_events.py:188:                logger.info(f\"Workflow execution result: {result}\")\n/app/src/routes/socket/workflow_events.py:218:            logger.info(f\"Received request to delete workflow: {data.get('workflowId')}\")\n/app/src/routes/socket/workflow_events.py:245:            logger.info(f\"Workflow deleted: {workflow_id}\")\n/app/src/routes/socket/service_events.py:15:            logger.info(\"Received request for services\")\n/app/src/routes/socket/service_events.py:22:            logger.info(f\"Returning {len(services)} services\")\n/app/src/routes/socket/service_events.py:43:            logger.info(f\"Received request to execute service: {data.get('serviceId')}\")\n/app/src/routes/socket/service_events.py:60:                    logger.info(f\"Service {service_id} executed successfully in background.\")\n/app/src/routes/socket/user_events.py:32:        logger.info(user)\n/app/src/routes/socket/user_events.py:37:        logger.info(user_groups)\n/app/src/routes/socket/user_events.py:38:        logger.info(user_roles)\n/app/src/routes/socket/user_events.py:40:        logger.info(f'Attributes: {attributes}')\n/app/src/routes/socket/user_events.py:55:        logger.info(f\"ASSISTANTS RENDER : {assistants[0].split(',')}\")\n/app/src/routes/socket/user_events.py:99:            logger.info(f\"RAW USERS : {users}\")\n/app/src/routes/socket/user_events.py:114:                    logger.info(f\"[USERS] Processed user: {transformed_user['username']}\")\n/app/src/routes/socket/user_events.py:116:            logger.info(f\"[USERS] Successfully processed {len(transformed_users)} users\")\n/app/src/routes/socket/user_events.py:211:        logger.info(id)\n/app/src/routes/socket/user_events.py:212:        logger.info(assistants)\n/app/src/routes/socket/user_events.py:213:        logger.info(f\"IN CUSTOM ATT: {data}\" )\n/app/src/routes/socket/user_events.py:217:        logger.info(user)\n/app/src/routes/socket/user_events.py:224:        logger.info(list)\n/app/src/routes/socket/user_events.py:228:        logger.info(list)\n/app/src/routes/socket/user_events.py:234:            logger.info(f\"Successfully updated {joint_list} for user {id}\")\n/app/src/routes/socket/user_events.py:237:            logger.info(\"Failed to update user attribute:\", e)\n/app/src/routes/socket/user_events.py:247:        logger.info(id)\n/app/src/routes/socket/user_events.py:248:        logger.info(assistant)\n/app/src/routes/socket/user_events.py:249:        logger.info(f\"IN UNASSIGN ATT: {data}\" ) # Corrected log message\n/app/src/routes/socket/user_events.py:260:        logger.info(list)\n/app/src/routes/socket/user_events.py:264:        logger.info(list)\n/app/src/routes/socket/user_events.py:270:            logger.info(f\"Successfully updated '{joint_list}' for user {id}\")\n/app/src/routes/socket/user_events.py:273:            logger.info(\"Failed to update user attribute:\", e)\n/app/src/routes/socket/model_events.py:60:        logger.info(f\"[MODEL] Model {model_id} set for assistant {assistant_id}\")\n/app/src/routes/socket/model_events.py:79:    logger.info(f\"Received deleteModel request: {data}\")\n/app/src/routes/socket/model_events.py:101:            logger.info(f\"[MODEL_DELETE] Successfully deleted model: {model_id_to_delete}\")\n/app/src/routes/socket/model_events.py:127:    logger.info(\"Received getModels request\")\n/app/src/routes/socket/model_events.py:159:        logger.info(f\"[MODEL] Base models prepared: {len(base_models)} models\")\n/app/src/routes/socket/model_events.py:163:            logger.info(\"[MODEL] Attempting to fetch custom models from OpenAI\")\n/app/src/routes/socket/model_events.py:170:            logger.info(\"[MODEL] Calling OpenAI API to list fine-tuned models\")\n/app/src/routes/socket/model_events.py:187:            logger.info(f\"[MODEL] Total models prepared: {len(all_models)} ({len(base_models)} base, {len(trained_models)} custom)\")\n/app/src/routes/socket/model_events.py:193:            logger.info(\"[MODEL] Falling back to base models only due to error\")\n/app/src/routes/socket/model_events.py:227:        logger.info(f\"[MODEL] Current model for assistant {assistant_id}: {current_model}\")\n/app/src/routes/socket/socket_server.py:23:        #     logger.info(f\"[SOCKET_SERVER] Client connected. SID: {sid}\")\n/app/src/routes/socket/socket_server.py:29:        #         logger.info(f\"[SOCKET_SERVER] Client disconnected. SID: {sid}\")\n/app/src/routes/socket/socket_server.py:49:                    logger.info(f\"[SOCKET_SERVER] IN HANDLE EVENT: {data}\")\n/app/src/routes/socket/socket_server.py:74:                    logger.info(f\"SocketServer: Handler for '{event_name}' (SID: {sid}) executed. Result type: {type(result)}\")\n/app/src/routes/socket/socket_server.py:100:            # logger.info(f\"[SOCKET_SERVER] Registered event: {event_name}\")\n/app/src/routes/socket/socket_server.py:109:        logger.info(f\"[SOCKET_SERVER] Re-registering event handlers... {self.event_handlers.keys()}\")\n/app/src/routes/socket/socket_server.py:112:            logger.info(f\"[SOCKET_SERVER] Re-registered handler for event: {event_name}\")\n/app/src/routes/socket/socket_server.py:120:                logger.info(f\"[SOCKET_SERVER] IN Emitting: {data}\")\n/app/src/routes/socket/socket_server.py:138:                logger.info(f\"[SOCKET_SERVER] Emitting getServices: {response}\")\n/app/src/routes/socket/function_events.py:17:            logger.info(\"Received request for functions\")\n/app/src/routes/socket/function_events.py:27:            logger.info(f\"Returning {len(functions_data)} functions\")\n/app/src/routes/socket/function_events.py:52:            logger.info(f\"Received request to execute function with query: {function_query}\")\n/app/src/routes/socket/function_events.py:81:                    logger.info(f\"Function '{function_query}' executed in background. Result: {result_obj.message}\")\n/app/src/routes/socket/chat_events.py:48:            logger.info(f\"[CHAT] Received message request for SID: {sid} with data: {data}\")\n/app/src/routes/socket/chat_events.py:78:            #     logger.info(\"[CHAT] No conversation_id provided, creating new conversation\")\n/app/src/routes/socket/chat_events.py:82:            logger.info(f\"[CHAT_HANDLER] Calling assistant.chat for SID: {sid}, User: {username}, Assistant: {assistant_id}, Convo: {conversation_id}, Msg ID: {message_id}\")\n/app/src/routes/socket/chat_events.py:89:            logger.info(f\"[CHAT_HANDLER] Raw response from assistant.chat for SID {sid}: '{ai_response_content}' (Type: {type(ai_response_content)})\")\n/app/src/routes/socket/chat_events.py:109:                    logger.info(f\"Conversation {conversation_id} has {len(messages_history)} messages. Checking for topic generation.\")\n/app/src/routes/socket/chat_events.py:112:                        logger.info(f\"No existing topic for {conversation_id}. Generating new topic.\")\n/app/src/routes/socket/chat_events.py:116:                            logger.info(f\"Successfully generated and stored topic '{new_topic}' for conversation {conversation_id}\")\n/app/src/routes/socket/chat_events.py:118:                        logger.info(f\"Topic already exists for conversation {conversation_id} in Qdrant: '{existing_topic}'\")\n/app/src/routes/socket/chat_events.py:137:            logger.info(f\"[CHAT_HANDLER] Prepared data for emission to SID {sid}: {json.dumps(final_return_for_socket_server, indent=2)}\")\n/app/src/routes/socket/chat_events.py:152:            logger.info(f\"[DEBUG] Received loadInitialMessages request with data: {data}\")\n/app/src/routes/socket/chat_events.py:158:            logger.info(f\"[CHAT] Loading initial messages for user: {username}, assistant: {assistant_id}, conversation: {conversation_id}\")\n/app/src/routes/socket/chat_events.py:183:            logger.info(f\"QDRANT : {qdrantMessages}\")\n/app/src/routes/socket/chat_events.py:209:            logger.info((f\"NEW CONVERSATION : {username} {assistant_id}\"))\n/app/src/routes/socket/chat_events.py:236:        logger.info(f\"[CHAT] Received getChatHistory request with data: {data}\")\n/app/src/routes/socket/chat_events.py:252:            logger.info(f\"MESSAGES {msg_qdr}\")\n/app/src/routes/socket/chat_events.py:284:            logger.info(f\"[DELETE] Attempting to delete chat with ID: {chat_id}\")\n/app/src/routes/socket/chat_events.py:299:                logger.info(f\"[DELETE] Deleted {result.get('deleted', 0)} messages\")\n/app/src/routes/socket/chat_events.py:312:                    logger.info(f\"[DELETE] Deleted assistant {chat_id} from assistants index\")\n/app/src/routes/socket/chat_events.py:333:        logger.info(f\"[CHAT] Received delete conversation request with data: {json.dumps(data, indent=2)}\")\n/app/src/routes/socket/chat_events.py:347:            logger.info(f\"[CHAT] Delete operation completed. Success: {success}\")\n/app/src/routes/socket/chat_events.py:371:            logger.info(f\"[Assistants] Received request for all assistants from {request_id}\")\n/app/src/routes/socket/chat_events.py:388:            logger.info(f\"[Assistants] Found assistants: {assistant_data}\")\n/app/src/routes/socket/avatar_routes.py:26:            logger.info(\"[AVATAR] Starting avatar upload process\")\n/app/src/routes/socket/avatar_routes.py:70:                logger.info(f\"[AVATAR] Successfully saved avatar for user: {username}\")\n/app/src/routes/socket/avatar_routes.py:99:            logger.info(\"[AVATAR] Starting assistant avatar upload process\")\n/app/src/routes/socket/avatar_routes.py:128:                    logger.info(f\"[AVATAR] Successfully saved/updated assistant avatar for: {ai_user}\")\n/app/src/routes/socket/avatar_routes.py:181:                        logger.info(f\"[AVATAR] No assistant avatar found for {username}\")\n/app/src/routes/socket/avatar_routes.py:203:                        logger.info(f\"[AVATAR] No user avatar found for {username}\")\n/app/src/routes/socket/avatar_routes.py:238:            logger.info(f\"[AVATAR] Received batch avatar request for {len(usernames)} users\")\n/app/src/routes/socket/avatar_routes.py:291:                logger.info(f\"[AVATAR] Created index: {index}\")\n/app/src/elastic/core/connection.py:47:                    logger.info(\"Successfully connected to Elasticsearch\")\n/app/src/elastic/elastic.py:18:            logger.info(\"Using injected Elasticsearch client.\")\n/app/src/elastic/elastic.py:26:        logger.info(\"Initializing Elasticsearch connection\")\n/app/src/elastic/elastic.py:53:                    logger.info(\"Successfully connected to Elasticsearch\")\n/app/src/elastic/elastic.py:60:                logger.info(f\"Retrying in {retry_delay}s...\")\n/app/src/elastic/elastic.py:77:            logger.info(f\"[Elastic] Attempting to index document to '{index}'\")\n/app/src/elastic/elastic.py:85:            logger.info(f\"[Elastic] Successfully indexed document: {result}\")\n/app/src/elastic/elastic.py:119:            logger.info(f\"[Elastic] Attempting to retrieve avatar from '{index_name}' for user '{username}' using query: {json.dumps(query)}\")\n/app/src/elastic/elastic.py:124:                logger.info(f\"[Elastic] Avatar found for '{username}' in '{index_name}'.\")\n/app/src/elastic/elastic.py:127:                logger.info(f\"[Elastic] Avatar not found for '{username}' in '{index_name}'.\")\n/app/src/elastic/operations/assistants/Assistant.py:25:            logger.info(\"🤖 [ASSISTANTS] Fetching all assistants from Qdrant\")\n/app/src/elastic/operations/assistants/Assistant.py:31:            logger.info(f\"✅ [ASSISTANTS] Successfully fetched {len(assistants)} assistants from Qdrant\")\n/app/src/elastic/operations/assistants/Assistant.py:40:        logger.info(\"IN STORE ASSISTANT\")\n/app/src/elastic/operations/assistants/Assistant.py:45:                logger.info(f\"Assistant {assistant_data.get('id')} stored successfully in Qdrant\")\n/app/src/elastic/operations/assistants/Assistant.py:66:                logger.info(f\"[Qdrant] Found message by ID: {assistant_id} {message}\")\n/app/src/elastic/operations/assistants/Assistant.py:82:                logger.info(f\"[Qdrant] System message stored successfully for assistant: {assistant_id}\")\n/app/src/elastic/operations/assistants/Assistant.py:91:            logger.info(f\"[Qdrant] Starting model update for assistant {assistant_id} to model {model}\")\n/app/src/elastic/operations/assistants/Assistant.py:101:            logger.info(f\"[Qdrant] Current model: {assistant.get('model', 'not set')}\")\n/app/src/elastic/operations/assistants/Assistant.py:102:            logger.info(f\"[Qdrant] New model: {model}\")\n/app/src/elastic/operations/assistants/Assistant.py:112:                logger.info(f\"[Qdrant] Assistant model updated successfully\")\n/app/src/elastic/operations/assistants/Assistant.py:122:                logger.info(f\"[Elastic] Attempting to index document to '{index}'\")\n/app/src/elastic/operations/assistants/Assistant.py:123:                logger.info(f\"[Elastic] Document content: {json.dumps(document, indent=2)}\")\n/app/src/elastic/operations/assistants/Assistant.py:135:                logger.info(f\"[Elastic] Successfully indexed document to '{index}'\")\n/app/src/elastic/operations/assistants/Assistant.py:136:                logger.info(f\"[Elastic] Response: {json.dumps(response_dict, indent=2)}\")\n/app/src/elastic/operations/assistants/Assistant.py:146:            logger.info(f\"[Elastic] Getting chat history - username: {username}, assistant: {assistant_id}, conversationId: {conversation_id}\")\n/app/src/elastic/operations/assistants/Assistant.py:159:            logger.info(f\"GET CHAT HISTORY FOR : {username, assistant_id, conversation_id}\" )\n/app/src/elastic/operations/assistants/Assistant.py:176:                logger.info(f\"DEBUG GET History {source},{conv_id}\")\n/app/src/elastic/operations/assistants/Assistant.py:208:            logger.info(f\"Sort: {conversations}\" )\n/app/src/elastic/operations/assistants/Assistant.py:211:                logger.info (f\"CONV {conv}\")\n/app/src/elastic/operations/assistants/Assistant.py:215:            logger.info(f\"[Elastic] Returning {len(conversations)} conversations\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:61:            logger.info(f\"[Elastic] Setting system message for {assistant_id}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:62:            logger.info(f\"[Elastic] Connection info: {self.es.info()}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:68:            logger.info(f\"[Elastic] Index {index_name} exists: {index_exists}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:82:                logger.info(f\"[Elastic] Create index response: {create_response}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:99:                logger.info(f\"[Elastic] Index response: {index_response}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:107:                logger.info(f\"[Elastic] Refresh response: {refresh_response}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:118:                logger.info(f\"[Elastic] Get response: {get_response}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:166:            logger.info(f\"[Elastic] DEBUG: Inspecting index for assistant_id: {assistant_id}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:170:            logger.info(f\"[Elastic] Index {self.SYSTEM_MESSAGES_INDEX} exists: {exists}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:175:                logger.info(f\"[Elastic] Index info: {index_info}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:186:                logger.info(f\"[Elastic] Total documents: {result['hits']['total']['value']}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:187:                logger.info(f\"[Elastic] Sample documents: {result['hits']['hits']}\")\n/app/src/elastic/operations/assistants/AssistantSystemMessages.py:201:                logger.info(f\"[Elastic] Documents for assistant_id {assistant_id}: {specific_result['hits']['hits']}\")\n/app/src/elastic/setup_indices.py:20:        #     logger.info(\"[Setup] No assistants found. Initializing default assistants...\")\n/app/src/elastic/setup_indices.py:40:        #             logger.info(f\"[Setup] Initialized default assistant: {default['id']}\")\n/app/src/elastic/setup_indices.py:46:        #     logger.info(\"[Setup] Assistants already exist in Elasticsearch\")\n/app/src/elastic/setup_indices.py:173:                logger.info(f\"[Setup] Creating {index_name} index\")\n/app/src/elastic/setup_indices.py:175:                logger.info(f\"[Setup] Created {index_name} index\")\n/app/src/elastic/setup_indices.py:181:                    logger.info(f\"[Setup] Updated mapping for existing index {index_name}\")\n/app/src/elastic/setup_indices.py:201:            logger.info(\"Created 'models' index\")\n/app/src/elastic/setup_indices.py:222:            logger.info(\"[Setup] Initializing default users...(not implemented)\")\n/app/src/elastic/setup_indices.py:225:        logger.info(\"[Setup] All indices created and verified successfully\")\n/app/src/elastic/setup_indices.py:260:            logger.info(\"[Setup] Created assistants index with mapping\")\n/app/src/elastic/setup_indices.py:264:            logger.info(\"[Setup] Updated assistants index mapping\")\n/app/src/elastic/init_elastic.py:13:    logger.info(\"[Main] Verifying Elasticsearch indices...\")\n/app/src/elastic/init_elastic.py:32:            logger.info(f\"[Main] Index '{index}' exists: {exists}\")\n"]]}, "timestamp": "2025-06-30T22:48:40.029711"}]