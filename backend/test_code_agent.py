#!/usr/bin/env python3
"""
Test script for the LangChain Code Agent.
This script tests the basic functionality of the enhanced code agent.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.implementations.langchain_text_service import LangChainService
from managers.FunctionsManager import FunctionsManager
from agent.code_agent_preprocessing import detect_code_intent, extract_code_entities


async def test_code_intent_detection():
    """Test the code intent detection functionality."""
    print("🧪 Testing Code Intent Detection...")
    
    test_messages = [
        "Ich möchte einen Code Agent erstellen",
        "Debug this Python function that's not working",
        "Analyze the structure of my project",
        "Create a new REST API endpoint",
        "Refactor this messy code",
        "Write unit tests for my function",
        "Find all async functions in the codebase",
        "Show me the git history for this file"
    ]
    
    for message in test_messages:
        intent_analysis = detect_code_intent(message)
        print(f"Message: '{message}'")
        print(f"  Primary Intent: {intent_analysis['primary_intent']}")
        print(f"  All Intents: {intent_analysis['intents']}")
        print(f"  Code Related: {intent_analysis['is_code_related']}")
        print(f"  Entities: {intent_analysis['entities']}")
        print()


async def test_entity_extraction():
    """Test the entity extraction functionality."""
    print("🔍 Testing Entity Extraction...")
    
    test_texts = [
        "Check the file backend/src/services/implementations/langchain_text_service.py",
        "The function calculate_score() is throwing a ValueError",
        "Look at the UserManager class in the auth module",
        "Error: TypeError: 'NoneType' object is not subscriptable",
        "Write a Python function to handle API requests",
        "The git log shows changes in main.py and config.json"
    ]
    
    for text in test_texts:
        entities = extract_code_entities(text)
        print(f"Text: '{text}'")
        for entity_type, values in entities.items():
            if values:
                print(f"  {entity_type}: {values}")
        print()


async def test_service_initialization():
    """Test the LangChain Code Agent service initialization."""
    print("⚙️ Testing Service Initialization...")
    
    try:
        # Initialize FunctionsManager (required dependency)
        functions_manager = FunctionsManager.get_instance()
        
        # Create the LangChain Code Agent service
        service = LangChainService(functions_manager=functions_manager)
        
        print(f"✅ Service created successfully!")
        print(f"  Service ID: {service.id}")
        print(f"  Service Name: {service.name}")
        print(f"  Service Category: {service.category}")
        print(f"  Service Icon: {service.icon}")
        print(f"  Service Description: {service.description}")
        
        # Test service configuration
        config_schema = await service.get_config_schema()
        print(f"  Config Schema: {config_schema}")
        
        return service
        
    except Exception as e:
        print(f"❌ Service initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_backward_compatibility():
    """Test backward compatibility with old service ID."""
    print("🔄 Testing Backward Compatibility...")
    
    try:
        # Test creating service with old ID
        functions_manager = FunctionsManager.get_instance()
        service = LangChainService(functions_manager=functions_manager, service_id='langchain_service')
        
        print(f"✅ Backward compatibility works!")
        print(f"  Requested ID: 'langchain_service'")
        print(f"  Actual ID: {service.id}")
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {e}")


async def main():
    """Run all tests."""
    print("🚀 Starting LangChain Code Agent Tests\n")
    
    # Test 1: Code Intent Detection
    await test_code_intent_detection()
    
    # Test 2: Entity Extraction
    await test_entity_extraction()
    
    # Test 3: Service Initialization
    service = await test_service_initialization()
    
    # Test 4: Backward Compatibility
    await test_backward_compatibility()
    
    print("✨ All tests completed!")
    
    if service:
        print(f"\n🎉 LangChain Code Agent is ready to use!")
        print(f"Service ID: {service.id}")
        print(f"Available tools will be loaded when the service is fully initialized.")


if __name__ == "__main__":
    asyncio.run(main())
