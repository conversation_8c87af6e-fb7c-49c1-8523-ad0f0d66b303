#!/usr/bin/env python3
"""
Quick fix for the asdf assistant to add workflow ID.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qdrant.operations.assistants import get_assistant_manager
from qdrant.operations.workflows import get_workflow_manager


async def main():
    """Fix the asdf assistant."""
    print("🔧 Quick Fix for 'asdf' Assistant")
    
    try:
        # Get managers
        assistant_manager = get_assistant_manager()
        workflow_manager = get_workflow_manager()
        
        # Step 1: Create default workflow
        print("1. Creating default workflow...")
        default_workflow = {
            "id": "default-assistant-workflow",
            "name": "Default Assistant Workflow",
            "description": "Default workflow for assistants using the LangChain Code Agent",
            "blocks": [
                {
                    "id": "langchain-block",
                    "type": "service",
                    "name": "LangChain Code Agent",
                    "serviceId": "langchain_code_agent",
                    "x": 200,
                    "y": 150,
                    "config": {}
                }
            ],
            "connections": []
        }
        
        result = await workflow_manager.save_workflow(default_workflow)
        if result.get('success'):
            print(f"✅ Workflow created: {result['workflow_id']}")
        else:
            print("⚠️  Workflow might already exist")
        
        # Step 2: Fix the asdf assistant
        print("2. Fixing 'asdf' assistant...")
        assistant = assistant_manager.get_assistant("asdf")
        
        if assistant:
            print(f"Found assistant: {assistant}")
            
            # Add workflow ID
            assistant['workflowId'] = 'default-assistant-workflow'
            
            # Update assistant
            success = await assistant_manager.store_assistant(assistant)
            if success:
                print("✅ Assistant updated successfully!")
                
                # Verify the update
                updated = assistant_manager.get_assistant("asdf")
                if updated and updated.get('workflowId'):
                    print(f"✅ Verified: workflowId = {updated['workflowId']}")
                    return True
                else:
                    print("❌ Verification failed")
                    return False
            else:
                print("❌ Failed to update assistant")
                return False
        else:
            print("❌ Assistant 'asdf' not found")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🎉 Fix complete! Try sending a message now.")
    else:
        print("\n❌ Fix failed.")
    sys.exit(0 if success else 1)
