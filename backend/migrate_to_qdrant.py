#!/usr/bin/env python3
"""
Migration script to move data from Elasticsearch to Qdrant.
This script migrates assistants, system messages, and workflows.
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from elastic.elastic import Elastic
from qdrant.operations.workflows import get_workflow_manager
from qdrant.operations.assistants import get_assistant_manager


async def migrate_assistants():
    """Migrate assistants from Elasticsearch to Qdrant."""
    print("🤖 Migrating Assistants...")
    
    try:
        # Get Elasticsearch instance
        es = Elastic()
        assistant_manager = get_assistant_manager()
        
        # Check if assistants index exists
        if not es.es.indices.exists(index='assistants'):
            print("ℹ️  No assistants index found in Elasticsearch")
            return True
        
        # Get all assistants from Elasticsearch
        result = es.es.search(
            index='assistants',
            body={
                'query': {'match_all': {}},
                'size': 1000
            }
        )
        
        assistants = []
        for hit in result['hits']['hits']:
            assistant = hit['_source']
            assistants.append(assistant)
        
        print(f"📋 Found {len(assistants)} assistants to migrate")
        
        # Migrate each assistant
        migrated = 0
        for assistant in assistants:
            try:
                success = await assistant_manager.store_assistant(assistant)
                if success:
                    migrated += 1
                    print(f"✅ Migrated assistant: {assistant.get('username', assistant.get('id'))}")
                else:
                    print(f"❌ Failed to migrate assistant: {assistant.get('username', assistant.get('id'))}")
            except Exception as e:
                print(f"❌ Error migrating assistant {assistant.get('username', assistant.get('id'))}: {e}")
        
        print(f"🎉 Successfully migrated {migrated}/{len(assistants)} assistants")
        return migrated == len(assistants)
        
    except Exception as e:
        print(f"❌ Error migrating assistants: {e}")
        return False


async def migrate_system_messages():
    """Migrate system messages from Elasticsearch to Qdrant."""
    print("💬 Migrating System Messages...")
    
    try:
        # Get Elasticsearch instance
        es = Elastic()
        assistant_manager = get_assistant_manager()
        
        # Check if system messages index exists
        if not es.es.indices.exists(index='assistant-system-messages'):
            print("ℹ️  No system messages index found in Elasticsearch")
            return True
        
        # Get all system messages from Elasticsearch
        result = es.es.search(
            index='assistant-system-messages',
            body={
                'query': {'match_all': {}},
                'size': 1000
            }
        )
        
        messages = []
        for hit in result['hits']['hits']:
            message = hit['_source']
            messages.append(message)
        
        print(f"📋 Found {len(messages)} system messages to migrate")
        
        # Migrate each system message
        migrated = 0
        for message in messages:
            try:
                assistant_id = message.get('assistant_id')
                system_message = message.get('system_message')
                username = message.get('username')
                
                if assistant_id and system_message:
                    success = await assistant_manager.set_system_message(
                        assistant_id, system_message, username
                    )
                    if success:
                        migrated += 1
                        print(f"✅ Migrated system message for: {assistant_id}")
                    else:
                        print(f"❌ Failed to migrate system message for: {assistant_id}")
                else:
                    print(f"⚠️  Skipping invalid system message: {message}")
            except Exception as e:
                print(f"❌ Error migrating system message for {assistant_id}: {e}")
        
        print(f"🎉 Successfully migrated {migrated}/{len(messages)} system messages")
        return migrated == len(messages)
        
    except Exception as e:
        print(f"❌ Error migrating system messages: {e}")
        return False


async def migrate_workflows():
    """Migrate workflows from Elasticsearch to Qdrant."""
    print("🔧 Migrating Workflows...")
    
    try:
        # Get Elasticsearch instance
        es = Elastic()
        workflow_manager = get_workflow_manager()
        
        # Check if workflows index exists
        if not es.es.indices.exists(index='workflows'):
            print("ℹ️  No workflows index found in Elasticsearch")
            return True
        
        # Get all workflows from Elasticsearch
        result = es.es.search(
            index='workflows',
            body={
                'query': {'match_all': {}},
                'size': 1000
            }
        )
        
        workflows = []
        for hit in result['hits']['hits']:
            workflow = hit['_source']
            workflows.append(workflow)
        
        print(f"📋 Found {len(workflows)} workflows to migrate")
        
        # Migrate each workflow
        migrated = 0
        for workflow in workflows:
            try:
                result = await workflow_manager.save_workflow(workflow)
                if result.get('success'):
                    migrated += 1
                    print(f"✅ Migrated workflow: {workflow.get('name', workflow.get('id'))}")
                else:
                    print(f"❌ Failed to migrate workflow: {workflow.get('name', workflow.get('id'))}")
            except Exception as e:
                print(f"❌ Error migrating workflow {workflow.get('name', workflow.get('id'))}: {e}")
        
        print(f"🎉 Successfully migrated {migrated}/{len(workflows)} workflows")
        return migrated == len(workflows)
        
    except Exception as e:
        print(f"❌ Error migrating workflows: {e}")
        return False


async def verify_migration():
    """Verify that the migration was successful."""
    print("🔍 Verifying Migration...")
    
    try:
        assistant_manager = get_assistant_manager()
        workflow_manager = get_workflow_manager()
        
        # Check assistants
        assistants = assistant_manager.get_assistants()
        print(f"✅ Found {len(assistants)} assistants in Qdrant")
        
        # Check workflows
        workflows = workflow_manager.list_workflows()
        print(f"✅ Found {len(workflows)} workflows in Qdrant")
        
        # Check system messages for each assistant
        system_messages = 0
        for assistant in assistants:
            message = assistant_manager.get_system_message(assistant['id'])
            if message:
                system_messages += 1
        
        print(f"✅ Found {system_messages} system messages in Qdrant")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying migration: {e}")
        return False


async def main():
    """Run the migration."""
    print("🚀 Starting Elasticsearch to Qdrant Migration\n")
    print("This will migrate:")
    print("- Assistants")
    print("- System Messages") 
    print("- Workflows")
    print("- Avatars (assistant avatars only)")
    print()
    
    # Confirm migration
    response = input("Do you want to proceed? (y/N): ")
    if response.lower() != 'y':
        print("Migration cancelled.")
        return
    
    print("\n" + "="*50)
    print("STARTING MIGRATION")
    print("="*50)
    
    # Run migrations
    migrations = [
        ("Assistants", migrate_assistants),
        ("System Messages", migrate_system_messages),
        ("Workflows", migrate_workflows)
    ]
    
    results = []
    for name, migration_func in migrations:
        print(f"\n{'='*30}")
        print(f"Migrating: {name}")
        print('='*30)
        
        try:
            result = await migration_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} migration failed: {e}")
            results.append((name, False))
    
    # Verify migration
    print(f"\n{'='*30}")
    print("Verifying Migration")
    print('='*30)
    verification_result = await verify_migration()
    
    # Summary
    print(f"\n{'='*50}")
    print("MIGRATION SUMMARY")
    print('='*50)
    
    passed = 0
    for name, result in results:
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    verification_status = "✅ SUCCESS" if verification_result else "❌ FAILED"
    print(f"Verification: {verification_status}")
    
    print(f"\nOverall: {passed}/{len(results)} migrations successful")
    
    if passed == len(results) and verification_result:
        print("\n🎉 Migration completed successfully!")
        print("Your data has been migrated from Elasticsearch to Qdrant.")
        print("\nNext steps:")
        print("1. Test your application to ensure everything works")
        print("2. Consider backing up your Elasticsearch data before removing it")
        print("3. Update any remaining Elasticsearch references in your code")
    else:
        print(f"\n⚠️  Migration had issues. Please check the errors above.")
    
    return passed == len(results) and verification_result


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
