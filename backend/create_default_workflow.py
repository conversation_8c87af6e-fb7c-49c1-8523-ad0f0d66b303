#!/usr/bin/env python3
"""
<PERSON>ript to create a default workflow for new assistants.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qdrant.operations.workflows import get_workflow_manager


async def create_default_workflow():
    """Create a default workflow for assistants."""
    print("🔧 Creating Default Workflow...")
    
    try:
        workflow_manager = get_workflow_manager()
        
        # Define the default workflow
        default_workflow = {
            "id": "default-assistant-workflow",
            "name": "Default Assistant Workflow",
            "description": "Default workflow for new assistants using the LangChain Code Agent",
            "blocks": [
                {
                    "id": "langchain-block",
                    "type": "service",
                    "name": "LangChain Code Agent",
                    "serviceId": "langchain_code_agent",
                    "x": 200,
                    "y": 150,
                    "config": {
                        "history_limit": 15,
                        "enable_code_execution": True,
                        "enable_file_operations": True,
                        "enable_git_operations": True
                    }
                }
            ],
            "connections": []
        }
        
        # Save the workflow
        result = await workflow_manager.save_workflow(default_workflow)
        
        if result.get('success'):
            workflow_id = result['workflow_id']
            print(f"✅ Default workflow created successfully: {workflow_id}")
            
            # Verify it was saved
            retrieved = workflow_manager.get_workflow(workflow_id)
            if retrieved:
                print(f"✅ Workflow verified: {retrieved['name']}")
                return workflow_id
            else:
                print("❌ Failed to verify workflow")
                return None
        else:
            print("❌ Failed to create default workflow")
            return None
            
    except Exception as e:
        print(f"❌ Error creating default workflow: {e}")
        import traceback
        traceback.print_exc()
        return None


async def main():
    """Create the default workflow."""
    print("🚀 Creating Default Workflow for Assistants\n")
    
    workflow_id = await create_default_workflow()
    
    if workflow_id:
        print(f"\n🎉 Default workflow created successfully!")
        print(f"Workflow ID: {workflow_id}")
        print("\nThis workflow will be used for new assistants that don't have a specific workflow assigned.")
        print("You can now create assistants and they will automatically use this workflow.")
    else:
        print("\n❌ Failed to create default workflow.")
        print("Please check the errors above and try again.")
    
    return workflow_id is not None


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
