#!/usr/bin/env python3
"""
Create the default workflow immediately.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qdrant.operations.workflows import get_workflow_manager


async def main():
    """Create the default workflow."""
    print("🔧 Creating Default Workflow...")
    
    try:
        workflow_manager = get_workflow_manager()
        
        # Create the default workflow
        default_workflow = {
            "id": "default-assistant-workflow",
            "name": "Default Assistant Workflow",
            "description": "Default workflow for assistants using the LangChain Code Agent",
            "blocks": [
                {
                    "id": "langchain-block",
                    "type": "service",
                    "name": "LangChain Code Agent",
                    "serviceId": "langchain_code_agent",
                    "x": 200,
                    "y": 150,
                    "config": {}
                }
            ],
            "connections": []
        }
        
        # Save the workflow
        result = await workflow_manager.save_workflow(default_workflow)
        
        if result.get('success'):
            workflow_id = result['workflow_id']
            print(f"✅ Workflow created: {workflow_id}")
            
            # Verify it exists
            retrieved = workflow_manager.get_workflow(workflow_id)
            if retrieved:
                print(f"✅ Verified: {retrieved['name']}")
                print("🎉 Default workflow is ready!")
                return True
            else:
                print("❌ Verification failed")
                return False
        else:
            print("❌ Failed to create workflow")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🎉 Workflow created! Your assistants should work now.")
    else:
        print("\n❌ Failed to create workflow.")
    sys.exit(0 if success else 1)
