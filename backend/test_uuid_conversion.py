#!/usr/bin/env python3
"""
Test script to verify UUID conversion for Qdrant operations.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qdrant.operations.assistants import _string_to_uuid
from qdrant.operations.workflows import _string_to_uuid as workflow_string_to_uuid


def test_uuid_conversion():
    """Test the UUID conversion function."""
    print("🔧 Testing UUID Conversion...")
    
    test_cases = [
        "asdf",
        "test_assistant_123",
        "my-workflow",
        "langchain_code_agent",
        "special!@#$%^&*()characters",
        "very_long_string_that_exceeds_normal_id_length_but_should_still_work_fine"
    ]
    
    for test_id in test_cases:
        # Test assistant UUID conversion
        assistant_uuid = _string_to_uuid(test_id)
        workflow_uuid = workflow_string_to_uuid(test_id)
        
        print(f"Original ID: '{test_id}'")
        print(f"  Assistant UUID: {assistant_uuid}")
        print(f"  Workflow UUID:  {workflow_uuid}")
        
        # Verify it's a valid UUID format
        try:
            import uuid
            uuid.UUID(assistant_uuid)
            uuid.UUID(workflow_uuid)
            print(f"  ✅ Valid UUID format")
        except ValueError:
            print(f"  ❌ Invalid UUID format")
            return False
        
        # Verify deterministic (same input = same output)
        assistant_uuid2 = _string_to_uuid(test_id)
        workflow_uuid2 = workflow_string_to_uuid(test_id)
        
        if assistant_uuid == assistant_uuid2 and workflow_uuid == workflow_uuid2:
            print(f"  ✅ Deterministic conversion")
        else:
            print(f"  ❌ Non-deterministic conversion")
            return False
        
        print()
    
    print("🎉 All UUID conversion tests passed!")
    return True


if __name__ == "__main__":
    success = test_uuid_conversion()
    sys.exit(0 if success else 1)
