# conceptual_code_search.py

import os
import anyio
import sys # Import sys
from pathlib import Path # Import Path
import uuid # Import uuid for generating valid Qdrant point IDs
import ast # For parsing Python code as an example
from typing import List, Dict, Any, Optional, Tuple
from qdrant_client import QdrantClient, models
from qdrant_client.http.models import PointStruct
import logging # Import logging

# Import your project's EmbeddingGenerator
from clients.qdrant import EmbeddingGenerator # Assuming qdrant.py is in a 'clients' directory relative to this script's execution path or PYTHONPATH is set

logger = logging.getLogger(__name__) # Use logging instead of print
logging.basicConfig(level=logging.INFO)

# --- Configuration ---
QDRANT_HOST = os.getenv("QDRANT_HOST", "localhost")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", 6333))
CODEBASE_COLLECTION_NAME = "codebase_embeddings_v1"
# For OpenAI's text-embedding-ada-002, the dimension is 1536
EMBEDDING_DIMENSION = 1536
PROJECT_ROOT = "/home/<USER>/Desktop/chat-ai-agent/backend/src" # IMPORTANT: Set to your project's root
FILE_EXTENSIONS_TO_INDEX = [".py"] # Add other extensions like .js, .java, etc.

# --- Qdrant Client and Embedding Generator ---
qdrant_cli = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
# Use your project's EmbeddingGenerator
# Ensure OPENAI_API_KEY is set in your environment
openai_api_key = os.getenv("OPENAI_API_KEY")
if not openai_api_key:
    logger.error("OPENAI_API_KEY environment variable not found. Please set it before running the script.")
    raise ValueError("OPENAI_API_KEY environment variable not found.") # Raise an exception instead of exiting

# --- Helper Functions ---

def ensure_qdrant_collection_exists_generic(
    client: QdrantClient,
    collection_name: str,
    vector_dimension: int,
    distance_model: models.Distance = models.Distance.COSINE
):
    """Ensures the Qdrant collection for codebase embeddings exists."""
    try:
        client.get_collection(collection_name=collection_name) # This is synchronous
        logger.info(f"Collection '{collection_name}' already exists.")
    except Exception: # More specific exception handling is better
        logger.info(f"Collection '{collection_name}' not found. Creating...")
        client.create_collection( # This is synchronous
            collection_name=collection_name,
            vectors_config=models.VectorParams(size=vector_dimension, distance=distance_model)
        )
        # Create payload indices for potentially useful filtering/sorting
        client.create_payload_index(collection_name=collection_name, field_name="file_path", field_schema="keyword") # Sync
        client.create_payload_index(collection_name=collection_name, field_name="language", field_schema="keyword") # Sync
        client.create_payload_index(collection_name=collection_name, field_name="identifier", field_schema="keyword") # Sync
        logger.info(f"Collection '{collection_name}' created.")

def discover_code_files(root_dir: str, extensions: List[str]) -> List[str]:
    """Discovers code files in the project directory."""
    code_files = []
    for subdir, _, files in os.walk(root_dir):
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                code_files.append(os.path.join(subdir, file))
    logger.info(f"Discovered {len(code_files)} code files.")
    return code_files

def chunk_python_code(file_path: str, file_content: str) -> List[Dict[str, Any]]:
    """
    Chunks Python code into functions and classes using AST.
    Returns a list of dictionaries, each representing a code chunk.
    """
    chunks = []
    try:
        tree = ast.parse(file_content)
        for node in ast.walk(tree):
            chunk_data = None
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                start_line = node.lineno
                # ast.unparse requires Python 3.9+
                # For older versions, you might need to extract source lines manually
                try:
                    # Get the source segment for the node
                    # This is a simplified way; more robust methods exist (e.g., asttokens library)
                    end_line = node.end_lineno if hasattr(node, 'end_lineno') else start_line
                    code_segment_lines = file_content.splitlines()[start_line-1:end_line]
                    code_segment = "\n".join(code_segment_lines)

                    chunk_data = {
                        "identifier": node.name,
                        "type": type(node).__name__,
                        "code_chunk": code_segment,
                        "content": code_segment,  # Added for Qdrant search compatibility
                        "file_path": file_path,
                        "language": "python",
                        "start_line": start_line,
                        "end_line": end_line,
                    }
                except Exception as e_unparse:
                    logger.error(f"Error getting source for node {node.name} in {file_path}: {e_unparse}")
                    continue

            if chunk_data:
                chunks.append(chunk_data)
    except SyntaxError as e:
        logger.error(f"Syntax error parsing {file_path}: {e}")
    except Exception as e:
        logger.error(f"Error chunking Python code in {file_path}: {e}")
    return chunks

async def process_and_index_file(
    qdrant_cli_instance: QdrantClient,
    embedding_gen_instance: EmbeddingGenerator,
    file_path: str,
    collection_name: str
):
    """Reads, chunks, embeds, and indexes a single code file."""
    logger.info(f"Processing file: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return

    chunks = []
    if file_path.endswith(".py"):
        chunks = chunk_python_code(file_path, content)
    # Add elif for other languages with their specific chunking logic
    # else:
    #     # Generic chunking (e.g., by paragraphs or fixed size, less ideal for code)
    #     chunks.append({"code_chunk": content, "file_path": file_path, "language": "unknown", "identifier": os.path.basename(file_path)})

    if not chunks:
        return

    # --- BATCH EMBEDDING (More efficient) ---
    code_to_embed = [chunk["code_chunk"] for chunk in chunks]
    try:
        # Assumes embed_documents exists for batching, which is a common pattern.
        embeddings = embedding_gen_instance.embed_documents(code_to_embed)

        # Check if it's a coroutine and await it
        if hasattr(embeddings, '__await__'):
            embeddings = await embeddings

    except AttributeError:
        # Fallback to single embedding if embed_documents is not available
        logger.warning("EmbeddingGenerator has no 'embed_documents' method, falling back to embedding one by one.")
        embeddings = []
        for text in code_to_embed:
            embedding = embedding_gen_instance.embed_query(text)
            if hasattr(embedding, '__await__'):
                embedding = await embedding
            embeddings.append(embedding)
    except Exception as e:
        logger.error(f"Error during batch embedding for {file_path}: {e}")
        return

    points_to_upsert = []
    for i, chunk_info in enumerate(chunks):
        # Create a unique and deterministic UUID for each chunk.
        # Using NAMESPACE_DNS and a string unique to the chunk ensures that
        # if you re-index the same chunk, it gets the same UUID.
        deterministic_string_for_id = f"{file_path}::{chunk_info.get('identifier', 'chunk')}::{i}"
        chunk_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, deterministic_string_for_id))

        points_to_upsert.append(
            PointStruct(
                id=chunk_id,
                vector=embeddings[i], # Use the pre-computed embedding
                payload=chunk_info # Store all collected chunk info
            )
        )

    if points_to_upsert:
        try:
            qdrant_cli_instance.upsert(
                collection_name=collection_name, # Synchronous
                points=points_to_upsert,
                wait=True
            )
            logger.info(f"Indexed {len(points_to_upsert)} chunks from {file_path}")
        except Exception as e:
            logger.error(f"Error upserting points to Qdrant for {file_path}: {e}")

async def index_codebase(
    qdrant_cli_instance: QdrantClient,
    embedding_gen_instance: EmbeddingGenerator,
    root_dir: str,
    extensions: List[str],
    collection_name: str,
    vector_dimension: int
):
    """
    Indexes the entire codebase.

    Args:
        qdrant_cli_instance: QdrantClient instance for Qdrant operations.
        embedding_gen_instance: EmbeddingGenerator instance for embeddings.
        root_dir: Root directory to search for code files.
        extensions: List of file extensions as strings, e.g. ['.py', '.js']. Must be a list, not a string!
        collection_name: Name of the Qdrant collection.
        vector_dimension: Dimension of the embedding vectors.

    The function will find all files in root_dir matching the given extensions, chunk them, embed them, and store them in Qdrant.
    """
    ensure_qdrant_collection_exists_generic(qdrant_cli_instance, collection_name, vector_dimension)
    code_files = discover_code_files(root_dir, extensions)
    for file_path in code_files:
        await process_and_index_file(qdrant_cli_instance, embedding_gen_instance, file_path, collection_name)
    logger.info("Codebase indexing complete.")

async def search_codebase(
    qdrant_cli_instance: QdrantClient,
    embedding_gen_instance: EmbeddingGenerator,
    query: str,
    collection_name: str,
    top_k: int = 5
) -> List[Dict[str, Any]]:
    """Searches the codebase using a natural language query."""
    if not query:
        return []
    logger.info(f"\nSearching for: '{query}'")
    query_embedding = await embedding_gen_instance.embed_query(query)

    try:
        # The qdrant_client's methods are synchronous, so we run them in a thread
        # to avoid blocking the async event loop.
        bound_search = functools.partial(
            qdrant_cli_instance.search,
            collection_name=collection_name,
            query_vector=query_embedding,
            limit=top_k,
            with_payload=True # Retrieve the payload
        )
        search_results = await anyio.to_thread.run_sync(bound_search)

        results = []
        for hit in search_results:
            payload = hit.payload or {}
            content = payload.get("content")
            # Ensure content is a valid string
            if content is None:
                logger.warning(f"Qdrant search result with id={hit.id} has no content. Skipping.")
                continue
            if not isinstance(content, str):
                logger.warning(f"Qdrant search result with id={hit.id} has non-string content. Skipping.")
                continue
            results.append({
                "score": hit.score,
                "id": hit.id,
                **payload
            })
        return results
    except Exception as e:
        logger.error(f"Error searching Qdrant: {e}")
        return []

# --- Main Execution ---
if __name__ == "__main__":
    # Debug: Print available tools if running as script
    try:
        from servers.mcpservers.filesystem import serve as fs_serve
        print("[DEBUG] Filesystem MCP-Tools:")
        import asyncio
        async def print_fs_tools():
            # Dummy root path for tool listing
            import tempfile
            with tempfile.TemporaryDirectory() as tmpdir:
                async def get_tools():
                    server = await fs_serve(tmpdir)
                    tools = await server.list_tools()
                    for t in tools:
                        print(f" - {t.name}")
                await get_tools()
        asyncio.run(print_fs_tools())
    except Exception as e:
        print(f"[DEBUG] Could not print Filesystem MCP-Tools: {e}")

    # Instantiate client and embedding generator for standalone script execution
    local_qdrant_cli = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
    local_embedding_gen = EmbeddingGenerator(api_key=openai_api_key)

    # 1. Index the codebase (run this once, or when code changes significantly)
    # print("Starting codebase indexing...")
    # index_codebase(
    #     qdrant_cli_instance=local_qdrant_cli,
    #     embedding_gen_instance=local_embedding_gen,
    #     root_dir=PROJECT_ROOT,
    #     extensions=FILE_EXTENSIONS_TO_INDEX,
    #     collection_name=CODEBASE_COLLECTION_NAME,
    #     vector_dimension=EMBEDDING_DIMENSION
    # )
    # print("Finished codebase indexing.")

    # --- Example Searches ---
    # Ensure the collection exists and has been indexed before searching
    ensure_qdrant_collection_exists_generic(local_qdrant_cli, CODEBASE_COLLECTION_NAME, EMBEDDING_DIMENSION)
    
    logger.info("\n--- Example Searches ---")
    queries = [
        "function to handle user login",
        "how are socket events managed for chat messages?",
        "Elasticsearch connection setup",
        "Qdrant client initialization",
        "assistant manager class"
    ]

    for q in queries:
        results = search_codebase(
            qdrant_cli_instance=local_qdrant_cli,
            embedding_gen_instance=local_embedding_gen,
            query=q,
            collection_name=CODEBASE_COLLECTION_NAME,
            top_k=3
        )
        if results:
            logger.info(f"Top results for '{q}':")
            for res_idx, res in enumerate(results):
                logger.info(f"  {res_idx+1}. Score: {res['score']:.4f}, File: {res.get('file_path', 'N/A')}, Identifier: {res.get('identifier', 'N/A')}")
                logger.info(f"     Code Snippet (first 80 chars): {res.get('code_chunk', '')[:80].replace(os.linesep, ' ')}...")
        else:
            logger.info(f"No results found for '{q}'.")
        logger.info("-" * 20)

    # Example of how to check collection info
    try:
        collection_info = local_qdrant_cli.get_collection(collection_name=CODEBASE_COLLECTION_NAME)
        logger.info(f"\nCollection '{CODEBASE_COLLECTION_NAME}' info: Points count = {collection_info.points_count}")
    except Exception as e:
        logger.error(f"Could not get info for collection '{CODEBASE_COLLECTION_NAME}': {e}")
