import logging
from typing import List, Dict, Optional
from datetime import datetime
from type_definitions.assistant import AssistantDocument
from elasticsearch.exceptions import NotFoundError
from qdrant.operations.assistants import get_assistant_manager
import traceback
import uuid
import json


logger = logging.getLogger(__name__)

ASSISTANTS_INDEX = 'assistants'
ASSISTANT_SYSTEM_MESSAGES_INDEX = 'assistant-system-messages'


class AssistantsOperations:
    def get_assistants(self) -> List[Dict]:
        """
        Get all available assistants from Qdrant
        Returns a list of assistant documents
        """
        try:
            logger.info("🤖 [ASSISTANTS] Fetching all assistants from Qdrant")

            # Get assistant manager
            assistant_manager = get_assistant_manager()
            assistants = assistant_manager.get_assistants()

            logger.info(f"✅ [ASSISTANTS] Successfully fetched {len(assistants)} assistants from Qdrant")
            return assistants
                
        except Exception as e:
            logger.error(f"❌ [ASSISTANTS] Error in get_assistants: {str(e)}")
            logger.error(f"[ASSISTANTS] Traceback: {traceback.format_exc()}")
            return []

    async def store_assistant(self, assistant_data: AssistantDocument) -> bool:
        logger.info("IN STORE ASSISTANT")
        try:
            assistant_manager = get_assistant_manager()
            success = await assistant_manager.store_assistant(assistant_data)
            if success:
                logger.info(f"Assistant {assistant_data.get('id')} stored successfully in Qdrant")
            return success
        except Exception as e:
            logger.error(f"Error storing assistant: {str(e)}")
            return False

    def get_assistant(self, assistant_id: str) -> Optional[AssistantDocument]:
        """Get assistant data by ID"""
        try:
            assistant_manager = get_assistant_manager()
            assistant = assistant_manager.get_assistant(assistant_id)
            return assistant
        except Exception as e:
            logger.error(f"Error getting assistant: {str(e)}")
            return None

    def get_system_message(self, assistant_id: str) -> Optional[str]:
        try:
            assistant_manager = get_assistant_manager()
            message = assistant_manager.get_system_message(assistant_id)
            if message:
                logger.info(f"[Qdrant] Found message by ID: {assistant_id} {message}")
            else:
                logger.warning(f"[Qdrant] No system message found for {assistant_id}")
            return message
            return None

        except Exception as e:
            logger.error(f"[Elastic] Error in get_system_message: {str(e)}")
            logger.error(traceback.format_exc())
            return None

    async def set_system_message(self, assistant_id: str, system_message: str, username: Optional[str] = None) -> bool:
        try:
            assistant_manager = get_assistant_manager()
            success = await assistant_manager.set_system_message(assistant_id, system_message, username)
            if success:
                logger.info(f"[Qdrant] System message stored successfully for assistant: {assistant_id}")
            return success
        except Exception as e:
            logger.error(f"[Qdrant] Error in set_system_message: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    async def update_assistant_model(self, assistant_id: str, model: str) -> bool:
        try:
            logger.info(f"[Qdrant] Starting model update for assistant {assistant_id} to model {model}")

            # Get current assistant data from Qdrant
            assistant_manager = get_assistant_manager()
            assistant = assistant_manager.get_assistant(assistant_id)

            if not assistant:
                logger.error(f"[Qdrant] Assistant not found: {assistant_id}")
                return False

            logger.info(f"[Qdrant] Current model: {assistant.get('model', 'not set')}")
            logger.info(f"[Qdrant] New model: {model}")

            # Update the model
            assistant['model'] = model
            assistant['updated_at'] = datetime.utcnow().isoformat()

            # Store the updated assistant
            success = await assistant_manager.store_assistant(assistant)

            if success:
                logger.info(f"[Qdrant] Assistant model updated successfully")
            return success

        except Exception as e:
            logger.error(f"[Qdrant] Error updating assistant model: {str(e)}")
            logger.error(f"[Qdrant] Full traceback: {traceback.format_exc()}")
            return False

    def index_document(self, index: str, document: dict) -> bool:
            try:
                logger.info(f"[Elastic] Attempting to index document to '{index}'")
                logger.info(f"[Elastic] Document content: {json.dumps(document, indent=2)}")
                
                response = self.es.index(index=index, document=document)
                
                # Convert ObjectApiResponse to dict before JSON serialization
                response_dict = {
                    'result': response['result'],
                    '_id': response['_id'],
                    'index': response['_index'],
                    'status': response.meta.status
                }
                
                logger.info(f"[Elastic] Successfully indexed document to '{index}'")
                logger.info(f"[Elastic] Response: {json.dumps(response_dict, indent=2)}")
                return True
                
            except Exception as e:
                logger.error(f"[Elastic] Failed to index document to '{index}': {str(e)}")
                logger.error(f"[Elastic] Full error: {traceback.format_exc()}")
                return False

    def get_chat_history(self, username: str = None, assistant_id: str = None, conversation_id: str = None, size: int = None) -> list:
        try:
            logger.info(f"[Elastic] Getting chat history - username: {username}, assistant: {assistant_id}, conversationId: {conversation_id}")
            
            # Build the query
            query = {
                "size": size or 10,  # Adjust as needed
                "query": {
                    "bool": {
                        "must": []
                    }
                },
                "sort": [{"ts": "desc"}]
            }

            logger.info(f"GET CHAT HISTORY FOR : {username, assistant_id, conversation_id}" )

            if username and username != 'all':
                query["query"]["bool"]["must"].append({"term": {"username.keyword": username}})
            if assistant_id and assistant_id != 'all':
                query["query"]["bool"]["must"].append({"term": {"assistant_id.keyword": assistant_id}})
            if conversation_id:
                query["query"]["bool"]["must"].append({"term": {"conversation_id.keyword": conversation_id}})

            result = self.es.search(index='user-data', body=query)
            
            # Group messages by conversation_id
            conversation_messages = {}
            for hit in result['hits']['hits']:
                source = hit['_source']
                conv_id = source.get('conversation_id')
                
                logger.info(f"DEBUG GET History {source},{conv_id}")
                if not conv_id:
                    conv_id = str(uuid.uuid4())
                    
                if conv_id not in conversation_messages:
                    conversation_messages[conv_id] = {
                        "conversation_id": conv_id,
                        "username": source.get('username'),
                        "timestamp": source.get('ts'),
                        "assistant_id": source.get('assistant_id'),
                        "assistant_name": source.get('assistant_name'),
                        "messages": []
                    }
                
                # Add messages to the conversation
                if 'userMsg' in source:
                    conversation_messages[conv_id]["messages"].append({
                        "role": "user",
                        "content": source['userMsg'],
                        "timestamp": source.get('ts'),
                        "username": source.get('username')
                    })
                if 'userResp' in source:
                    conversation_messages[conv_id]["messages"].append({
                        "role": "assistant",
                        "content": source['userResp'],
                        "timestamp": source.get('ts'),
                        "username": source.get('username')
                    })

            # Convert to list and sort messages within each conversation
            conversations = list(conversation_messages.values())
            logger.info(f"Sort: {conversations}" )
            
            for conv in conversations:
                logger.info (f"CONV {conv}")
                if 'messages' in conv:
                    conv['messages'].sort(key=lambda x: x.get("timestamp", ""))
            
            logger.info(f"[Elastic] Returning {len(conversations)} conversations")
            return conversations

        except Exception as e:
            logger.error(f"[Elastic] Error getting chat history: {str(e)}")
            logger.error(traceback.format_exc())
            return []