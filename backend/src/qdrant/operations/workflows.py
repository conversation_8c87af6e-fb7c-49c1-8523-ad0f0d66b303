"""
Qdrant-based workflow operations to replace Elasticsearch.
"""

import json
import uuid
import hashlib
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from qdrant_client import QdrantClient
from qdrant_client.http.models import (
    Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
)
from clients.qdrant import MyQdrantClient

logger = logging.getLogger(__name__)


def _string_to_uuid(string_id: str) -> str:
    """Convert a string ID to a deterministic UUID."""
    # Create a deterministic UUID from the string using SHA-256 hash
    hash_object = hashlib.sha256(string_id.encode())
    hash_hex = hash_object.hexdigest()
    # Take first 32 characters and format as UUID
    uuid_str = f"{hash_hex[:8]}-{hash_hex[8:12]}-{hash_hex[12:16]}-{hash_hex[16:20]}-{hash_hex[20:32]}"
    return uuid_str


class QdrantWorkflowManager:
    """Manages workflows in Qdrant instead of Elasticsearch."""
    
    def __init__(self, qdrant_client: MyQdrantClient = None):
        self.qdrant_client = qdrant_client or MyQdrantClient()
        self.collection_name = "workflows"
        self.vector_size = 384  # Default embedding size
        self._ensure_collection()
    
    def _ensure_collection(self):
        """Ensure the workflows collection exists."""
        try:
            collections = self.qdrant_client.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                logger.info(f"Creating Qdrant collection: {self.collection_name}")
                self.qdrant_client.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created Qdrant collection: {self.collection_name}")
            else:
                logger.info(f"Qdrant collection already exists: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Error ensuring Qdrant collection {self.collection_name}: {e}")
            raise
    
    async def save_workflow(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """Save a workflow to Qdrant."""
        try:
            # Generate ID if not provided
            if not workflow.get('id'):
                workflow['id'] = str(uuid.uuid4())
            
            # Add timestamp
            workflow['updated_at'] = datetime.now(timezone.utc).isoformat()
            
            # Create a searchable text representation of the workflow
            workflow_text = self._create_workflow_text(workflow)
            
            # Generate embedding for the workflow
            embedding = await self.qdrant_client.embedding_generator.embed(workflow_text)
            
            # Create point for Qdrant with UUID
            point_id = _string_to_uuid(workflow['id'])
            point = PointStruct(
                id=point_id,
                vector=embedding,
                payload={
                    "workflow_data": json.dumps(workflow),
                    "workflow_id": workflow['id'],  # Keep original ID in payload
                    "name": workflow.get('name', ''),
                    "description": workflow.get('description', ''),
                    "updated_at": workflow['updated_at'],
                    "blocks_count": len(workflow.get('blocks', [])),
                    "connections_count": len(workflow.get('connections', [])),
                    "searchable_text": workflow_text
                }
            )
            
            # Store in Qdrant
            self.qdrant_client.client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )
            
            logger.info(f"Workflow saved to Qdrant: {workflow['id']}")
            return {"success": True, "workflow_id": workflow['id']}
            
        except Exception as e:
            logger.error(f"Error saving workflow to Qdrant: {e}")
            raise
    
    def get_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get a workflow by ID from Qdrant."""
        try:
            # Search for the workflow by ID
            search_result = self.qdrant_client.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="workflow_id",
                            match=MatchValue(value=workflow_id)
                        )
                    ]
                ),
                limit=1,
                with_payload=True
            )
            
            if search_result[0]:  # Points found
                point = search_result[0][0]
                workflow_data = json.loads(point.payload["workflow_data"])
                logger.info(f"Retrieved workflow from Qdrant: {workflow_id}")
                return workflow_data
            else:
                logger.warning(f"Workflow not found in Qdrant: {workflow_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving workflow from Qdrant: {e}")
            return None
    
    async def search_workflows(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search workflows by text query."""
        try:
            # Generate embedding for the search query
            query_embedding = await self.qdrant_client.embedding_generator.embed(query)
            
            # Search in Qdrant
            search_results = self.qdrant_client.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit,
                with_payload=True
            )
            
            workflows = []
            for result in search_results:
                workflow_data = json.loads(result.payload["workflow_data"])
                workflow_data["_score"] = result.score
                workflows.append(workflow_data)
            
            logger.info(f"Found {len(workflows)} workflows for query: {query}")
            return workflows
            
        except Exception as e:
            logger.error(f"Error searching workflows in Qdrant: {e}")
            return []
    
    def list_workflows(self, limit: int = 100) -> List[Dict[str, Any]]:
        """List all workflows."""
        try:
            # Scroll through all workflows
            workflows = []
            offset = None
            
            while len(workflows) < limit:
                scroll_result = self.qdrant_client.client.scroll(
                    collection_name=self.collection_name,
                    limit=min(50, limit - len(workflows)),
                    offset=offset,
                    with_payload=True
                )
                
                points, next_offset = scroll_result
                
                if not points:
                    break
                
                for point in points:
                    workflow_data = json.loads(point.payload["workflow_data"])
                    workflows.append(workflow_data)
                
                offset = next_offset
                if next_offset is None:
                    break
            
            # Sort by updated_at descending
            workflows.sort(key=lambda w: w.get('updated_at', ''), reverse=True)
            
            logger.info(f"Listed {len(workflows)} workflows from Qdrant")
            return workflows
            
        except Exception as e:
            logger.error(f"Error listing workflows from Qdrant: {e}")
            return []
    
    def delete_workflow(self, workflow_id: str) -> bool:
        """Delete a workflow from Qdrant."""
        try:
            point_id = _string_to_uuid(workflow_id)
            self.qdrant_client.client.delete(
                collection_name=self.collection_name,
                points_selector=[point_id]
            )
            
            logger.info(f"Deleted workflow from Qdrant: {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting workflow from Qdrant: {e}")
            return False
    
    def _create_workflow_text(self, workflow: Dict[str, Any]) -> str:
        """Create a searchable text representation of the workflow."""
        text_parts = []
        
        # Add basic info
        if workflow.get('name'):
            text_parts.append(f"Name: {workflow['name']}")
        if workflow.get('description'):
            text_parts.append(f"Description: {workflow['description']}")
        
        # Add block information
        blocks = workflow.get('blocks', [])
        for block in blocks:
            if block.get('name'):
                text_parts.append(f"Block: {block['name']}")
            if block.get('type'):
                text_parts.append(f"Type: {block['type']}")
            if block.get('serviceId'):
                text_parts.append(f"Service: {block['serviceId']}")
        
        return " ".join(text_parts)


# Global instance
_workflow_manager = None

def get_workflow_manager() -> QdrantWorkflowManager:
    """Get the global workflow manager instance."""
    global _workflow_manager
    if _workflow_manager is None:
        _workflow_manager = QdrantWorkflowManager()
    return _workflow_manager
