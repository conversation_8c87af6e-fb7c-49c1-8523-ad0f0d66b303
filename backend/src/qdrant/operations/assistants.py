"""
Qdrant-based assistant operations to replace Elasticsearch.
"""

import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from qdrant_client.http.models import (
    Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
)
from clients.qdrant import MyQdrantClient

logger = logging.getLogger(__name__)


class QdrantAssistantManager:
    """Manages assistants and system messages in Qdrant instead of Elasticsearch."""
    
    def __init__(self, qdrant_client: MyQdrantClient = None):
        self.qdrant_client = qdrant_client or MyQdrantClient()
        self.assistants_collection = "assistants"
        self.system_messages_collection = "assistant_system_messages"
        self.avatars_collection = "assistant_avatars"
        self.vector_size = 384  # Default embedding size
        self._ensure_collections()
    
    def _ensure_collections(self):
        """Ensure all assistant-related collections exist."""
        collections_to_create = [
            self.assistants_collection,
            self.system_messages_collection,
            self.avatars_collection
        ]
        
        try:
            existing_collections = self.qdrant_client.client.get_collections()
            existing_names = [col.name for col in existing_collections.collections]
            
            for collection_name in collections_to_create:
                if collection_name not in existing_names:
                    logger.info(f"Creating Qdrant collection: {collection_name}")
                    self.qdrant_client.client.create_collection(
                        collection_name=collection_name,
                        vectors_config=VectorParams(
                            size=self.vector_size,
                            distance=Distance.COSINE
                        )
                    )
                    logger.info(f"Created Qdrant collection: {collection_name}")
                else:
                    logger.info(f"Qdrant collection already exists: {collection_name}")
                    
        except Exception as e:
            logger.error(f"Error ensuring Qdrant collections: {e}")
            raise
    
    async def store_assistant(self, assistant_data: Dict[str, Any]) -> bool:
        """Store assistant data in Qdrant."""
        try:
            assistant_id = assistant_data.get('id')
            if not assistant_id:
                logger.error("Assistant data missing 'id' field")
                return False
            
            # Add timestamp
            assistant_data['updated_at'] = datetime.now(timezone.utc).isoformat()
            
            # Create searchable text
            searchable_text = self._create_assistant_text(assistant_data)
            
            # Generate embedding
            embedding = await self.qdrant_client.embedding_generator.embed(searchable_text)
            
            # Create point
            point = PointStruct(
                id=assistant_id,
                vector=embedding,
                payload={
                    "assistant_data": json.dumps(assistant_data),
                    "assistant_id": assistant_id,
                    "username": assistant_data.get('username', ''),
                    "display_name": assistant_data.get('display_name', ''),
                    "type": assistant_data.get('type', ''),
                    "language": assistant_data.get('language', ''),
                    "model": assistant_data.get('model', ''),
                    "updated_at": assistant_data['updated_at'],
                    "searchable_text": searchable_text
                }
            )
            
            # Store in Qdrant
            self.qdrant_client.client.upsert(
                collection_name=self.assistants_collection,
                points=[point]
            )
            
            logger.info(f"Assistant stored in Qdrant: {assistant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing assistant in Qdrant: {e}")
            return False
    
    def get_assistant(self, assistant_id: str) -> Optional[Dict[str, Any]]:
        """Get assistant data by ID from Qdrant."""
        try:
            search_result = self.qdrant_client.client.scroll(
                collection_name=self.assistants_collection,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="assistant_id",
                            match=MatchValue(value=assistant_id)
                        )
                    ]
                ),
                limit=1,
                with_payload=True
            )
            
            if search_result[0]:  # Points found
                point = search_result[0][0]
                assistant_data = json.loads(point.payload["assistant_data"])
                logger.info(f"Retrieved assistant from Qdrant: {assistant_id}")
                return assistant_data
            else:
                logger.warning(f"Assistant not found in Qdrant: {assistant_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving assistant from Qdrant: {e}")
            return None
    
    def get_assistants(self, language: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get all assistants, optionally filtered by language."""
        try:
            assistants = []
            offset = None
            
            # Build filter
            filter_conditions = []
            if language:
                filter_conditions.append(
                    FieldCondition(
                        key="language",
                        match=MatchValue(value=language)
                    )
                )
            
            scroll_filter = Filter(must=filter_conditions) if filter_conditions else None
            
            while len(assistants) < limit:
                scroll_result = self.qdrant_client.client.scroll(
                    collection_name=self.assistants_collection,
                    scroll_filter=scroll_filter,
                    limit=min(50, limit - len(assistants)),
                    offset=offset,
                    with_payload=True
                )
                
                points, next_offset = scroll_result
                
                if not points:
                    break
                
                for point in points:
                    assistant_data = json.loads(point.payload["assistant_data"])
                    assistants.append(assistant_data)
                
                offset = next_offset
                if next_offset is None:
                    break
            
            # Sort by username
            assistants.sort(key=lambda a: a.get('username', '').lower())
            
            logger.info(f"Retrieved {len(assistants)} assistants from Qdrant")
            return assistants
            
        except Exception as e:
            logger.error(f"Error retrieving assistants from Qdrant: {e}")
            return []
    
    async def set_system_message(self, assistant_id: str, system_message: str, username: str = None) -> bool:
        """Store system message for an assistant in Qdrant."""
        try:
            # Create system message document
            doc = {
                'system_message': system_message,
                'assistant_id': assistant_id,
                'username': username or 'system',
                'updated_at': datetime.now(timezone.utc).isoformat()
            }
            
            # Generate embedding for the system message
            embedding = await self.qdrant_client.embedding_generator.embed(system_message)
            
            # Create point
            point = PointStruct(
                id=assistant_id,  # Use assistant_id as the point ID
                vector=embedding,
                payload={
                    "system_message_data": json.dumps(doc),
                    "assistant_id": assistant_id,
                    "username": username or 'system',
                    "updated_at": doc['updated_at'],
                    "message_text": system_message
                }
            )
            
            # Store in Qdrant
            self.qdrant_client.client.upsert(
                collection_name=self.system_messages_collection,
                points=[point]
            )
            
            logger.info(f"System message stored in Qdrant for assistant: {assistant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing system message in Qdrant: {e}")
            return False
    
    def get_system_message(self, assistant_id: str) -> Optional[str]:
        """Get system message for an assistant from Qdrant."""
        try:
            search_result = self.qdrant_client.client.scroll(
                collection_name=self.system_messages_collection,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="assistant_id",
                            match=MatchValue(value=assistant_id)
                        )
                    ]
                ),
                limit=1,
                with_payload=True
            )
            
            if search_result[0]:  # Points found
                point = search_result[0][0]
                message_data = json.loads(point.payload["system_message_data"])
                logger.info(f"Retrieved system message from Qdrant for assistant: {assistant_id}")
                return message_data.get('system_message')
            else:
                logger.warning(f"System message not found in Qdrant for assistant: {assistant_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving system message from Qdrant: {e}")
            return None
    
    async def store_avatar(self, assistant_id: str, avatar_data: str) -> bool:
        """Store avatar data for an assistant in Qdrant."""
        try:
            doc = {
                'assistant_id': assistant_id,
                'avatar_data': avatar_data,
                'updated_at': datetime.now(timezone.utc).isoformat()
            }
            
            # Generate a simple embedding (just use assistant_id)
            embedding = await self.qdrant_client.embedding_generator.embed(f"avatar for {assistant_id}")
            
            # Create point
            point = PointStruct(
                id=assistant_id,
                vector=embedding,
                payload={
                    "avatar_document": json.dumps(doc),
                    "assistant_id": assistant_id,
                    "updated_at": doc['updated_at']
                }
            )
            
            # Store in Qdrant
            self.qdrant_client.client.upsert(
                collection_name=self.avatars_collection,
                points=[point]
            )
            
            logger.info(f"Avatar stored in Qdrant for assistant: {assistant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing avatar in Qdrant: {e}")
            return False
    
    def get_avatar(self, assistant_id: str) -> Optional[str]:
        """Get avatar data for an assistant from Qdrant."""
        try:
            search_result = self.qdrant_client.client.scroll(
                collection_name=self.avatars_collection,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="assistant_id",
                            match=MatchValue(value=assistant_id)
                        )
                    ]
                ),
                limit=1,
                with_payload=True
            )
            
            if search_result[0]:  # Points found
                point = search_result[0][0]
                avatar_doc = json.loads(point.payload["avatar_document"])
                logger.info(f"Retrieved avatar from Qdrant for assistant: {assistant_id}")
                return avatar_doc.get('avatar_data')
            else:
                logger.info(f"Avatar not found in Qdrant for assistant: {assistant_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving avatar from Qdrant: {e}")
            return None
    
    def _create_assistant_text(self, assistant_data: Dict[str, Any]) -> str:
        """Create searchable text for an assistant."""
        text_parts = []
        
        for field in ['username', 'display_name', 'type', 'language', 'model']:
            if assistant_data.get(field):
                text_parts.append(str(assistant_data[field]))
        
        return " ".join(text_parts)


# Global instance
_assistant_manager = None

def get_assistant_manager() -> QdrantAssistantManager:
    """Get the global assistant manager instance."""
    global _assistant_manager
    if _assistant_manager is None:
        _assistant_manager = QdrantAssistantManager()
    return _assistant_manager
