"""
Chunked file processing for handling large files that exceed token limits.
"""

import os
import logging
from typing import List, Dict, Any, Optional
import re

logger = logging.getLogger(__name__)


class ChunkedFileProcessor:
    """Process large files in chunks to avoid token limits."""
    
    def __init__(self, max_chunk_size: int = 2000):
        """
        Initialize the chunked file processor.
        
        Args:
            max_chunk_size: Maximum number of lines per chunk
        """
        self.max_chunk_size = max_chunk_size
    
    def chunk_file_by_lines(self, file_path: str, chunk_size: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Split a file into chunks by line count.
        
        Args:
            file_path: Path to the file to chunk
            chunk_size: Number of lines per chunk (defaults to max_chunk_size)
            
        Returns:
            List of chunks with metadata
        """
        if chunk_size is None:
            chunk_size = self.max_chunk_size
            
        chunks = []
        
        try:
            if not os.path.exists(file_path):
                return [{"error": f"File not found: {file_path}"}]
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            total_lines = len(lines)
            
            for i in range(0, total_lines, chunk_size):
                chunk_lines = lines[i:i + chunk_size]
                chunk_content = ''.join(chunk_lines)
                
                chunk_info = {
                    "chunk_id": len(chunks) + 1,
                    "file_path": file_path,
                    "start_line": i + 1,
                    "end_line": min(i + chunk_size, total_lines),
                    "total_lines": total_lines,
                    "content": chunk_content,
                    "line_count": len(chunk_lines)
                }
                chunks.append(chunk_info)
            
            logger.info(f"Split {file_path} into {len(chunks)} chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Error chunking file {file_path}: {e}")
            return [{"error": f"Error reading file: {str(e)}"}]
    
    def search_in_chunks(self, file_path: str, search_pattern: str, chunk_size: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Search for a pattern in file chunks.
        
        Args:
            file_path: Path to the file to search
            search_pattern: Pattern to search for
            chunk_size: Number of lines per chunk
            
        Returns:
            List of chunks containing matches
        """
        if chunk_size is None:
            chunk_size = self.max_chunk_size
            
        matching_chunks = []
        chunks = self.chunk_file_by_lines(file_path, chunk_size)
        
        for chunk in chunks:
            if "error" in chunk:
                return [chunk]
                
            # Search for pattern in chunk content
            if re.search(search_pattern, chunk["content"], re.IGNORECASE):
                # Find specific line numbers with matches
                lines = chunk["content"].split('\n')
                matches = []
                
                for line_idx, line in enumerate(lines):
                    if re.search(search_pattern, line, re.IGNORECASE):
                        actual_line_num = chunk["start_line"] + line_idx
                        matches.append({
                            "line_number": actual_line_num,
                            "line_content": line.strip(),
                            "match": search_pattern
                        })
                
                if matches:
                    chunk_with_matches = chunk.copy()
                    chunk_with_matches["matches"] = matches
                    chunk_with_matches["match_count"] = len(matches)
                    matching_chunks.append(chunk_with_matches)
        
        logger.info(f"Found {len(matching_chunks)} chunks with matches for '{search_pattern}' in {file_path}")
        return matching_chunks
    
    def get_file_summary(self, file_path: str) -> Dict[str, Any]:
        """
        Get a summary of the file without loading full content.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File summary information
        """
        try:
            if not os.path.exists(file_path):
                return {"error": f"File not found: {file_path}"}
            
            # Get file stats
            stat = os.stat(file_path)
            file_size = stat.st_size
            
            # Count lines efficiently
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                line_count = sum(1 for _ in f)
            
            # Estimate chunks needed
            estimated_chunks = (line_count // self.max_chunk_size) + 1
            
            return {
                "file_path": file_path,
                "file_size_bytes": file_size,
                "total_lines": line_count,
                "estimated_chunks": estimated_chunks,
                "max_chunk_size": self.max_chunk_size,
                "file_exists": True
            }
            
        except Exception as e:
            logger.error(f"Error getting file summary for {file_path}: {e}")
            return {"error": f"Error accessing file: {str(e)}"}


# Global instance for use in tools
chunked_processor = ChunkedFileProcessor(max_chunk_size=1000)  # Smaller chunks for token efficiency


def search_large_file_in_chunks(file_path: str, search_pattern: str) -> str:
    """
    Tool function to search for patterns in large files using chunks.
    
    Args:
        file_path: Path to the file to search
        search_pattern: Pattern to search for
        
    Returns:
        Formatted search results
    """
    try:
        # First get file summary
        summary = chunked_processor.get_file_summary(file_path)
        
        if "error" in summary:
            return f"Error: {summary['error']}"
        
        # If file is small, process normally
        if summary["total_lines"] <= 500:
            return f"File {file_path} is small ({summary['total_lines']} lines). Use regular search tools."
        
        # Search in chunks for large files
        matching_chunks = chunked_processor.search_in_chunks(file_path, search_pattern)
        
        if not matching_chunks:
            return f"No matches found for '{search_pattern}' in {file_path} ({summary['total_lines']} lines)"
        
        # Format results
        result = f"Found {len(matching_chunks)} chunks with matches for '{search_pattern}' in {file_path}:\n\n"
        
        for chunk in matching_chunks:
            if "matches" in chunk:
                result += f"Chunk {chunk['chunk_id']} (lines {chunk['start_line']}-{chunk['end_line']}):\n"
                for match in chunk["matches"]:
                    result += f"  Line {match['line_number']}: {match['line_content']}\n"
                result += "\n"
        
        return result
        
    except Exception as e:
        logger.error(f"Error in search_large_file_in_chunks: {e}")
        return f"Error searching file: {str(e)}"


def get_file_chunk(file_path: str, chunk_number: int) -> str:
    """
    Tool function to get a specific chunk of a large file.
    
    Args:
        file_path: Path to the file
        chunk_number: Chunk number to retrieve (1-based)
        
    Returns:
        Formatted chunk content
    """
    try:
        chunks = chunked_processor.chunk_file_by_lines(file_path)
        
        if not chunks or "error" in chunks[0]:
            return f"Error: {chunks[0].get('error', 'Unknown error')}"
        
        if chunk_number < 1 or chunk_number > len(chunks):
            return f"Error: Chunk {chunk_number} not found. File has {len(chunks)} chunks."
        
        chunk = chunks[chunk_number - 1]  # Convert to 0-based index
        
        result = f"Chunk {chunk['chunk_id']} of {len(chunks)} from {file_path}:\n"
        result += f"Lines {chunk['start_line']}-{chunk['end_line']} (of {chunk['total_lines']} total)\n"
        result += f"Content ({chunk['line_count']} lines):\n\n"
        result += chunk["content"]
        
        return result
        
    except Exception as e:
        logger.error(f"Error in get_file_chunk: {e}")
        return f"Error getting chunk: {str(e)}"
