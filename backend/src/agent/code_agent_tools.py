"""
Enhanced code agent tools for the LangChain service.
Provides specialized tools for code analysis, file operations, and development assistance.
"""

import logging
import os
import subprocess
import json
from typing import List, Dict, Any, Optional
from langchain.agents import Tool as LangchainAgentTool
from langchain_core.tools import StructuredTool
from pydantic.v1 import BaseModel, Field
from agent.chunked_file_processor import search_large_file_in_chunks, get_file_chunk
from agent.diff_generator import generate_comment_out_diff, generate_file_modification_diff
from agent.simple_diff import show_file_changes, create_unified_diff
from agent.direct_diff_tool import create_logger_diff, show_logger_changes

logger = logging.getLogger(__name__)


class FileAnalysisInput(BaseModel):
    """Input for file analysis tool."""
    file_path: str = Field(description="Path to the file to analyze")
    analysis_type: str = Field(description="Type of analysis: 'structure', 'dependencies', 'complexity', 'security'", default="structure")


class CodeSearchInput(BaseModel):
    """Input for code search tool."""
    query: str = Field(description="Search query for code patterns or functions")
    file_pattern: str = Field(description="File pattern to search in (e.g., '*.py', '*.js')", default="*")
    include_tests: bool = Field(description="Whether to include test files in search", default=False)


class GitOperationInput(BaseModel):
    """Input for git operations."""
    operation: str = Field(description="Git operation: 'status', 'log', 'diff', 'branch', 'blame'")
    file_path: Optional[str] = Field(description="File path for file-specific operations", default=None)
    additional_args: Optional[str] = Field(description="Additional git arguments", default="")


def get_code_agent_tools(service_instance) -> List[LangchainAgentTool]:
    """
    Returns enhanced code agent tools for development assistance.
    """
    tools = []

    # File Analysis Tool
    async def analyze_file(file_path: str, analysis_type: str = "structure") -> str:
        """Analyze a file for structure, dependencies, complexity, or security issues."""
        try:
            if not os.path.exists(file_path):
                return f"File not found: {file_path}"
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            analysis_result = {
                "file_path": file_path,
                "file_size": len(content),
                "line_count": len(content.splitlines()),
                "analysis_type": analysis_type
            }
            
            if analysis_type == "structure":
                # Basic structure analysis
                lines = content.splitlines()
                analysis_result.update({
                    "functions": len([line for line in lines if line.strip().startswith('def ')]),
                    "classes": len([line for line in lines if line.strip().startswith('class ')]),
                    "imports": [line.strip() for line in lines if line.strip().startswith(('import ', 'from '))]
                })
            
            elif analysis_type == "dependencies":
                # Extract import dependencies
                lines = content.splitlines()
                imports = [line.strip() for line in lines if line.strip().startswith(('import ', 'from '))]
                analysis_result["dependencies"] = imports
            
            elif analysis_type == "complexity":
                # Basic complexity metrics
                lines = content.splitlines()
                analysis_result.update({
                    "cyclomatic_complexity": len([line for line in lines if any(keyword in line for keyword in ['if ', 'elif ', 'for ', 'while ', 'try:', 'except'])]),
                    "nested_levels": max([len(line) - len(line.lstrip()) for line in lines]) // 4
                })
            
            return json.dumps(analysis_result, indent=2)
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return f"Error analyzing file: {str(e)}"

    tools.append(StructuredTool.from_function(
        func=analyze_file,
        name="analyze_file",
        description="Analyze a file for structure, dependencies, complexity, or security issues. Useful for understanding code organization and quality."
    ))

    # Enhanced Code Search Tool
    async def search_code_patterns(query: str, file_pattern: str = "*", include_tests: bool = False) -> str:
        """Search for code patterns across the codebase."""
        try:
            # Check if ripgrep is available first
            try:
                subprocess.run(["rg", "--version"], capture_output=True, check=True)
                # Use ripgrep if available
                search_cmd = ["rg", "--type-add", "code:*", "-t", "code", query]
                if not include_tests:
                    search_cmd.extend(["--glob", "!*test*", "--glob", "!*spec*"])

                result = subprocess.run(search_cmd, capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    return f"Code search results for '{query}':\n{result.stdout}"
                else:
                    return f"No matches found for '{query}' using ripgrep"

            except (FileNotFoundError, subprocess.CalledProcessError):
                # ripgrep not available, use grep
                logger.info("ripgrep not available, falling back to grep")
                grep_cmd = ["grep", "-r", "-n", query, "/app/src", "--include=*.py"]
                if not include_tests:
                    grep_cmd.extend(["--exclude-dir=test*"])

                result = subprocess.run(grep_cmd, capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    return f"Code search results for '{query}':\n{result.stdout}"
                else:
                    return f"No matches found for '{query}' using grep"

        except Exception as e:
            logger.error(f"Error searching code patterns: {e}")
            return f"Error searching code: {str(e)}"

    tools.append(StructuredTool.from_function(
        func=search_code_patterns,
        name="search_code_patterns",
        description="Search for code patterns, functions, or specific text across the codebase. Supports regex patterns and file filtering."
    ))

    # Git Operations Tool
    async def git_operation(operation: str, file_path: Optional[str] = None, additional_args: str = "") -> str:
        """Perform git operations for code analysis and history."""
        try:
            git_cmd = ["git", operation]
            
            if file_path:
                git_cmd.append(file_path)
            
            if additional_args:
                git_cmd.extend(additional_args.split())
            
            result = subprocess.run(git_cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return f"Git {operation} result:\n{result.stdout}"
            else:
                return f"Git {operation} error:\n{result.stderr}"
                
        except subprocess.TimeoutExpired:
            return f"Git {operation} timed out"
        except Exception as e:
            logger.error(f"Error with git {operation}: {e}")
            return f"Error with git operation: {str(e)}"

    tools.append(StructuredTool.from_function(
        func=git_operation,
        name="git_operation",
        description="Perform git operations like status, log, diff, branch, blame. Useful for understanding code history and changes."
    ))

    # Project Structure Tool
    async def analyze_project_structure(max_depth: int = 3) -> str:
        """Analyze the overall project structure and organization."""
        try:
            structure = {}
            
            def build_tree(path, current_depth=0):
                if current_depth >= max_depth:
                    return "..."
                
                items = {}
                try:
                    for item in sorted(os.listdir(path)):
                        if item.startswith('.'):
                            continue
                        item_path = os.path.join(path, item)
                        if os.path.isdir(item_path):
                            items[f"{item}/"] = build_tree(item_path, current_depth + 1)
                        else:
                            items[item] = os.path.getsize(item_path)
                except PermissionError:
                    return "Permission denied"
                return items
            
            structure = build_tree(".")
            return f"Project structure:\n{json.dumps(structure, indent=2)}"
            
        except Exception as e:
            logger.error(f"Error analyzing project structure: {e}")
            return f"Error analyzing project structure: {str(e)}"

    tools.append(LangchainAgentTool(
        name="analyze_project_structure",
        func=analyze_project_structure,
        description="Analyze the overall project structure and file organization. Useful for understanding project layout and architecture."
    ))

    # Code Generation Tool
    async def generate_code_template(language: str, template_type: str, description: str = "") -> str:
        """Generate code templates for common patterns."""
        try:
            templates = {
                "python": {
                    "class": f"""class {{class_name}}:
    \"\"\"
    {description}
    \"\"\"

    def __init__(self):
        pass

    def __str__(self):
        return f"{{self.__class__.__name__}}"
""",
                    "function": f"""def {{function_name}}():
    \"\"\"
    {description}
    \"\"\"
    pass
""",
                    "test": f"""import unittest

class Test{{class_name}}(unittest.TestCase):
    \"\"\"
    {description}
    \"\"\"

    def setUp(self):
        pass

    def test_{{test_name}}(self):
        # Arrange

        # Act

        # Assert
        self.assertTrue(True)

if __name__ == '__main__':
    unittest.main()
"""
                }
            }

            if language in templates and template_type in templates[language]:
                return f"Generated {language} {template_type} template:\n\n{templates[language][template_type]}"
            else:
                available = {lang: list(temps.keys()) for lang, temps in templates.items()}
                return f"Template not found. Available templates: {json.dumps(available, indent=2)}"

        except Exception as e:
            logger.error(f"Error generating code template: {e}")
            return f"Error generating code template: {str(e)}"

    tools.append(StructuredTool.from_function(
        func=generate_code_template,
        name="generate_code_template",
        description="Generate code templates for common patterns like classes, functions, tests, etc. Supports Python and other languages."
    ))

    return tools


def get_enhanced_qdrant_tools(service_instance) -> List[LangchainAgentTool]:
    """
    Enhanced Qdrant tools specifically for code search and analysis.
    """
    tools = []
    
    # Enhanced code search with semantic understanding
    async def semantic_code_search(query: str, top_k: int = 10, search_type: str = "semantic") -> str:
        """Enhanced semantic code search with different search strategies."""
        if not service_instance.my_qdrant_client:
            return "Qdrant client not available."
        
        try:
            if search_type == "semantic":
                results = await service_instance.my_qdrant_client.search_codebase(query=query, top_k=top_k)
            elif search_type == "similar_functions":
                # Search for functions similar to the query
                results = await service_instance.my_qdrant_client.search_codebase(
                    query=f"function similar to: {query}", top_k=top_k
                )
            elif search_type == "usage_patterns":
                # Search for usage patterns
                results = await service_instance.my_qdrant_client.search_codebase(
                    query=f"usage pattern: {query}", top_k=top_k
                )
            else:
                results = await service_instance.my_qdrant_client.search_codebase(query=query, top_k=top_k)
            
            if results:
                formatted_results = []
                for i, result in enumerate(results, 1):
                    file_path = result.get('file_path', 'unknown')
                    snippet = result.get('snippet', str(result))
                    score = result.get('score', 'N/A')
                    formatted_results.append(f"Result {i} (Score: {score}):\nFile: {file_path}\nSnippet:\n{snippet}\n")
                
                return "\n".join(formatted_results)
            else:
                return f"No relevant code found for query: {query}"
                
        except Exception as e:
            logger.error(f"Error in semantic code search: {e}")
            return f"Error in semantic code search: {str(e)}"

    tools.append(StructuredTool.from_function(
        func=semantic_code_search,
        name="semantic_code_search",
        description="Advanced semantic code search using vector embeddings. Can search for similar functions, usage patterns, or general code concepts."
    ))

    # Add chunked file processing tools for large files
    tools.append(StructuredTool.from_function(
        func=search_large_file_in_chunks,
        name="search_large_file_chunks",
        description="Search for patterns in large files by processing them in chunks. Use this for files that are too large for regular tools."
    ))

    tools.append(StructuredTool.from_function(
        func=get_file_chunk,
        name="get_file_chunk",
        description="Get a specific chunk of a large file. Use after search_large_file_chunks to examine specific sections."
    ))

    # Add proper diff generation tools
    tools.append(StructuredTool.from_function(
        func=generate_comment_out_diff,
        name="generate_comment_out_diff",
        description="Generate a proper Git diff showing how a file would look with specific patterns commented out. Perfect for showing logger.info removals."
    ))

    tools.append(StructuredTool.from_function(
        func=generate_file_modification_diff,
        name="generate_file_modification_diff",
        description="Generate a proper Git diff showing pattern replacements in a file. Use for any search-and-replace operations."
    ))

    # Add simple, readable diff tools
    tools.append(StructuredTool.from_function(
        func=show_file_changes,
        name="show_file_changes",
        description="Show what changes would be made to a file in a simple, readable format. Perfect for showing before/after comparisons."
    ))

    tools.append(StructuredTool.from_function(
        func=create_unified_diff,
        name="create_unified_diff",
        description="Create a clean unified diff showing file changes. Use this for standard diff output."
    ))

    # Add direct diff tools for logger.info specifically
    tools.append(StructuredTool.from_function(
        func=create_logger_diff,
        name="create_logger_diff",
        description="Create a diff showing main.py with logger.info lines commented out. Use this for logger diff requests."
    ))

    tools.append(StructuredTool.from_function(
        func=show_logger_changes,
        name="show_logger_changes",
        description="Show readable before/after comparison of logger.info changes in main.py. Perfect for diff requests."
    ))

    return tools
