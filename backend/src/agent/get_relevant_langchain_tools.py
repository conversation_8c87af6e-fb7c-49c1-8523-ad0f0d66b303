from typing import List
from langchain.agents import Tool as LangchainAgentTool
from langchain_core.tools import StructuredTool # Import StructuredTool
from langchain_community.vectorstores import Qdrant as LangchainQdrant
from clients.types import Tool as MCPClientTool
from clients.qdrant_utils.get_qdrant_tools import get_relevant_tool_objects_from_qdrant
from agent.convert_mcp_to_langchain import _convert_mcp_tool_to_langchain_tool
from clients.qdrant import EmbeddingGenerator, CustomEmbeddingGeneratorWrapper # Import CustomEmbeddingGeneratorWrapper from qdrant.py
import logging
from agent.run_neo_sync import run_neo4j_query_sync
from servers.mcpservers.neoj import _generate_graph_based_prompt, _perform_multi_hop_reasoning
from agent.code_agent_tools import get_code_agent_tools, get_enhanced_qdrant_tools
logger = logging.getLogger(__name__)
async def _get_relevant_langchain_tools(self, current_user_message: str) -> List[LangchainAgentTool]:
    """Fetches relevant MCP tools based on the query, converts them to LangchainAgentTools, and adds built-in tools."""
    relevant_langchain_tools: List[LangchainAgentTool] = []

    if self.my_qdrant_client:
        try:
            relevant_mcp_tools: List[MCPClientTool] = await get_relevant_tool_objects_from_qdrant(
                qdrant_client=self.my_qdrant_client,
                query_text=current_user_message,
                k=3 # Further reduced to 3 for GPT-3.5-turbo token limits
            )
            logger.info(f"LangChainService (id={id(self)}): Fetched {len(relevant_mcp_tools)} relevant MCP tools for query: '{current_user_message[:50]}...'")
            for mcp_tool_obj in relevant_mcp_tools:
                relevant_langchain_tools.append(_convert_mcp_tool_to_langchain_tool(
                    mcp_tool_obj,
                    execute_tool_callback=self._execute_mcp_tool_adapter
                ))
        except Exception as e:
            logger.error(f"LangChainService (id={id(self)}): Error fetching or converting relevant MCP tools: {e}", exc_info=True)
    
    # Skip Qdrant retriever to save tokens
    # if self.my_qdrant_client and self.my_qdrant_client.client and self.my_qdrant_client.embedding_generator:
    #     custom_embeddings = CustomEmbeddingGeneratorWrapper(self.my_qdrant_client.embedding_generator)
    #     langchain_qdrant_store = LangchainQdrant(
    #         client=self.my_qdrant_client.client, collection_name="chat_collection",
    #         embeddings=custom_embeddings, content_payload_key="content", metadata_payload_key="metadata"
    #     )
    #     retriever = langchain_qdrant_store.as_retriever()
    #     relevant_langchain_tools.append(
    #         LangchainAgentTool(name="QdrantRetriever", func=retriever.get_relevant_documents, description="Useful for semantic search in chat history or documents.")
    #     )
    
    # Skip Neo4j tools to save tokens
    if False and self.neo4j_client:
        # Basic Neo4j query tool
        relevant_langchain_tools.append(
            LangchainAgentTool(
                name="Neo4jQuery", 
                func=run_neo4j_query_sync,
                description="Execute a Cypher query against the Neo4j database. Provide the query as a string and optional parameters as a dictionary."
            )
        )
        
        # Entity relationship tool
        relevant_langchain_tools.append(
            StructuredTool.from_function(
                func=self._get_entity_relationships,
                name="GetEntityRelationships",
                description="Get relationships for a specific entity from the Neo4j graph database. Useful for understanding connections between users, conversations, and messages."
            )
        )
        
        # Add Neo4jGraphAnalytics tools if available
        if self.neo4j_graph_analytics:
            # Graph-based summary tool
            relevant_langchain_tools.append(
                StructuredTool.from_function(
                    func=self._generate_graph_based_summary,
                    name="GraphBasedSummary",
                    description="Generate a summary of a conversation based on graph structure. Useful for understanding the key points of a conversation."
                )
            )
            
            # Multi-hop reasoning tool
            relevant_langchain_tools.append(
                StructuredTool.from_function(
                    func=_perform_multi_hop_reasoning,
                    name="MultiHopReasoning",
                    description="Perform multi-hop reasoning across graph relationships. Useful for finding connections between entities that are not directly connected."
                )
            )
            
            # Graph-based prompt tool
            relevant_langchain_tools.append(
                StructuredTool.from_function(
                    func=_generate_graph_based_prompt,
                    name="GraphBasedPrompt",
                    description="Generate a prompt based on graph relationships. Useful for creating context-aware prompts for specific entities."
                )
            )

    # Add essential code agent tools (reduced for token efficiency)
    try:
        code_tools = get_code_agent_tools(self)
        # Only add the most essential tools to save tokens
        essential_tools = [tool for tool in code_tools if tool.name in [
            'analyze_file', 'search_code_patterns', 'semantic_code_search'
        ]]
        relevant_langchain_tools.extend(essential_tools)
        logger.info(f"LangChainService (id={id(self)}): Added {len(essential_tools)} essential code agent tools")

        # Add only essential Qdrant tools for code search
        enhanced_qdrant_tools = get_enhanced_qdrant_tools(self)
        essential_qdrant_tools = enhanced_qdrant_tools[:2]  # Limit to first 2 tools
        relevant_langchain_tools.extend(essential_qdrant_tools)
        logger.info(f"LangChainService (id={id(self)}): Added {len(essential_qdrant_tools)} essential Qdrant tools")

    except Exception as e:
        logger.error(f"LangChainService (id={id(self)}): Error adding code agent tools: {e}", exc_info=True)

    logger.info(f"LangChainService (id={id(self)}): Final list of {len(relevant_langchain_tools)} Langchain tools for this invocation: {[t.name for t in relevant_langchain_tools]}")
    return relevant_langchain_tools
