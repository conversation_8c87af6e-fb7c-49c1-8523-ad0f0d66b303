"""
Simple diff generator for showing file changes.
"""

import os
import re
import logging
from typing import List, Tuple

logger = logging.getLogger(__name__)


def create_simple_diff(file_path: str, pattern: str, action: str = "comment") -> str:
    """
    Create a simple, readable diff showing changes to a file.
    
    Args:
        file_path: Path to the file
        pattern: Pattern to search for (e.g., "logger.info")
        action: What to do with the pattern ("comment", "remove", etc.)
        
    Returns:
        Simple diff showing the changes
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"
        
        # Read the file
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Find lines with the pattern
        changes = []
        for line_num, line in enumerate(lines, 1):
            if re.search(pattern, line, re.IGNORECASE):
                # Skip already commented lines
                stripped = line.strip()
                if not stripped.startswith('#'):
                    if action == "comment":
                        # Create commented version
                        indent = line[:len(line) - len(line.lstrip())]
                        commented = f"{indent}# {line.lstrip()}"
                        changes.append((line_num, line.rstrip(), commented.rstrip()))
        
        if not changes:
            return f"No uncommented lines containing '{pattern}' found in {file_path}"
        
        # Create the diff output
        diff_lines = [
            f"Diff for {file_path}",
            f"Showing changes to comment out '{pattern}' lines:",
            "=" * 60,
            ""
        ]
        
        for line_num, original, modified in changes:
            diff_lines.extend([
                f"Line {line_num}:",
                f"- {original}",
                f"+ {modified}",
                ""
            ])
        
        diff_lines.extend([
            "=" * 60,
            f"Total changes: {len(changes)} lines"
        ])
        
        return "\n".join(diff_lines)
        
    except Exception as e:
        logger.error(f"Error creating diff for {file_path}: {e}")
        return f"Error creating diff: {str(e)}"


def show_file_changes(file_path: str, search_pattern: str, replacement: str = None) -> str:
    """
    Show what changes would be made to a file.
    
    Args:
        file_path: Path to the file
        search_pattern: Pattern to search for
        replacement: What to replace it with (if None, will comment out)
        
    Returns:
        Readable diff showing the changes
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"
        
        # Read the file
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Find matching lines
        matches = []
        for line_num, line in enumerate(lines, 1):
            if re.search(search_pattern, line, re.IGNORECASE):
                original = line.rstrip()
                
                if replacement is None:
                    # Comment out the line
                    stripped = line.strip()
                    if not stripped.startswith('#'):
                        indent = line[:len(line) - len(line.lstrip())]
                        modified = f"{indent}# {line.lstrip().rstrip()}"
                    else:
                        modified = original  # Already commented
                else:
                    # Replace with the given replacement
                    modified = re.sub(search_pattern, replacement, original, flags=re.IGNORECASE)
                
                if original != modified:
                    matches.append((line_num, original, modified))
        
        if not matches:
            action = "comment out" if replacement is None else "replace"
            return f"No lines to {action} found for pattern '{search_pattern}' in {file_path}"
        
        # Create readable output
        result_lines = [
            f"Changes for: {file_path}",
            f"Pattern: {search_pattern}",
            f"Action: {'Comment out' if replacement is None else f'Replace with: {replacement}'}",
            "",
            "Changes to be made:",
            "-" * 50
        ]
        
        for line_num, original, modified in matches:
            result_lines.extend([
                f"",
                f"Line {line_num}:",
                f"  Before: {original}",
                f"  After:  {modified}"
            ])
        
        result_lines.extend([
            "",
            "-" * 50,
            f"Total lines to change: {len(matches)}"
        ])
        
        return "\n".join(result_lines)
        
    except Exception as e:
        logger.error(f"Error showing file changes: {e}")
        return f"Error: {str(e)}"


def create_unified_diff(file_path: str, pattern: str) -> str:
    """
    Create a unified diff format showing the changes.
    
    Args:
        file_path: Path to the file
        pattern: Pattern to comment out
        
    Returns:
        Unified diff format
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"
        
        # Read original file
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            original_lines = f.readlines()
        
        # Create modified version
        modified_lines = []
        for line in original_lines:
            if re.search(pattern, line, re.IGNORECASE):
                stripped = line.strip()
                if not stripped.startswith('#'):
                    # Comment out the line
                    indent = line[:len(line) - len(line.lstrip())]
                    modified_lines.append(f"{indent}# {line.lstrip()}")
                else:
                    modified_lines.append(line)
            else:
                modified_lines.append(line)
        
        # Use system diff to create unified diff
        import tempfile
        import subprocess
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.orig', delete=False) as orig_file:
            orig_file.writelines(original_lines)
            orig_path = orig_file.name
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.mod', delete=False) as mod_file:
            mod_file.writelines(modified_lines)
            mod_path = mod_file.name
        
        try:
            # Generate unified diff
            result = subprocess.run(
                ['diff', '-u', orig_path, mod_path],
                capture_output=True,
                text=True
            )
            
            diff_output = result.stdout
            
            if not diff_output:
                return f"No changes needed for {file_path}"
            
            # Replace temp file names with actual file name
            diff_output = diff_output.replace(orig_path, file_path)
            diff_output = diff_output.replace(mod_path, file_path)
            
            return diff_output
            
        finally:
            # Clean up temp files
            os.unlink(orig_path)
            os.unlink(mod_path)
            
    except Exception as e:
        logger.error(f"Error creating unified diff: {e}")
        return f"Error: {str(e)}"
