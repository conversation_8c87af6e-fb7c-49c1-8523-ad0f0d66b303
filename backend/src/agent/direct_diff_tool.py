"""
Direct diff tool that works immediately without complex dependencies.
"""

import os
import re
import logging

logger = logging.getLogger(__name__)


def create_logger_diff(file_path: str = "/app/src/main.py") -> str:
    """
    Create a diff showing main.py with logger.info lines commented out.
    
    Args:
        file_path: Path to the file (defaults to main.py)
        
    Returns:
        Diff showing the changes
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"
        
        # Read the file
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Find and comment out logger.info lines
        changes = []
        for line_num, line in enumerate(lines, 1):
            if re.search(r'logger\.info', line, re.IGNORECASE):
                # <PERSON><PERSON> already commented lines
                stripped = line.strip()
                if not stripped.startswith('#'):
                    original = line.rstrip()
                    # Create commented version
                    indent = line[:len(line) - len(line.lstrip())]
                    commented = f"{indent}# {line.lstrip().rstrip()}"
                    changes.append((line_num, original, commented))
        
        if not changes:
            return f"No uncommented logger.info lines found in {file_path}"
        
        # Create unified diff format
        diff_lines = [
            f"--- {file_path}",
            f"+++ {file_path}",
            ""
        ]
        
        # Group changes into hunks
        for line_num, original, commented in changes:
            diff_lines.extend([
                f"@@ -{line_num},1 +{line_num},1 @@",
                f"-{original}",
                f"+{commented}",
                ""
            ])
        
        diff_lines.extend([
            f"",
            f"Summary: {len(changes)} logger.info lines would be commented out"
        ])
        
        return "\n".join(diff_lines)
        
    except Exception as e:
        logger.error(f"Error creating diff: {e}")
        return f"Error creating diff: {str(e)}"


def show_logger_changes(file_path: str = "/app/src/main.py") -> str:
    """
    Show what logger.info changes would be made in a readable format.
    
    Args:
        file_path: Path to the file (defaults to main.py)
        
    Returns:
        Readable before/after comparison
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"
        
        # Read the file
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Find logger.info lines
        changes = []
        for line_num, line in enumerate(lines, 1):
            if re.search(r'logger\.info', line, re.IGNORECASE):
                # Skip already commented lines
                stripped = line.strip()
                if not stripped.startswith('#'):
                    original = line.rstrip()
                    # Create commented version
                    indent = line[:len(line) - len(line.lstrip())]
                    commented = f"{indent}# {line.lstrip().rstrip()}"
                    changes.append((line_num, original, commented))
        
        if not changes:
            return f"No uncommented logger.info lines found in {file_path}"
        
        # Create readable output
        result_lines = [
            f"Changes for: {file_path}",
            f"Action: Comment out logger.info lines",
            "",
            "Changes to be made:",
            "-" * 60
        ]
        
        for line_num, original, commented in changes:
            result_lines.extend([
                f"",
                f"Line {line_num}:",
                f"  Before: {original}",
                f"  After:  {commented}"
            ])
        
        result_lines.extend([
            "",
            "-" * 60,
            f"Total lines to change: {len(changes)}"
        ])
        
        return "\n".join(result_lines)
        
    except Exception as e:
        logger.error(f"Error showing changes: {e}")
        return f"Error: {str(e)}"
