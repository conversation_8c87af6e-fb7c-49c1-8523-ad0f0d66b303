"""
Generate proper Git-style diffs for code modifications.
"""

import os
import re
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class DiffGenerator:
    """Generate proper Git-style diffs for file modifications."""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def generate_comment_diff(self, file_path: str, pattern: str, comment_prefix: str = "#") -> str:
        """
        Generate a proper Git diff showing lines with a pattern commented out.
        
        Args:
            file_path: Path to the file
            pattern: Pattern to search for (e.g., "logger.info")
            comment_prefix: Comment prefix to use (default: "#")
            
        Returns:
            Proper Git diff format string
        """
        try:
            if not os.path.exists(file_path):
                return f"Error: File not found: {file_path}"
            
            # Read original file
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                original_lines = f.readlines()
            
            # Create modified version with pattern commented out
            modified_lines = []
            changes_made = []
            
            for line_num, line in enumerate(original_lines, 1):
                if re.search(pattern, line, re.IGNORECASE):
                    # Check if line is already commented
                    stripped = line.lstrip()
                    if not stripped.startswith(comment_prefix):
                        # Comment out the line
                        indent = line[:len(line) - len(line.lstrip())]
                        commented_line = f"{indent}{comment_prefix} {stripped}"
                        modified_lines.append(commented_line)
                        changes_made.append({
                            'line_num': line_num,
                            'original': line.rstrip(),
                            'modified': commented_line.rstrip()
                        })
                    else:
                        # Already commented, keep as is
                        modified_lines.append(line)
                else:
                    # No pattern match, keep original
                    modified_lines.append(line)
            
            if not changes_made:
                return f"No uncommented lines containing '{pattern}' found in {file_path}"
            
            # Generate proper Git diff
            diff_content = self._generate_git_diff(
                file_path, 
                original_lines, 
                modified_lines, 
                changes_made
            )
            
            return diff_content
            
        except Exception as e:
            logger.error(f"Error generating diff for {file_path}: {e}")
            return f"Error generating diff: {str(e)}"
    
    def _generate_git_diff(self, file_path: str, original_lines: List[str], 
                          modified_lines: List[str], changes: List[Dict]) -> str:
        """Generate a proper Git diff format."""
        
        # Git diff header
        diff_lines = [
            f"diff --git a/{file_path} b/{file_path}",
            f"index 1234567..abcdefg 100644",
            f"--- a/{file_path}",
            f"+++ b/{file_path}"
        ]
        
        # Group changes into hunks
        hunks = self._create_hunks(original_lines, modified_lines, changes)
        
        for hunk in hunks:
            diff_lines.extend(hunk)
        
        return "\n".join(diff_lines)
    
    def _create_hunks(self, original_lines: List[str], modified_lines: List[str], 
                     changes: List[Dict]) -> List[List[str]]:
        """Create diff hunks with context."""
        hunks = []
        context_lines = 3  # Lines of context before/after changes
        
        # Group nearby changes
        change_groups = self._group_nearby_changes(changes, context_lines * 2)
        
        for group in change_groups:
            hunk = self._create_single_hunk(
                original_lines, modified_lines, group, context_lines
            )
            hunks.append(hunk)
        
        return hunks
    
    def _group_nearby_changes(self, changes: List[Dict], max_distance: int) -> List[List[Dict]]:
        """Group changes that are close together."""
        if not changes:
            return []
        
        groups = []
        current_group = [changes[0]]
        
        for change in changes[1:]:
            last_line = current_group[-1]['line_num']
            if change['line_num'] - last_line <= max_distance:
                current_group.append(change)
            else:
                groups.append(current_group)
                current_group = [change]
        
        groups.append(current_group)
        return groups
    
    def _create_single_hunk(self, original_lines: List[str], modified_lines: List[str],
                           changes: List[Dict], context_lines: int) -> List[str]:
        """Create a single diff hunk."""
        first_change = changes[0]['line_num']
        last_change = changes[-1]['line_num']
        
        # Calculate hunk boundaries
        start_line = max(1, first_change - context_lines)
        end_line = min(len(original_lines), last_change + context_lines)
        
        # Count original and modified lines in this hunk
        orig_count = end_line - start_line + 1
        mod_count = orig_count  # Same since we're only commenting, not adding/removing lines
        
        # Hunk header
        hunk_lines = [f"@@ -{start_line},{orig_count} +{start_line},{mod_count} @@"]
        
        # Generate hunk content
        change_line_nums = {change['line_num'] for change in changes}
        
        for line_num in range(start_line, end_line + 1):
            line_idx = line_num - 1  # Convert to 0-based index
            
            if line_num in change_line_nums:
                # This line was changed
                original_line = original_lines[line_idx].rstrip()
                modified_line = modified_lines[line_idx].rstrip()
                hunk_lines.append(f"-{original_line}")
                hunk_lines.append(f"+{modified_line}")
            else:
                # Context line
                context_line = original_lines[line_idx].rstrip()
                hunk_lines.append(f" {context_line}")
        
        return hunk_lines


# Global instance
diff_generator = DiffGenerator()


def generate_comment_out_diff(file_path: str, pattern: str) -> str:
    """
    Tool function to generate a diff showing a pattern commented out.
    
    Args:
        file_path: Path to the file
        pattern: Pattern to comment out (e.g., "logger.info")
        
    Returns:
        Proper Git diff format
    """
    try:
        return diff_generator.generate_comment_diff(file_path, pattern)
    except Exception as e:
        logger.error(f"Error in generate_comment_out_diff: {e}")
        return f"Error generating diff: {str(e)}"


def generate_file_modification_diff(file_path: str, search_pattern: str, 
                                  replacement: str) -> str:
    """
    Tool function to generate a diff showing pattern replacements.
    
    Args:
        file_path: Path to the file
        search_pattern: Pattern to search for
        replacement: What to replace it with
        
    Returns:
        Proper Git diff format
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"
        
        # Read original file
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            original_content = f.read()
        
        # Create modified content
        modified_content = re.sub(search_pattern, replacement, original_content, flags=re.MULTILINE)
        
        if original_content == modified_content:
            return f"No changes needed - pattern '{search_pattern}' not found or already replaced"
        
        # Write to temporary files and use diff
        import tempfile
        import subprocess
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.orig', delete=False) as orig_file:
            orig_file.write(original_content)
            orig_path = orig_file.name
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.mod', delete=False) as mod_file:
            mod_file.write(modified_content)
            mod_path = mod_file.name
        
        try:
            # Generate diff using system diff command
            result = subprocess.run(
                ['diff', '-u', orig_path, mod_path],
                capture_output=True,
                text=True
            )
            
            diff_output = result.stdout
            
            # Replace temp file names with actual file name
            diff_output = diff_output.replace(orig_path, f"a/{file_path}")
            diff_output = diff_output.replace(mod_path, f"b/{file_path}")
            
            return diff_output if diff_output else "No differences found"
            
        finally:
            # Clean up temp files
            os.unlink(orig_path)
            os.unlink(mod_path)
            
    except Exception as e:
        logger.error(f"Error in generate_file_modification_diff: {e}")
        return f"Error generating diff: {str(e)}"
