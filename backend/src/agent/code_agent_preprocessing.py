"""
Code agent preprocessing functions for enhanced code understanding and context preparation.
"""

import logging
import re
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


def detect_code_intent(user_message: str) -> Dict[str, Any]:
    """
    Analyze user message to detect coding intent and extract relevant information.
    """
    intent_patterns = {
        "debug": [
            r"debug|fix|error|bug|issue|problem|not working|broken",
            r"exception|traceback|stack trace|crash"
        ],
        "analyze": [
            r"analyze|review|understand|explain|what does|how does",
            r"code review|analyze code|understand code"
        ],
        "implement": [
            r"create|build|implement|write|develop|add|make",
            r"new feature|new function|new class|new method"
        ],
        "refactor": [
            r"refactor|improve|optimize|clean up|restructure",
            r"better way|more efficient|simplify"
        ],
        "test": [
            r"test|unit test|testing|test case|coverage",
            r"write test|create test|test for"
        ],
        "documentation": [
            r"document|docs|comment|docstring|readme",
            r"documentation|explain code|add comments"
        ],
        "search": [
            r"find|search|look for|locate|where is",
            r"show me|find code|search code"
        ]
    }
    
    detected_intents = []
    confidence_scores = {}
    
    message_lower = user_message.lower()
    
    for intent, patterns in intent_patterns.items():
        score = 0
        for pattern in patterns:
            matches = len(re.findall(pattern, message_lower))
            score += matches
        
        if score > 0:
            detected_intents.append(intent)
            confidence_scores[intent] = score
    
    # Extract file paths, function names, and other code-related entities
    entities = extract_code_entities(user_message)
    
    return {
        "intents": detected_intents,
        "confidence_scores": confidence_scores,
        "primary_intent": max(confidence_scores.keys(), key=confidence_scores.get) if confidence_scores else "general",
        "entities": entities,
        "is_code_related": len(detected_intents) > 0 or len(entities["file_paths"]) > 0
    }


def extract_code_entities(text: str) -> Dict[str, List[str]]:
    """
    Extract code-related entities from text (file paths, function names, etc.).
    """
    entities = {
        "file_paths": [],
        "function_names": [],
        "class_names": [],
        "variable_names": [],
        "error_messages": [],
        "programming_languages": []
    }
    
    # File path patterns
    file_patterns = [
        r'[a-zA-Z0-9_/.-]+\.[a-zA-Z]{1,4}',  # Basic file paths with extensions
        r'[./]?[a-zA-Z0-9_/-]+/[a-zA-Z0-9_.-]+',  # Directory paths
    ]
    
    for pattern in file_patterns:
        matches = re.findall(pattern, text)
        entities["file_paths"].extend(matches)
    
    # Function name patterns (common conventions)
    function_patterns = [
        r'\b[a-zA-Z_][a-zA-Z0-9_]*\(\)',  # function()
        r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # Python def
        r'function\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # JavaScript function
    ]
    
    for pattern in function_patterns:
        matches = re.findall(pattern, text)
        entities["function_names"].extend(matches)
    
    # Class name patterns
    class_patterns = [
        r'class\s+([A-Z][a-zA-Z0-9_]*)',  # Python/Java class
        r'\b[A-Z][a-zA-Z0-9_]*(?=\s*\{|\s*extends|\s*implements)',  # Class-like patterns
    ]
    
    for pattern in class_patterns:
        matches = re.findall(pattern, text)
        entities["class_names"].extend(matches)
    
    # Programming language detection
    language_keywords = {
        "python": ["python", "py", "def", "import", "from", "class", "self", "pip", "conda"],
        "javascript": ["javascript", "js", "function", "var", "let", "const", "npm", "node"],
        "java": ["java", "public", "private", "static", "void", "class", "interface"],
        "cpp": ["c++", "cpp", "include", "namespace", "std", "cout", "cin"],
        "go": ["golang", "go", "func", "package", "import", "var", "type"],
        "rust": ["rust", "fn", "let", "mut", "struct", "impl", "cargo"],
        "typescript": ["typescript", "ts", "interface", "type", "export", "import"]
    }
    
    text_lower = text.lower()
    for lang, keywords in language_keywords.items():
        if any(keyword in text_lower for keyword in keywords):
            entities["programming_languages"].append(lang)
    
    # Error message patterns
    error_patterns = [
        r'Error:\s*(.+)',
        r'Exception:\s*(.+)',
        r'Traceback.*?:\s*(.+)',
        r'SyntaxError:\s*(.+)',
        r'TypeError:\s*(.+)',
        r'ValueError:\s*(.+)'
    ]
    
    for pattern in error_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        entities["error_messages"].extend(matches)
    
    # Clean up and deduplicate
    for key in entities:
        entities[key] = list(set(entities[key]))  # Remove duplicates
        entities[key] = [item.strip() for item in entities[key] if item.strip()]  # Remove empty strings
    
    return entities


def prepare_code_context(intent_analysis: Dict[str, Any], conversation_history: List[Dict[str, str]]) -> Dict[str, Any]:
    """
    Prepare enhanced context for code-related tasks based on intent analysis.
    """
    context = {
        "primary_intent": intent_analysis["primary_intent"],
        "is_code_related": intent_analysis["is_code_related"],
        "suggested_tools": [],
        "context_priority": "normal",
        "search_keywords": [],
        "file_focus": []
    }
    
    primary_intent = intent_analysis["primary_intent"]
    entities = intent_analysis["entities"]
    
    # Suggest tools based on intent
    tool_suggestions = {
        "debug": ["search_code_patterns", "analyze_file", "git_operation"],
        "analyze": ["analyze_file", "analyze_project_structure", "semantic_code_search"],
        "implement": ["generate_code_template", "search_code_patterns", "analyze_project_structure"],
        "refactor": ["analyze_file", "search_code_patterns", "git_operation"],
        "test": ["generate_code_template", "analyze_file", "search_code_patterns"],
        "documentation": ["analyze_file", "analyze_project_structure"],
        "search": ["semantic_code_search", "search_code_patterns", "analyze_project_structure"]
    }
    
    if primary_intent in tool_suggestions:
        context["suggested_tools"] = tool_suggestions[primary_intent]
    
    # Set context priority based on complexity
    if len(entities["file_paths"]) > 3 or len(entities["error_messages"]) > 0:
        context["context_priority"] = "high"
    elif primary_intent in ["implement", "refactor"]:
        context["context_priority"] = "high"
    
    # Prepare search keywords
    search_keywords = []
    search_keywords.extend(entities["function_names"])
    search_keywords.extend(entities["class_names"])
    search_keywords.extend([lang for lang in entities["programming_languages"]])
    
    # Add intent-specific keywords
    if primary_intent == "debug":
        search_keywords.extend(["error", "exception", "bug", "fix"])
    elif primary_intent == "test":
        search_keywords.extend(["test", "unittest", "pytest", "spec"])
    
    context["search_keywords"] = list(set(search_keywords))
    context["file_focus"] = entities["file_paths"]
    
    # Analyze conversation history for additional context
    recent_files = []
    for msg in conversation_history[-5:]:  # Look at last 5 messages
        if msg.get("role") == "user":
            msg_entities = extract_code_entities(msg.get("content", ""))
            recent_files.extend(msg_entities["file_paths"])
    
    context["recent_files"] = list(set(recent_files))
    
    return context


def enhance_agent_state_for_code(state: Dict[str, Any], user_message: str) -> Dict[str, Any]:
    """
    Enhance the agent state with code-specific context and analysis.
    """
    # Perform intent analysis
    intent_analysis = detect_code_intent(user_message)
    
    # Prepare code context
    conversation_history = []
    if "chat_history" in state:
        # Convert LangChain messages to dict format for analysis
        for msg in state["chat_history"]:
            if hasattr(msg, 'content') and hasattr(msg, 'type'):
                conversation_history.append({
                    "role": msg.type,
                    "content": msg.content
                })
    
    code_context = prepare_code_context(intent_analysis, conversation_history)
    
    # Add code-specific information to state
    state["code_intent"] = intent_analysis
    state["code_context"] = code_context
    state["is_code_task"] = intent_analysis["is_code_related"]
    
    # Enhance system message if it's a code task
    if intent_analysis["is_code_related"] and "system_message" in state:
        code_enhancement = f"""

DETECTED CODE TASK:
- Primary Intent: {intent_analysis['primary_intent']}
- Detected Entities: {intent_analysis['entities']}
- Suggested Tools: {code_context['suggested_tools']}
- Priority: {code_context['context_priority']}

Focus on providing precise, actionable code assistance."""
        
        state["system_message"] += code_enhancement
    
    logger.info(f"Enhanced agent state for code task. Intent: {intent_analysis['primary_intent']}, "
                f"Code-related: {intent_analysis['is_code_related']}")
    
    return state
