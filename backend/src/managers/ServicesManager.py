import logging
import importlib
import inspect
import os
from typing import Dict, List, Any, Optional, Type
import asyncio

# Import types for dependencies
from src.managers.AssistantManager import AssistantManager 
from src.managers.FunctionsManager import FunctionsManager
from src.type_definitions.service import ServiceResponse
from src.interfaces.BaseService import ServiceInterface, BaseService
from clients.neoj import MyNeo4jClient
import traceback # For more detailed error logging
from datetime import datetime

logger = logging.getLogger(__name__)

# Singleton instance
_instance: Optional['ServicesManager'] = None

class ServicesManager:
    """Manager for service implementations."""
    _instance_configured = False # Class variable to track configuration

    def __init__(self, base_service_config: Optional[Dict[str, Any]] = None, graph: MyNeo4jClient = None):
        self.services: Dict[str, ServiceInterface] = {}
        self.initialized = False
        self.graph = graph
        
        if not ServicesManager._instance_configured and base_service_config:
            logger.critical("ServicesManager __init__: FIRST TIME CONFIGURATION.")
            self._internal_base_service_config = base_service_config
            ServicesManager._instance_configured = True
        elif ServicesManager._instance_configured:
            logger.debug("ServicesManager __init__: Instance was already configured. Using existing configuration.")
            if not hasattr(self, '_internal_base_service_config') and _instance and hasattr(_instance, '_internal_base_service_config'):
                 self._internal_base_service_config = _instance._internal_base_service_config
            elif not hasattr(self, '_internal_base_service_config'):
                 self._internal_base_service_config = {} 
                 logger.warning("ServicesManager __init__: Re-init on configured instance but _internal_base_service_config missing.")
        else: 
            logger.critical("ServicesManager __init__: INSTANCE CREATED WITHOUT INITIAL BASE CONFIGURATION. DEPENDENCIES WILL BE MISSING.")
            self._internal_base_service_config = {}

        logger.info(f"ServicesManager __init__ effective base_service_config keys: {list(self._internal_base_service_config.keys())}")

        self.assistant_manager: Optional[AssistantManager] = None
        self.functions_manager: Optional[FunctionsManager] = None

        self.chat_session = self._internal_base_service_config.get("chat_session")
        self.assistant_manager = self._internal_base_service_config.get("user_manager")
        self.functions_manager = FunctionsManager.get_instance() 

        logger.info(
            f"ServicesManager __init__ dependencies: chat_session {'SET' if self.chat_session else 'NONE'}, "
            f"assistant_manager {'SET' if self.assistant_manager else 'NONE'}, "
            f"functions_manager {'OBTAINED' if self.functions_manager else 'NONE'}, "
            f"graph {'SET' if self.graph else 'NONE'}."
        )

    @classmethod
    def get_instance(cls, base_service_config: Optional[Dict[str, Any]] = None, graph: MyNeo4jClient = None):
        """Get the singleton instance of ServicesManager."""
        global _instance
        if _instance is None:
            logger.critical("ServicesManager.get_instance: No instance exists. CREATING NEW INSTANCE.")
            if base_service_config is None and not cls._instance_configured:
                logger.critical("ServicesManager.get_instance: CRITICAL - Creating new instance WITHOUT base_service_config. This is likely an error.")
            _instance = cls(base_service_config=base_service_config, graph=graph)
        elif base_service_config is not None and not cls._instance_configured:
            logger.critical("ServicesManager.get_instance: Instance exists but reports not configured. ATTEMPTING TO CONFIGURE NOW.")
            _instance._internal_base_service_config = base_service_config
            _instance.chat_session = base_service_config.get("chat_session")
            _instance.assistant_manager = base_service_config.get("user_manager")
            _instance.graph = graph
            cls._instance_configured = True 
            logger.info(
                f"ServicesManager.get_instance (re-config path) dependencies: chat_session {'SET' if _instance.chat_session else 'NONE'}, "
                f"assistant_manager {'SET' if _instance.assistant_manager else 'NONE'}, "
                f"graph {'SET' if _instance.graph else 'NONE'}."
            )
        elif base_service_config is not None and cls._instance_configured:
            if _instance._internal_base_service_config != base_service_config:
                logger.warning(
                    "ServicesManager.get_instance: Instance already configured. "
                    f"A new, different base_service_config was provided (keys: {list(base_service_config.keys())}). "
                    "This new config will be IGNORED. Returning existing configured instance."
                )
            else:
                 logger.debug("ServicesManager.get_instance: Instance exists and configured. New config matches. Returning instance.")
        else: 
            logger.debug("ServicesManager.get_instance: Instance exists and configured. No new config. Returning instance.")
        return _instance
    
    async def initialize(self) -> bool:
        """Initialize all services."""
        if self.initialized:
            logger.info("ServicesManager already initialized. Skipping.")
            return True
        
        logger.info("ServicesManager: Starting initialization process...")
        
        if not ServicesManager._instance_configured:
            logger.error("ServicesManager.initialize: CRITICAL - Attempting to initialize an unconfigured ServicesManager instance. Services will likely fail to load or work.")

        if self.functions_manager and not self.functions_manager.initialized:
            logger.info("ServicesManager: FunctionsManager dependency not initialized. Initializing now...")
            fm_init_success = await self.functions_manager.initialize()
            if not fm_init_success:
                logger.error("ServicesManager: Failed to initialize FunctionsManager. This may impact services relying on it.")
        elif not self.functions_manager:
            logger.warning("ServicesManager: FunctionsManager instance is None. Services relying on it may fail.")
        else:
            logger.info("ServicesManager: FunctionsManager already initialized.")

        logger.info("ServicesManager: Proceeding to discover services...")
        await self._discover_services()
        
        initialization_tasks = []
        if not self.services:
            logger.warning("ServicesManager.initialize: No services were discovered. Initialization loop will be empty.")
        else:
            logger.info(f"ServicesManager.initialize: Initializing {len(self.services)} discovered services...")
            for service_id, service in self.services.items():
                initialization_tasks.append(self._initialize_service(service_id, service))
            
            results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                service_id_res = list(self.services.keys())[i] 
                if isinstance(result, Exception):
                    logger.error(f"ServicesManager: Service '{service_id_res}' failed to initialize: {result}", exc_info=result)
                else:
                    logger.info(f"ServicesManager: Service '{service_id_res}' initialized successfully (Result: {result}).")
        
        self.initialized = True
        final_service_count = len(self.services)
        logger.info(f"ServicesManager: Initialization complete. Total services loaded and initialized: {final_service_count}.")
        if final_service_count == 0:
            logger.critical("ServicesManager: CRITICAL - Initialization finished, but NO services are loaded. Check discovery path, service definitions, and dependency injection.")
        return True
    
    async def _initialize_service(self, service_id: str, service: ServiceInterface) -> bool:
        """Initialize a single service."""
        try:
            logger.info(f"Initializing service: {service_id}")
            
            # Store service initialization start in Neo4j if graph client is available
            if self.graph:
                try:
                    # This would need to be implemented in the Neo4j client
                    # For now, we'll just log that we would store service initialization in Neo4j
                    logger.info(f"Would store service initialization start in Neo4j: {service_id}")
                    # In the future, you could implement a method like:
                    # await self.graph.store_service_initialization_start(
                    #     service_id=service_id,
                    #     timestamp=datetime.utcnow().isoformat()
                    # )
                except Exception as e:
                    logger.error(f"Error storing service initialization start in Neo4j: {str(e)}")
            
            result = await service.initialize()
            
            # Store service initialization result in Neo4j if graph client is available
            if self.graph:
                try:
                    # This would need to be implemented in the Neo4j client
                    # For now, we'll just log that we would store service initialization result in Neo4j
                    logger.info(f"Would store service initialization result in Neo4j: {service_id}, result: {result}")
                    # In the future, you could implement a method like:
                    # await self.graph.store_service_initialization_result(
                    #     service_id=service_id,
                    #     result=result,
                    #     timestamp=datetime.utcnow().isoformat()
                    # )
                except Exception as e:
                    logger.error(f"Error storing service initialization result in Neo4j: {str(e)}")
            
            logger.info(f"Service {service_id} initialized: {result}")
            return result
        except Exception as e:
            logger.error(f"Error initializing service {service_id}: {e}")
            return False
    
    async def _discover_services(self) -> None:
        """Discover and load service implementations."""
        logger.info(f"ServicesManager._discover_services: Starting. Dependencies status: "
                    f"chat_session {'AVAILABLE' if self.chat_session else 'MISSING'}, "
                    f"assistant_manager {'AVAILABLE' if self.assistant_manager else 'MISSING'}, "
                    f"functions_manager {'INITIALIZED' if self.functions_manager and self.functions_manager.initialized else ('AVAILABLE_UNINITIALIZED' if self.functions_manager else 'MISSING')}, "
                    f"graph {'AVAILABLE' if self.graph else 'MISSING'}")
        
        try:
            services_path = os.path.join(os.path.dirname(__file__), "..", "services", "implementations")
            if not os.path.exists(services_path):
                logger.error(f"ServicesManager._discover_services: CRITICAL - Services directory NOT FOUND: {services_path}")
                os.makedirs(services_path, exist_ok=True)
                logger.info(f"ServicesManager._discover_services: Created missing services directory: {services_path}")
                return
            
            logger.info(f"ServicesManager._discover_services: Scanning for services in: {services_path}")

            service_files = [f for f in os.listdir(services_path) if f.endswith(".py") and not f.startswith("__")]
            if not service_files:
                logger.warning(f"ServicesManager._discover_services: No Python files found in services directory: {services_path}")
                return
            
            logger.info(f"ServicesManager._discover_services: Found potential service files: {service_files}")

            for file_name in service_files:
                try:
                    module_name = f"src.services.implementations.{file_name[:-3]}"
                    logger.info(f"ServicesManager._discover_services: Attempting to import module: {module_name}")
                    module = importlib.import_module(module_name)
                    logger.debug(f"ServicesManager._discover_services: Successfully imported {module_name}")

                    found_service_in_file = False
                    for name, obj_type in inspect.getmembers(module):
                        if inspect.isclass(obj_type) and \
                           issubclass(obj_type, ServiceInterface) and \
                           obj_type is not ServiceInterface and \
                           obj_type is not BaseService:
                            
                            logger.info(f"ServicesManager._discover_services: Found service class '{name}' in {module_name}.")
                            found_service_in_file = True
                            service_kwargs = {}
                            constructor_params = inspect.signature(obj_type.__init__).parameters

                            if 'chat_session' in constructor_params and self.chat_session:
                                if self.chat_session:
                                    service_kwargs['chat_session'] = self.chat_session
                                else:
                                    logger.warning(f"Service '{name}' expects 'chat_session', but it's unavailable in ServicesManager.")
                            
                            if 'assistant_manager' in constructor_params and self.assistant_manager:
                                if self.assistant_manager:
                                    service_kwargs['assistant_manager'] = self.assistant_manager
                                else:
                                    logger.warning(f"Service '{name}' expects 'assistant_manager', but it's unavailable in ServicesManager.")
                            
                            if 'functions_manager' in constructor_params:
                                if self.functions_manager and self.functions_manager.initialized:
                                    service_kwargs['functions_manager'] = self.functions_manager
                                elif self.functions_manager:
                                    logger.warning(f"Service '{name}' expects 'functions_manager', but it's available but NOT INITIALIZED in ServicesManager.")
                                else:
                                    logger.warning(f"Service '{name}' expects 'functions_manager', but it's unavailable in ServicesManager.")
                            
                            if 'graph' in constructor_params and self.graph:
                                service_kwargs['graph'] = self.graph
                            
                            logger.debug(f"ServicesManager._discover_services: Attempting to instantiate '{name}' with kwargs: {list(service_kwargs.keys())}")
                            try:
                                service_instance = obj_type(**service_kwargs)
                            except TypeError as te:
                                logger.error(
                                    f"ServicesManager._discover_services: TypeError instantiating service '{name}' with provided dependencies "
                                    f"({list(service_kwargs.keys())}): {te}. Attempting fallback instantiation without dependencies."
                                )
                                try:
                                    service_instance = obj_type() # Fallback
                                except Exception as fallback_e:
                                    logger.error(f"ServicesManager._discover_services: Fallback instantiation for service '{name}' also FAILED: {fallback_e}", exc_info=True)
                                    continue # Skip this service
                            except Exception as e:
                                logger.error(f"ServicesManager._discover_services: Error instantiating service '{name}': {e}", exc_info=True)
                                continue # Skip this service
                            
                            if hasattr(service_instance, 'id') and service_instance.id:
                                self.services[service_instance.id] = service_instance
                                logger.info(f"ServicesManager._discover_services: Successfully loaded and added service '{service_instance.id}' from class '{name}'. Total services now: {len(self.services)}")
                            else:
                                logger.error(f"ServicesManager._discover_services: Service class '{name}' instantiated but has no 'id' attribute or id is empty. Skipping.")

                    if not found_service_in_file:
                        logger.debug(f"ServicesManager._discover_services: No service classes found in {module_name}.")
                except ImportError as ie:
                    logger.error(f"ServicesManager._discover_services: ImportError loading module from {file_name}: {ie}", exc_info=True)
                except Exception as e:
                    logger.error(f"ServicesManager._discover_services: Unexpected error processing file {file_name}: {e}", exc_info=True)
        
        except Exception as e:
            logger.error(f"ServicesManager._discover_services: Major error during service discovery: {e}", exc_info=True)
        
        logger.info(f"ServicesManager._discover_services: Discovery finished. Total services in manager: {len(self.services)}")
    
    def get_service(self, service_id: str) -> Optional[ServiceInterface]:
        """Get a service by ID with backward compatibility support."""
        # Direct lookup first
        service = self.services.get(service_id)
        if service:
            return service

        # Backward compatibility mapping
        service_aliases = {
            'langchain_service': 'langchain_code_agent'
        }

        if service_id in service_aliases:
            mapped_id = service_aliases[service_id]
            logger.info(f"ServicesManager: Backward compatibility - mapping '{service_id}' to '{mapped_id}'")
            return self.services.get(mapped_id)

        return None
    
    async def get_all_services(self) -> List[Dict[str, Any]]:
        """Get all services as dictionaries."""
        if not self.initialized:
            logger.warning("ServicesManager.get_all_services: Called before manager is initialized. Services might be incomplete or empty.")
        
        service_list_tasks = []
        for service_id, service_obj in self.services.items():
            try:
                service_list_tasks.append(service_obj.to_dict())
            except Exception as e:
                logger.error(f"Error calling to_dict() on service {service_id}: {e}", exc_info=True)
        
        services_data = []
        if service_list_tasks:
            results = await asyncio.gather(*service_list_tasks, return_exceptions=True)
            for i, res in enumerate(results):
                service_id_for_dict = list(self.services.keys())[i]
                if isinstance(res, Exception):
                    logger.error(f"Failed to get dict for service {service_id_for_dict}: {res}")
                elif isinstance(res, dict):
                    services_data.append(res)
                else:
                    logger.warning(f"Service {service_id_for_dict}.to_dict() did not return a dict: {type(res)}")
        
        logger.info(f"ServicesManager.get_all_services: Returning {len(services_data)} services.")
        return services_data
    
    async def execute_service(self, service_id: str, input_data: Any) -> ServiceResponse:
        """Execute a service with the given input data."""
        if not self.initialized:
            logger.warning(f"ServicesManager.execute_service: Called for service '{service_id}' before manager is initialized.")
            return ServiceResponse(success=False, message="ServicesManager not initialized.", error="ServicesManager not initialized.")

        service = self.get_service(service_id)
        if not service:
            available_services = list(self.services.keys())
            logger.error(f"ServicesManager.execute_service: Service not found: {service_id}")
            logger.error(f"Available services: {available_services}")
            return ServiceResponse(success=False, message=f"Service not found: {service_id}", error=f"Service not found: {service_id}")
        
        logger.info(f"ServicesManager.execute_service: Executing service '{service_id}' with input: {input_data}")
        try:
            # Store service execution start in Neo4j if graph client is available
            execution_id = None
            if self.graph:
                try:
                    execution_id = f"{service_id}_{datetime.utcnow().isoformat()}"
                    # This would need to be implemented in the Neo4j client
                    # For now, we'll just log that we would store service execution in Neo4j
                    logger.info(f"Would store service execution start in Neo4j: {execution_id}")
                    # In the future, you could implement a method like:
                    # await self.graph.store_service_execution_start(
                    #     execution_id=execution_id,
                    #     service_id=service_id,
                    #     input_data=input_data
                    # )
                except Exception as e:
                    logger.error(f"Error storing service execution start in Neo4j: {str(e)}")
            
            # Assuming ServiceResponse is a TypedDict, result will be a dictionary.
            result: Dict[str, Any] = await service.execute(input_data) # Changed type hint for clarity
            
            # Access dictionary items by key
            success_status = result.get('success', False) # Default to False if key missing
            message_content = result.get('message', 'No message content.') # Default if key missing
            logger.info(f"ServicesManager.execute_service: Service '{service_id}' executed. Success: {success_status}, Message: {message_content}")
            
            # Store service execution result in Neo4j if graph client is available
            if self.graph and execution_id:
                try:
                    # This would need to be implemented in the Neo4j client
                    # For now, we'll just log that we would store service execution result in Neo4j
                    logger.info(f"Would store service execution result in Neo4j: {execution_id}, success: {success_status}")
                    # In the future, you could implement a method like:
                    # await self.graph.store_service_execution_result(
                    #     execution_id=execution_id,
                    #     result=result
                    # )
                except Exception as e:
                    logger.error(f"Error storing service execution result in Neo4j: {str(e)}")
            
            return result
        except Exception as e:
            logger.error(f"ServicesManager.execute_service: Exception during execution of service '{service_id}': {e}", exc_info=True)
            return ServiceResponse(success=False, message=f"Error executing service {service_id}: {str(e)}", error=str(e))