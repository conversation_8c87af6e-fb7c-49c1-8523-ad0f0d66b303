import json
import traceback
from typing import Optional, Dict
from datetime import datetime
from interfaces.Assistant import Assistant
from src.type_definitions import AssistantDocument
from elastic.elastic import Elastic
import logging
from clients.qdrant import MyQdrantClient
from clients.neoj import MyNeo4jClient

from utils.logger import logger
logger = logging.getLogger(__name__)

class AssistantManager:
    def __init__(self, es: Elastic = None, qdrant: MyQdrantClient = None, graph: MyNeo4jClient = None): # Added graph parameter
        self._es = es if es is not None else Elastic()
        self._qdrant = qdrant
        self._graph = graph

    def get_or_create_assistant(self, username: str, assistant_id: Optional[str] = None) -> Optional[Assistant]:
        """
        Get an existing user or create a new one.
        
        Args:
            username (str): The username of the user
            assistant_id (str, optional): The ID of the assistant to use
            
        Returns:
            Optional[User]: The user object or None if creation fails
        """
        try:
            logger.info(f"GET OR CREATE USER : {username} {assistant_id}")
            # Use assistant_id as key if provided, otherwise use username
            user_key = assistant_id if assistant_id else username
            
            # Get user settings from Elasticsearch
            assistant = self._es.get_assistant(assistant_id)

            logger.info(f"IN GET USER WITH  : {assistant}")
            # If no settings exist, create default settings

            # Create new user instance
            assistant = Assistant(
                es=self._es,
                username=username,
                language=assistant.get('language', 'English'),
                assistant_id=assistant_id,
                model=assistant.get('model'),
                qdrant=self._qdrant,
                graph=self._graph  # Pass Neo4j client to Assistant
            )

            # Store assistant in Neo4j if graph client is available
            if self._graph:
                try:
                    # Use asyncio.run to execute the async method in a synchronous context
                    import asyncio
                    asyncio.run(self._graph._create_assistant(
                        assistant_id=assistant_id,
                        assistant_name=assistant.get('name', 'Assistant'),
                        properties=assistant
                    ))
                    logger.info(f"Stored assistant in Neo4j: {assistant_id}")
                except Exception as e:
                    logger.error(f"Error storing assistant in Neo4j: {str(e)}")

            logger.info(f"Created/retrieved user: {username} with key: {user_key}")
            return assistant

        except Exception as e:
            logger.error(f"Error in get_or_create_assistant: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def get_assistants(self) -> list:
        """Get all available assistants from Elasticsearch and Neo4j"""
        try:
            # Get assistants from Elasticsearch
            es_assistants = self._es.get_assistants() or []
            
            # If Neo4j client is available, try to get assistants from Neo4j as well
            neo4j_assistants = []
            if self._graph:
                try:
                    # This would need to be implemented in the Neo4j client
                    # For now, we'll just log that we would get assistants from Neo4j
                    logger.info("Would get assistants from Neo4j if method was implemented")
                    # In the future, you could implement a method like:
                    # neo4j_assistants = await self._graph.get_assistants()
                except Exception as e:
                    logger.error(f"Error getting assistants from Neo4j: {str(e)}")
            
            # Combine assistants from both sources, removing duplicates
            # This assumes assistants have an 'id' field for deduplication
            combined_assistants = es_assistants
            assistant_ids = {a.get('id') for a in es_assistants if 'id' in a}
            
            for assistant in neo4j_assistants:
                if assistant.get('id') not in assistant_ids:
                    combined_assistants.append(assistant)
                    assistant_ids.add(assistant.get('id'))
            
            return combined_assistants
        except Exception as e:
            logger.error(f"Error getting assistants: {str(e)}")
            return []

    def set_system_message(self, assistant_id: str, system_message: str, username: str) -> bool:
        """Set system message for an assistant"""
        try:
            success = self._es.set_system_message(
                assistant_id=assistant_id,
                system_message=system_message,
                username=username
            )
            return success
        except Exception as e:
            logger.error(f"Error in set_system_message: {str(e)}")
            return False

    def get_system_message(self, assistant_id: str) -> Optional[str]:
        """Get system message for an assistant"""
        try:
            return self._es.get_system_message(assistant_id)
        except Exception as e:
            logger.error(f"Error in get_system_message: {str(e)}")
            return None

    def update_assistant(self, username:str, assistant_id: str, updates: Dict) -> bool:
        """Update assistant properties in Elasticsearch and Neo4j"""
        logger.info("UPDATE ASSISTANT")
        try:
            current_data = self._es.get_assistant(assistant_id)
            if not current_data:
                logger.error(f"Assistant {assistant_id} not found")
                return False

            updated_data = {**current_data}
            
            for key, value in updates.items():
                if key in updated_data:
                    updated_data[key] = value
                else:
                    updated_data[key] = value
            
            system_message = updates.get('system_message')
            if system_message:
                self._es.set_system_message(assistant_id, system_message, username)
                del updates["system_message"]
            updated_data['updated_at'] = datetime.utcnow().isoformat()

            logger.info(f"Before store Assistant {updated_data}")
            
            # Update in Neo4j if graph client is available
            if self._graph:
                try:
                    # Use asyncio.run to execute the async method in a synchronous context
                    import asyncio
                    asyncio.run(self._graph._create_assistant(
                        assistant_id=assistant_id,
                        assistant_name=updated_data.get('name', 'Assistant'),
                        properties=updated_data
                    ))
                    logger.info(f"Updated assistant in Neo4j: {assistant_id}")
                except Exception as e:
                    logger.error(f"Error updating assistant in Neo4j: {str(e)}")
            
            return self._es.store_assistant(updated_data)
            
        except Exception as e:
            logger.error(f"Error in update_assistant: {str(e)}")
            return False

    def get_assistant(self, assistant_id: str) -> list:
        """Get assistant from Elasticsearch and Neo4j"""
        try:
            logger.info(f"Getting Assistant: {assistant_id}")
            # Get assistant from Elasticsearch
            es_assistant = self._es.get_assistant(assistant_id) or []
            
            # If Neo4j client is available, try to get assistant from Neo4j as well
            neo4j_assistant = None
            if self._graph:
                try:
                    # This would need to be implemented in the Neo4j client
                    # For now, we'll just log that we would get assistant from Neo4j
                    logger.info(f"Would get assistant {assistant_id} from Neo4j if method was implemented")
                    # In the future, you could implement a method like:
                    # neo4j_assistant = await self._graph.get_assistant(assistant_id)
                except Exception as e:
                    logger.error(f"Error getting assistant from Neo4j: {str(e)}")
            
            # If we have data from both sources, merge them with ES taking precedence
            if neo4j_assistant and isinstance(es_assistant, dict) and isinstance(neo4j_assistant, dict):
                merged_assistant = {**neo4j_assistant, **es_assistant}
                return merged_assistant
            
            return es_assistant
        except Exception as e:
            logger.error(f"Error getting assistant: {str(e)}")
            return []

    def set_assistant(self, data: Dict) -> bool:
        """Store assistant in Elasticsearch and Neo4j"""
        # Store in Neo4j if graph client is available
        if self._graph and 'id' in data:
            try:
                # Use asyncio.run to execute the async method in a synchronous context
                import asyncio
                asyncio.run(self._graph._create_assistant(
                    assistant_id=data['id'],
                    assistant_name=data.get('name', 'Assistant'),
                    properties=data
                ))
                logger.info(f"Stored assistant in Neo4j: {data['id']}")
            except Exception as e:
                logger.error(f"Error storing assistant in Neo4j: {str(e)}")
        
        # Store in Elasticsearch
        return self._es.store_assistant(data)
 