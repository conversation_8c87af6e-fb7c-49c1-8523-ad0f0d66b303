from typing import Dict, Any, List, Optional
from elastic.elastic import Elastic
from qdrant.operations.workflows import get_workflow_manager
from utils.logger import logger
from managers.ServicesManager import ServicesManager
from src.type_definitions.service import ServiceResponse
from clients.neoj import MyNeo4jClient
import anyio as asyncio
from datetime import datetime

class WorkflowManager:
    """Manages the execution of workflows"""
    
    def __init__(self, graph: MyNeo4jClient = None):
        self._es = Elastic()
        self._services_manager = ServicesManager.get_instance()
        self._graph = graph
    
    async def execute_workflow(self, workflow_id: str, input_data: Dict[str, Any]) -> Any: # Or Union[ServiceResponse, str]
        """
        Execute a workflow with the given input data
        
        Args:
            workflow_id: The ID of the workflow to execute
            input_data: The input data for the workflow
            
        Returns: # Updated to reflect actual return type
            The result of the workflow execution, typically a ServiceResponse object or an error string.
        """
        logger.info(f"Executing (MANAGER) workflow: {workflow_id} with input: {input_data}")
        try:
            # Get workflow from Elasticsearch
            workflow = self._get_workflow(workflow_id)
            if not workflow:
                logger.error(f"Workflow not found: {workflow_id}")
                return "Error: Workflow not found"
            
            # Store workflow execution start in Neo4j if graph client is available
            execution_id = None
            if self._graph:
                try:
                    execution_id = f"{workflow_id}_{datetime.utcnow().isoformat()}"
                    # This would need to be implemented in the Neo4j client
                    # For now, we'll just log that we would store workflow execution in Neo4j
                    logger.info(f"Would store workflow execution start in Neo4j: {execution_id}")
                    # In the future, you could implement a method like:
                    # await self._graph.store_workflow_execution_start(
                    #     execution_id=execution_id,
                    #     workflow_id=workflow_id,
                    #     input_data=input_data
                    # )
                except Exception as e:
                    logger.error(f"Error storing workflow execution start in Neo4j: {str(e)}")
            
            # Execute the workflow
            result = await self._execute_workflow_blocks(workflow, input_data, execution_id)
            logger.info(f"Executing (MANAGER) Result: {result}")
            
            # Store workflow execution result in Neo4j if graph client is available
            if self._graph and execution_id:
                try:
                    # This would need to be implemented in the Neo4j client
                    # For now, we'll just log that we would store workflow execution result in Neo4j
                    logger.info(f"Would store workflow execution result in Neo4j: {execution_id}")
                    # In the future, you could implement a method like:
                    # await self._graph.store_workflow_execution_result(
                    #     execution_id=execution_id,
                    #     result=result
                    # )
                except Exception as e:
                    logger.error(f"Error storing workflow execution result in Neo4j: {str(e)}")
            
            # Return the final result
            return result
                
        except Exception as e:
            logger.error(f"Error executing workflow: {str(e)}")
            return f"Error executing workflow: {str(e)}"
    
    def _get_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get a workflow by ID from Qdrant"""
        try:
            workflow_manager = get_workflow_manager()
            workflow = workflow_manager.get_workflow(workflow_id)
            return workflow
        except Exception as e:
            logger.error(f"Error retrieving workflow: {str(e)}")
            return None
    
    async def _execute_workflow_blocks(self, workflow: Dict[str, Any], input_data: Dict[str, Any], execution_id: Optional[str] = None) -> Any:
        """Execute all blocks in the workflow in the correct order"""
        # Create a dictionary to store intermediate results
        block_results: Dict[str, ServiceReponse] = {}
        
        # Get blocks and connections
        blocks = workflow.get('blocks', [])
        connections = workflow.get('connections', [])
        logger.info(f"Workflow Manager: Starting _execute_workflow_blocks for workflow_id: '{workflow.get('id', 'N/A')}'. Blocks: {len(blocks)}, Connections: {len(connections)}")
        
        logger.info(f"Executing workflow with {len(blocks)} blocks and {len(connections)} connections")
        
        # Create a dependency graph
        dependency_graph = self._create_dependency_graph(blocks, connections)
        
        # Sort blocks by dependency order
        execution_order = self._topological_sort(dependency_graph)
        # The warning about '-1' would have already occurred if present in dependency_graph construction or sort.
        logger.info(f"Block execution order: {execution_order}")
        
        # Execute blocks in order
        for block_id in execution_order:
            block = next((b for b in blocks if b['id'] == block_id), None)
            if not block:
                logger.warning(f"Block {block_id} not found in workflow")
                continue
            
            # Get input for this block from connected blocks
            block_input = self._get_block_input(block, connections, block_results, input_data)
            
            # Execute the block
            service_id = block.get('serviceId') or block.get('type')
            logger.info(f"Executing block {block_id} with service_id: {service_id}")
            logger.info(f"Block data: {block}")

            if not service_id:
                logger.error(f"Block {block_id} has no service ID")
                continue
            
            # Check if service exists before trying to execute it
            available_services = list(self._services_manager.services.keys())
            logger.info(f"Available services: {available_services}")
            if service_id not in available_services:
                error_msg = f"[workflow_manager]Service not found: {service_id}"
                logger.error(error_msg)
                block_results[block_id] = f"Error: {error_msg}"
                continue
        
            try:
                # Execute the service with all available data
                service_input = {
                    'config': block.get('config', {}),
                    'input': block_input,
                    'messages': input_data.get('messages', []),
                    'text': block_input.get('text', '')
                }
                
                # If we have a user message in the messages, extract it for convenience
                if 'messages' in input_data:
                    for msg in reversed(input_data['messages']):
                        if msg.get('role') == 'user':
                            service_input['user_message'] = msg.get('content', '')
                            break
                
                # Store block execution start in Neo4j if graph client is available
                if self._graph and execution_id:
                    try:
                        # This would need to be implemented in the Neo4j client
                        # For now, we'll just log that we would store block execution start in Neo4j
                        logger.info(f"Would store block execution start in Neo4j: {execution_id}, block: {block_id}")
                        # In the future, you could implement a method like:
                        # await self._graph.store_block_execution_start(
                        #     execution_id=execution_id,
                        #     block_id=block_id,
                        #     service_id=service_id,
                        #     input_data=service_input
                        # )
                    except Exception as e:
                        logger.error(f"Error storing block execution start in Neo4j: {str(e)}")
                
                # Execute the service
                result = await self._services_manager.execute_service(
                    service_id=service_id,
                    input_data=service_input
                )
                
                # Store block execution result in Neo4j if graph client is available
                if self._graph and execution_id:
                    try:
                        # This would need to be implemented in the Neo4j client
                        # For now, we'll just log that we would store block execution result in Neo4j
                        logger.info(f"Would store block execution result in Neo4j: {execution_id}, block: {block_id}")
                        # In the future, you could implement a method like:
                        # await self._graph.store_block_execution_result(
                        #     execution_id=execution_id,
                        #     block_id=block_id,
                        #     result=result
                        # )
                    except Exception as e:
                        logger.error(f"Error storing block execution result in Neo4j: {str(e)}")
                
                # Store the result
                block_results[block_id] = result
                logger.info(f"Block {block_id} execution result: {result}")
                
            except Exception as e:
                logger.error(f"Error executing block {block_id}: {str(e)}")
                block_results[block_id] = f"Error: {str(e)}"
        
        # Return the result of the last block in the execution order
        if execution_order and execution_order[-1] in block_results:
            result = block_results[execution_order[-1]]
            logger.info(f"Returning result from last block {execution_order[-1]}: {result}")
            return result
        elif block_results:
            # If we can't find the last block but we have results, return the result of any block
            last_block_id = list(block_results.keys())[-1]
            result = block_results[last_block_id]
            logger.info(f"Returning result from block {last_block_id}: {result}")
            return result
        
        return "No result from workflow execution"
    
    def _create_dependency_graph(self, blocks: List[Dict[str, Any]], connections: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Create a dependency graph from blocks and connections"""
        graph = {block['id']: [] for block in blocks}
        
        for connection in connections:
            source_id = connection.get('sourceId')
            target_id = connection.get('targetId')
            
            if source_id and target_id:
                if target_id in graph:
                    graph[target_id].append(source_id)
        
        return graph
    
    def _topological_sort(self, graph: Dict[str, List[str]]) -> List[str]:
        """Sort blocks in topological order (dependencies first)"""
        visited = set()
        temp = set()
        order = []
        # Store nodes that are part of a detected cycle to avoid re-warning
        nodes_in_cycle = set()
        
        logger.debug(f"Workflow Manager: Starting _topological_sort with graph: {graph}")
        def visit(node):
            # Special handling for conventional "start node" IDs.
            # These are not actual blocks in the graph but represent initial workflow inputs.
            # Common examples might be -1 (integer) or "-1" (string).
            CONVENTIONAL_START_NODE_IDS = ["-1", -1, "-2", -2] # Add any other conventional non-block IDs
            if node in CONVENTIONAL_START_NODE_IDS or str(node) in CONVENTIONAL_START_NODE_IDS:
                logger.debug(f"Workflow Manager: Node '{node}' is a conventional start/end node ID. Not an executable block.")
                return # Do not process as a regular block, do not warn.

            # Check 1: Is this node a valid block defined in the workflow?
            # (Especially important when 'node' is a 'dependency_id' from a connection)
            if node not in graph:
                logger.warning(f"Workflow Manager: Encountered a connection pointing to a non-existent or non-conventional block ID '{node}'. This connection will be ignored in the execution plan.")
                return

            # Check 2: Cycle detection
            if node in temp:
                if node not in nodes_in_cycle: # Log only once per node involved in a cycle
                    logger.warning(f"Workflow Manager: Cycle detected involving block ID '{node}'. Blocks in this cycle may not execute as expected.")
                    nodes_in_cycle.add(node)
                return # Stop recursion for this path to prevent infinite loop
            
            # Check 3: Already visited and processed
            if node in visited:
                return
                
            temp.add(node) # Mark as "currently visiting" for cycle detection in this path
            
            # Recursively visit all dependencies of the current node
            for dependency_id in graph.get(node, []): # graph.get(node,[]) should be safe due to check 1 for 'node'
                visit(dependency_id) 
                
            temp.remove(node) # Unmark as "currently visiting"
            visited.add(node) # Mark as fully processed
            order.append(node) # Add to the linear execution order
        
        for node in graph:
            if node not in visited:
                visit(node)
                
        # The 'order' list is already in "dependencies first" order due to the DFS traversal.
        return order
    
    def _get_block_input(self, block: Dict[str, Any], connections: List[Dict[str, Any]], 
                         block_results: Dict[str, Any], initial_input: Dict[str, Any]) -> Dict[str, Any]:
        """Get input for a block from connected blocks or initial input"""
        block_id = block['id']
        
        # Find connections where this block is the target
        input_connections = [c for c in connections if c.get('targetId') == block_id]
        
        if not input_connections:
            # If this is a starting block with no inputs, use the initial input
            return initial_input
        
        # Combine results from all input blocks
        combined_input = {}
        
        for connection in input_connections:
            source_id = connection.get('sourceId')
            if source_id in block_results:
                # Add the source block's result to the input
                source_result = block_results[source_id]
                
                if isinstance(source_result, dict):
                    combined_input.update(source_result)
                else:
                    # If the result is not a dict, use it as 'text' input
                    combined_input['text'] = str(source_result)
        
        # If we have no combined input but we do have initial input, use that
        if not combined_input:
            return initial_input
            
        return combined_input
