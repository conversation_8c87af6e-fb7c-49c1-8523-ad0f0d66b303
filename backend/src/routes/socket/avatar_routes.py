from fastapi import APIRouter, HTTPException, Body, Depends
from fastapi.responses import JSONResponse
import os
import base64
from datetime import datetime
from src.elastic.elastic import Elastic
from qdrant.operations.assistants import get_assistant_manager
from elasticsearch.exceptions import NotFoundError
import logging
from src.type_definitions.elastic_types import AvatarDocument # Assuming this is correctly pathed
from src.routes.socket.socket_server import SocketServer # For type hinting
from fastapi import FastAPI # For type hinting app

logger = logging.getLogger('chat_app')

# Create an APIRouter instance
# The prefix can be set here if these were HTTP routes, but for Socket.IO events,
# the event names themselves are the "routes".
# If you plan to add HTTP endpoints for avatars later, you can use the prefix.
router = APIRouter()

def init_avatar_routes(app: FastAPI, socket_server: SocketServer):
    @socket_server.async_handle_event('uploadAvatar') # Changed to async_handle_event
    async def handle_upload_avatar(sid: str, data: dict): # Add sid, type data
        try:
            logger.info("[AVATAR] Starting avatar upload process")
            username = data.get('username')
            avatar_data = data.get('avatar')
            
            if not username or not avatar_data:
                logger.error("[AVATAR] Missing required data")
                return { # Return a dict for consistency with error handling
                    'success': False,
                    'message': 'Missing required data (username or avatar)'
                }

            # Extract the base64 data, removing any prefix
            if isinstance(avatar_data, str) and avatar_data.startswith('data:'):
                # Extract just the base64 part
                parts = avatar_data.split(',', 1)
                if len(parts) > 1:
                    avatar_data = parts[1]  # Store only the base64 part without prefix
            
            # Decode base64 data
            try:
                _ = base64.b64decode(avatar_data) # Validate decoding, store if needed for validation
            except Exception as e:
                logger.error(f"[AVATAR] Base64 decode error: {str(e)}")
                return {
                    'success': False,
                    'message': 'Invalid image data'
                }

            # Store in ES
            try:
                es = Elastic().es
                avatar_doc: AvatarDocument = {
                    'username': username,
                    'type': 'user',
                    'image_data': avatar_data,  # Store clean base64 without prefix
                    'updated_at': datetime.utcnow().isoformat()
                }

                es.update( # REMOVE await: es.update is synchronous
                    index='user-avatars',
                    id=username,
                    body={'doc': avatar_doc, 'doc_as_upsert': True},
                    retry_on_conflict=5
                )
                logger.info(f"[AVATAR] Successfully saved avatar for user: {username}")
 
            except Exception as e:
                logger.error(f"[AVATAR] Failed to update ES document: {str(e)}")
                # The socket_server.handle_event decorator should handle emitting errors
                # based on the returned dictionary.
                return {
                    'success': False,
                    'message': 'Failed to save avatar'
                }
            
            return {
                'success': True,
                'data': avatar_data,
                'username': username,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"[AVATAR] Error saving avatar: {str(e)}")
            # Let the decorator handle the error emission
            return {
                'success': False,
                'message': str(e)
            }

    @socket_server.async_handle_event('uploadAssistantAvatar') # Changed to async_handle_event
    async def handle_assistant_avatar_upload(sid: str, data: dict): # Add sid, type data
        try:
            logger.info("[AVATAR] Starting assistant avatar upload process")
            username = data.get('username')
            ai_user = data.get('assistantId')
            avatar_data = data.get('avatar')
            requestId = data.get('requestId')
            
            if not username or not avatar_data or not ai_user:
                logger.error(f"[AVATAR] Missing required data - username: {bool(username)}, ai_user: {bool(ai_user)}, avatar_data: {bool(avatar_data)}")
                
                return  {
                    'success': False,
                    'message': 'Missing required data'
                }

            # Decode base64 data
            try:
                _ = base64.b64decode(avatar_data) # Validate decoding
            except Exception as e:
                logger.error(f"[AVATAR] Base64 decode error: {str(e)}")
                return {
                    'success': False,
                    'message': 'Invalid image data'
                }

            # Store in Qdrant
            try:
                assistant_manager = get_assistant_manager()
                success = await assistant_manager.store_avatar(ai_user, avatar_data)
                if success:
                    logger.info(f"[AVATAR] Successfully saved/updated assistant avatar for: {ai_user}")
                else:
                    logger.error(f"[AVATAR] Failed to store avatar in Qdrant for: {ai_user}")
            except Exception as e:
                logger.error(f"[AVATAR] Failed to store avatar in Qdrant: {str(e)}")
                # Continue even if Qdrant update fails
            
            # Emit success response with exactly matching event name
            response_data = {
                'success': True,
                'data': avatar_data,
                'requestId': requestId
            }
            
            return response_data
        
        except Exception as e:
            logger.error(f"[AVATAR] Error saving assistant avatar: {str(e)}")
            logger.error("[AVATAR] Full traceback:", exc_info=True)
            return {
                'success': False,
                'message': str(e),
                'requestId': requestId
            }

    @socket_server.async_handle_event('getAvatar') # Changed to async_handle_event
    async def handle_get_avatar(sid: str, data: dict): # Add sid, type data
        try:
            username = data.get('username')
            is_assistant = data.get('isAssistant', False)
            request_id = data.get('requestId')
            
            if not username:
                logger.warning("[AVATAR] No username provided in request")
                return {
                    'success': False,
                    'message': 'Missing username',
                    'requestId': request_id
                }
                
            try:
                if is_assistant:
                    # Get assistant avatar from Qdrant
                    assistant_manager = get_assistant_manager()
                    avatar_data = assistant_manager.get_avatar(username)

                    if avatar_data:
                        return {
                            'success': True,
                            'data': avatar_data,
                            'requestId': request_id
                        }
                    else:
                        logger.info(f"[AVATAR] No assistant avatar found for {username}")
                        return {
                            'success': False,
                            'message': 'Avatar not found',
                            'requestId': request_id
                        }
                else:
                    # For user avatars, still use Elasticsearch for now
                    # TODO: Migrate user avatars to Qdrant as well
                    es = Elastic().es
                    result = es.get(
                        index='user-avatars',
                        id=username
                    )

                    if result.get('found') and result.get('_source', {}).get('image_data'):
                        return {
                            'success': True,
                            'data': result['_source']['image_data'],
                            'requestId': request_id
                        }
                    else:
                        logger.info(f"[AVATAR] No user avatar found for {username}")
                        return {
                            'success': False,
                            'message': 'Avatar not found',
                            'requestId': request_id
                        }

            except Exception as e:
                logger.error(f"[AVATAR] Error retrieving avatar: {str(e)}")
                return {
                    'success': False,
                    'message': 'Error retrieving avatar',
                    'requestId': request_id
                }
                
        except Exception as e:
            logger.error(f"[AVATAR] Error in handle_get_avatar: {str(e)}")
            return {
                'success': False,
                'message': str(e),
                'requestId': request_id
            }

    # For direct socketio.on handlers, ensure they are async if they perform async operations.
    # The SocketServer class's emit method might need to be async if it uses an async socketio client.
    @socket_server.socketio.on('getAvatarsBatch')
    async def handle_get_avatars_batch(sid: str, data: dict): # Add sid, type data
        try:
            usernames = data.get('usernames', [])
            is_assistant = data.get('isAssistant', False)
            request_id = data.get('requestId')
            
            if not usernames:
                return
            
            logger.info(f"[AVATAR] Received batch avatar request for {len(usernames)} users")
            
            es = Elastic().es
            index = 'assistant-avatars' if is_assistant else 'user-avatars'
            
            # Bulk get from ES
            results = es.mget( # REMOVE await: es.mget is synchronous
                body={'ids': usernames},
                index=index
            )
            
            # Process results
            avatar_data = {}
            for doc in results['docs']:
                if doc.get('found'):
                    username = doc['_id']
                    avatar_data[username] = doc['_source'].get('image_data')
            
            # Use socket_server.emit for consistency if it handles async correctly
            # or ensure socket_server.socketio.emit is from python-socketio and can be awaited if needed.
            await socket_server.socketio.emit('avatarsBatchResponse', { # Assuming python-socketio's emit
                'success': True,
                'data': avatar_data,
                'requestId': request_id
            })
        
        except Exception as e:
            logger.error(f"[AVATAR] Error in batch avatar fetch: {str(e)}")
            await socket_server.socketio.emit('avatarsBatchResponse', { # Assuming python-socketio's emit
                'success': False,
                'data': {}
            })

    # Add initialization code to ensure indices exist
    async def init_indices(): # Changed to async def
        es = Elastic().es
        indices = ['user-avatars', 'assistant-avatars']
        
        for index in indices:
            if not es.indices.exists(index=index): # REMOVE await
                es.indices.create( # REMOVE await
                    index=index,
                    body={
                        'mappings': {
                            'properties': {
                                'username': {'type': 'keyword'},
                                'image-data': {'type': 'binary'},
                                'type': {'type': 'keyword'},
                                'updated_at': {'type': 'date'}
                            }
                        }
                    }
                )
                logger.info(f"[AVATAR] Created index: {index}")

    # Initialize indices when routes are set up
    # This should be called in an async context, e.g., during app startup
    # For now, we'll define it. The actual call needs to be handled in main.py's async startup.
    # async def startup_event():
    #     try:
    #         await init_indices()
    #     except Exception as e:
    #         logger.error(f"[AVATAR] Failed to initialize indices: {str(e)}")
    # app.add_event_handler("startup", startup_event)

    # If you had HTTP routes in avatar_bp, you would do:
    # app.include_router(router)
    # Since these are Socket.IO events registered via socket_server,
    # including the router might not be necessary unless it also contains HTTP routes.
