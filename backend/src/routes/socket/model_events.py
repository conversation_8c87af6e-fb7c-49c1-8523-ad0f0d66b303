from typing import List
# from flask import request # Not directly used in this file's current state
from utils.logger import logger
from elastic.elastic import Elastic
from datetime import datetime, timezone
import openai
import anyio # Import anyio for to_thread.run_sync
import os
import traceback

# Define available models
AVAILABLE_MODELS = [
    'gpt-4',
    'gpt-3.5-turbo',
    'claude-2'
]

def format_custom_model_name(model_id):
    """Format custom model name to be more user-friendly"""
    parts = model_id.split(':')
    base_model = parts[1] if len(parts) > 1 else 'unknown'
    project = parts[2] if len(parts) > 2 else 'custom'
    # Remove checkpoint info from display name
    if ':ckpt-step-' in model_id:
        checkpoint = model_id.split(':ckpt-step-')[1]
        return f'Custom {base_model} - {project} (checkpoint {checkpoint})'
    return f'Custom {base_model} - {project}'

# Define handler functions at module level so they can be imported and decorated

async def handle_select_model(sid: str, data: dict): # Add sid, make async
    """Handle model selection request"""
    request_id = data.get('requestId') # Capture requestId early
    try:
        model_id = data.get('modelId')
        assistant_id = data.get('assistantId')
        
        if not model_id:
            logger.error("[MODEL] No modelId provided")
            return {'success': False, 'error': "Model ID is required", 'requestId': request_id}
        
        if not assistant_id:
            logger.error("[MODEL] No assistantId provided")
            return {'success': False, 'error': "Assistant ID is required", 'requestId': request_id}
        
        # Validate model exists in our available models
        if model_id not in AVAILABLE_MODELS and not model_id.startswith('ft:'):
            logger.error(f"[MODEL] Invalid model ID: {model_id}")
            return {'success': False, 'error': f"Invalid model ID: {model_id}", 'requestId': request_id}
        
        # Update the assistant's model in Elasticsearch
        es = Elastic()
        # Assuming update_assistant_model is synchronous, run in thread
        success = await anyio.to_thread.run_sync(es.update_assistant_model, assistant_id, model_id)
        
        if not success:
            logger.error(f"[MODEL] Failed to update model for assistant {assistant_id}")
            return {'success': False, 'error': "Failed to update assistant model", 'requestId': request_id}
        
        logger.info(f"[MODEL] Model {model_id} set for assistant {assistant_id}")
        
        return {
            'success': True,
            'modelId': model_id,
            'assistantId': assistant_id,
            'status': 'success', # Kept for compatibility, though 'success' is standard
            'timestamp': datetime.now().isoformat(),
            'requestId': request_id
        }
        
    except Exception as e:
        error_msg = f"Error selecting model: {str(e)}"
        logger.error(f"[MODEL] {error_msg}")
        logger.error(f"[MODEL] Traceback: {traceback.format_exc()}")
        return {'success': False, 'error': error_msg, 'requestId': request_id}

async def handle_delete_model(sid: str, data: dict): # Add sid, make async
    """Handle request to delete a custom fine-tuned model"""
    logger.info(f"Received deleteModel request: {data}")
    model_id_to_delete = data.get('modelId')
    request_id = data.get('requestId')

    if not model_id_to_delete:
        logger.error("[MODEL_DELETE] modelId not provided.")
        return {'success': False, 'error': "modelId is required.", 'requestId': request_id}

    if not model_id_to_delete.startswith('ft:'):
        logger.error(f"[MODEL_DELETE] Attempt to delete non-custom model: {model_id_to_delete}")
        return {'success': False, 'error': "Only custom fine-tuned models (starting with 'ft:') can be deleted.", 'requestId': request_id}

    try:
        openai.api_key = os.getenv('OPENAI_API_KEY')
        if not openai.api_key:
            logger.error("[MODEL_DELETE] OpenAI API key not configured.")
            return {'success': False, 'error': "OpenAI API key not configured.", 'requestId': request_id}

        # OpenAI SDK calls are typically blocking I/O, so run in a thread
        delete_response = await asyncio.to_thread.run_sync(openai.models.delete, model_id_to_delete)

        if delete_response and delete_response.deleted:
            logger.info(f"[MODEL_DELETE] Successfully deleted model: {model_id_to_delete}")
            return {
                'success': True,
                'message': f"Model {model_id_to_delete} deleted successfully.",
                'modelId': model_id_to_delete,
                'requestId': request_id
            }
        else:
            logger.error(f"[MODEL_DELETE] Failed to delete model {model_id_to_delete}. Response: {delete_response}")
            return {
                'success': False,
                'error': f"Failed to delete model {model_id_to_delete}. OpenAI API did not confirm deletion.",
                'modelId': model_id_to_delete,
                'requestId': request_id
            }

    except openai.APIError as e: 
        logger.error(f"[MODEL_DELETE] OpenAI API error deleting model {model_id_to_delete}: {str(e)}")
        return {'success': False, 'error': f"OpenAI API error: {str(e)}", 'modelId': model_id_to_delete, 'requestId': request_id}
    except Exception as e:
        error_msg = f"Unexpected error deleting model {model_id_to_delete}: {str(e)}"
        logger.error(f"[MODEL_DELETE] {error_msg}\nTraceback: {traceback.format_exc()}")
        return {'success': False, 'error': error_msg, 'modelId': model_id_to_delete, 'requestId': request_id}

async def handle_get_models(sid: str, data: dict): # Add sid, make async
    """Handle request for available models"""
    logger.info("Received getModels request")
    requestId = data.get('requestId')
    try:
        base_models = [
            {
                'model_id': 'gpt-4',
                'name': 'GPT-4',
                'description': 'Most capable GPT-4 model, better at complex tasks',
                'status': 'active',
                'type': 'base',
                'last_updated': datetime.now().isoformat(),
                'capabilities': ['chat', 'completion', 'advanced-reasoning']
            },
            {
                'model_id': 'gpt-3.5-turbo',
                'name': 'GPT-3.5 Turbo',
                'description': 'Fast and efficient for most tasks',
                'status': 'active',
                'type': 'base',
                'last_updated': datetime.now().isoformat(),
                'capabilities': ['chat', 'completion']
            },
            {
                'model_id': 'claude-2',
                'name': 'Claude 2',
                'description': 'Anthropic\'s Claude 2 model for advanced reasoning',
                'status': 'active',
                'type': 'base',
                'last_updated': datetime.now().isoformat(),
                'capabilities': ['chat', 'completion', 'advanced-reasoning']
            }
        ]
        logger.info(f"[MODEL] Base models prepared: {len(base_models)} models")

        trained_models = []
        try:
            logger.info("[MODEL] Attempting to fetch custom models from OpenAI")
            openai.api_key = os.getenv('OPENAI_API_KEY')
            
            if not openai.api_key:
                logger.warning("[MODEL] OpenAI API key not found in environment variables")
                raise ValueError("OpenAI API key not configured")

            logger.info("[MODEL] Calling OpenAI API to list fine-tuned models")
            # OpenAI SDK calls are typically blocking I/O
            custom_models_response = await anyio.to_thread.run_sync(openai.models.list)

            for model in custom_models_response.data:
                if 'ft:' in model.id:
                    trained_models.append({
                        'model_id': model.id,
                        'name': format_custom_model_name(model.id),
                        'description': 'Fine-tuned custom model',
                        'status': 'active',
                        'last_updated': datetime.now(timezone.utc).isoformat(),
                        'capabilities': ['chat', 'completion'],
                        'type': 'custom'
                    })
            
            all_models = base_models + trained_models
            logger.info(f"[MODEL] Total models prepared: {len(all_models)} ({len(base_models)} base, {len(trained_models)} custom)")
            
        except Exception as e:
            logger.error(f"[MODEL] Error fetching custom models: {str(e)}")
            logger.error(f"[MODEL] Full traceback: {traceback.format_exc()}")
            all_models = base_models 
            logger.info("[MODEL] Falling back to base models only due to error")

        return {
            'success': True,
            'models': all_models,
            'requestId': requestId
        }
        
    except Exception as e:
        error_msg = f"Error fetching models: {str(e)}"
        logger.error(f"[MODEL] {error_msg}")
        logger.error(f"[MODEL] Full traceback: {traceback.format_exc()}")
        return {'success': False, 'error': error_msg, 'requestId': data.get('requestId')}

async def handle_get_current_model(sid: str, data: dict): # Add sid, make async
    """Handle request for current model"""
    request_id = data.get('requestId') # Capture requestId early
    try:
        assistant_id = data.get('assistantId')
        
        if not assistant_id:
            logger.warning("[MODEL] No assistant ID provided")
            return {'success': False, 'error': 'Assistant ID is required', 'requestId': request_id}
        
        es = Elastic()
        # Assuming es.get_assistant is synchronous, run in thread
        # If it were async, you would just await it directly.
        assistant_data = await es.get_assistant(assistant_id) # Make async if es.get_assistant is async
        
        if not assistant_data:
            logger.warning(f"[MODEL] No assistant data found for ID: {assistant_id}")
            return {'success': False, 'error': 'Assistant not found', 'requestId': request_id}
        
        current_model = assistant_data.get('model', 'gpt-3.5-turbo')
        logger.info(f"[MODEL] Current model for assistant {assistant_id}: {current_model}")
        
        return {
            'success': True,
            'modelId': current_model,
            'assistantId': assistant_id,
            'requestId': request_id
        }
        
    except Exception as e:
        error_msg = f"Error getting current model: {str(e)}"
        logger.error(f"[MODEL] {error_msg}")
        logger.error(f"[MODEL] Full traceback: {traceback.format_exc()}")
        return {'success': False, 'error': error_msg, 'requestId': request_id}

def init_model_routes(socket_server):
    # Decorate the module-level functions.
    # The `(handle_select_model)` part applies the decorator to the function.
    # The socket_server.handle_event decorator correctly handles async functions.
    socket_server.handle_event('selectModel')(handle_select_model)
    socket_server.handle_event('deleteModel')(handle_delete_model)
    socket_server.handle_event('getModels')(handle_get_models)
    socket_server.handle_event('getCurrentModel')(handle_get_current_model)
