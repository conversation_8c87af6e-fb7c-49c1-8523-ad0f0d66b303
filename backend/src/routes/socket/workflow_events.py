import logging
import uuid
from typing import Dict, Any, List
from datetime import datetime
from elastic.elastic import Elastic
from qdrant.operations.workflows import get_workflow_manager

logger = logging.getLogger(__name__)

def init_workflow_routes(socket_server):
    """Initialize workflow-related socket routes."""
    
    @socket_server.handle_event('saveWorkflow')
    async def handle_save_workflow(sid: str, data: Dict[str, Any]):
        """Save a workflow."""
        try:
            logger.info("Received request to save workflow")
            
            # Validate input
            workflow = data.get('workflow')
            request_id = data.get('requestId')
            
            if not workflow:
                raise ValueError("Workflow data is required")
            
            # Get Qdrant workflow manager
            workflow_manager = get_workflow_manager()

            # Save to Qdrant (this handles ID generation and timestamps)
            result = await workflow_manager.save_workflow(workflow)
            
            logger.info(f"Workflow saved successfully with ID: {result['workflow_id']}")

            # Return success response
            return {
                'success': True,
                'data': {
                    'id': result['workflow_id'],
                    'name': workflow['name']
                },
                'requestId': request_id
            }
        
        except Exception as e:
            logger.error(f"Error saving workflow: {e}")
            return {
                'success': False,
                'error': f"Failed to save workflow: {str(e)}",
                'requestId': data.get('requestId')
            }
    
    @socket_server.handle_event('getWorkflows')
    async def handle_get_workflows(sid: str, data: Dict[str, Any]):
        """Get all saved workflows."""
        try:
            logger.info("Received request for workflows")
            request_id = data.get('requestId')
            
            # Get Qdrant workflow manager
            workflow_manager = get_workflow_manager()

            # Get workflows from Qdrant
            workflows = workflow_manager.list_workflows(limit=100)
            
            logger.info(f"Returning {len(workflows)} workflows")
            
            # Return workflows
            return {
                'success': True,
                'data': workflows,
                'requestId': request_id
            }
        
        except Exception as e:
            logger.error(f"Error getting workflows: {e}")
            return {
                'success': False,
                'error': f"Failed to get workflows: {str(e)}",
                'requestId': data.get('requestId')
            }
    
    @socket_server.handle_event('getWorkflow')
    async def handle_get_workflow(sid: str, data: Dict[str, Any]):
        """Get a specific workflow by ID."""
        try:
            logger.info(f"Received request for workflow: {data.get('workflowId')}")
            
            # Validate input
            workflow_id = data.get('workflowId')
            request_id = data.get('requestId')
            
            if not workflow_id:
                raise ValueError("Workflow ID is required")
            
            # Get Elasticsearch instance
            es = Elastic()
            
            # Get workflow from Elasticsearch
            try:
                result = es.es.get(
                    index='workflows',
                    id=workflow_id
                )
                workflow = result['_source']
            except Exception as e:
                logger.error(f"Error retrieving workflow: {e}")
                return {
                    'success': False,
                    'error': f"Workflow not found: {workflow_id}",
                    'requestId': request_id
                }
            
            logger.info(f"Returning workflow: {workflow_id}")
            
            # Return workflow
            return {
                'success': True,
                'data': workflow,
                'requestId': request_id
            }
        
        except Exception as e:
            logger.error(f"Error getting workflow: {e}")
            return {
                'success': False,
                'error': f"Failed to get workflow: {str(e)}",
                'requestId': data.get('requestId')
            }
    
    @socket_server.handle_event('executeWorkflow')
    async def handle_execute_workflow(sid: str, data: Dict[str, Any]):
        """Execute a workflow."""
        try:
            logger.info(f"Received request to execute workflow: {data.get('workflowId')}")
            
            # Validate input
            workflow_id = data.get('workflowId')
            request_id = data.get('requestId')
            
            if not workflow_id:
                raise ValueError("Workflow ID is required")
            
            # Get Elasticsearch instance
            es = Elastic()
            
            # Get workflow from Elasticsearch
            try:
                result = es.es.get(
                    index='workflows',
                    id=workflow_id
                )
                workflow = result['_source']
            except Exception as e:
                logger.error(f"Error retrieving workflow: {e}")
                return {
                    'success': False,
                    'error': f"Workflow not found: {workflow_id}",
                    'requestId': request_id
                }
            
            # Get services manager
            from managers.ServicesManager import ServicesManager
            services_manager = ServicesManager.get_instance()
            
            # Execute workflow
            # This is a simplified implementation - in a real system, you would need to:
            # 1. Topologically sort the blocks based on connections
            # 2. Execute each block in order
            # 3. Pass outputs from one block to inputs of connected blocks
            
            logger.info(f"Executing workflow: {workflow_id}")
            
            # For now, just execute the first block as an example
            if workflow['blocks']:
                first_block = workflow['blocks'][0]
                service_id = first_block['type']
                config = first_block['config']
                
                # Execute the service
                result = await services_manager.execute_service(
                    service_id=service_id,
                    input_data={
                        'config': config,
                        'input': {}
                    }
                )
                
                logger.info(f"Workflow execution result: {result}")
                
                # Return result
                return {
                    'success': True,
                    'data': {
                        'result': result,
                        'message': 'Workflow executed successfully'
                    },
                    'requestId': request_id
                }
            else:
                return {
                    'success': False,
                    'error': 'Workflow has no blocks to execute',
                    'requestId': request_id
                }
        
        except Exception as e:
            logger.error(f"Error executing workflow: {e}")
            return {
                'success': False,
                'error': f"Failed to execute workflow: {str(e)}",
                'requestId': data.get('requestId')
            }
    
    @socket_server.handle_event('deleteWorkflow')
    async def handle_delete_workflow(sid: str, data: Dict[str, Any]):
        """Delete a workflow."""
        try:
            logger.info(f"Received request to delete workflow: {data.get('workflowId')}")
            
            # Validate input
            workflow_id = data.get('workflowId')
            request_id = data.get('requestId')
            
            if not workflow_id:
                raise ValueError("Workflow ID is required")
            
            # Get Elasticsearch instance
            es = Elastic()
            
            # Delete workflow from Elasticsearch
            try:
                es.es.delete(
                    index='workflows',
                    id=workflow_id,
                    refresh=True
                )
            except Exception as e:
                logger.error(f"Error deleting workflow: {e}")
                return {
                    'success': False,
                    'error': f"Workflow not found or could not be deleted: {workflow_id}",
                    'requestId': request_id
                }
            
            logger.info(f"Workflow deleted: {workflow_id}")
            
            # Return success response
            return {
                'success': True,
                'data': {
                    'id': workflow_id,
                    'message': 'Workflow deleted successfully'
                },
                'requestId': request_id
            }
        
        except Exception as e:
            logger.error(f"Error deleting workflow: {e}")
            return {
                'success': False,
                'error': f"Failed to delete workflow: {str(e)}",
                'requestId': data.get('requestId')
            }