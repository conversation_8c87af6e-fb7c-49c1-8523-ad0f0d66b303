
from datetime import datetime
import logging
from pathlib import Path
import sys
import traceback
from functools import wraps
import time
import json
from typing import Dict, Any, TypedDict, List, Union

# Flask imports

# Local imports
from managers.AssistantManager import AssistantManager
from elastic.elastic import Elastic
from utils.logger import logger
from routes.socket.socket_server import SocketServer
from type_definitions.assistant import AssistantDocument
from src.clients.qdrant import MyQdrantClient



class GetAssistantsResponse(TypedDict):
    success: bool
    data: List[AssistantDocument]
    error: Union[str, None]


def init_assistant_routes(es: Elastic, socket_server: SocketServer, qdrant: MyQdrantClient) -> None:
    """Initialize assistant-related socket routes"""
    
    # Add debug log to verify initialization
    logger.info("🚀 [SOCKET] Initializing assistant routes")
    
    @socket_server.handle_event('getAssistants')
    async def handle_get_assistants(sid: str, data: Dict[str, Any]):
        """<PERSON>les request to get all assistants."""
        logger.info("====== Get Assistants Request Started ======")
        
        try:
            username = data.get('username')
            request_id = data.get('requestId')
            assi_manager = AssistantManager(es=es,qdrant=qdrant)
            
            assistants = assi_manager.get_assistants()
            response = {
                'success': True,
                'data': assistants,
                'requestId': request_id
            }
      
            return response
        except Exception as e:
            error_msg = f"Error in handle_get_assistants: {str(e)}"
            logger.error(f"❌ [SOCKET] {error_msg}")
            logger.error(f"[SOCKET] Traceback: {traceback.format_exc()}")
            response = {
                'success': False,
                'error': error_msg,
                'requestId': request_id
            }
            return response


    @socket_server.handle_event('getAssistant')
    async def handle_get_assistant(sid: str, data: Dict[str, Any]):
        """Handles request to get a single assistant by ID."""
        logger.info(f"====== Get Assistant Request Started ====== {data}", )
        
        try:
            username = data.get('username')
            request_id = data.get('requestId')
            assistant_id = data.get('assistantId')
            assi_manager = AssistantManager(es,qdrant=qdrant)
            
            assistant = assi_manager.get_assistant(assistant_id)
            response = {
                'success': True,
                'data': assistant,
                'requestId': request_id
            }
    
            return response
        except Exception as e:
            error_msg = f"Error in handle_get_assistants: {str(e)}"
            logger.error(f"❌ [SOCKET] {error_msg}")
            logger.error(f"[SOCKET] Traceback: {traceback.format_exc()}")
            response = {
                'success': False,
                'error': error_msg,
                'requestId': request_id
            }
            return response

    @socket_server.handle_event('createAssistant')
    async def handle_create_assistant(sid: str, data: Dict[str, Any]):
        """Handles request to create a new assistant."""
        try:
            request_id = data.get('requestId')
            # If createdBy is not provided, use JavaScript-style ISO timestamp
            # This will create format like: "2025-04-23T15:54:03.502Z"
            created_by = data.get('createdBy') or datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'

            assistant_data = {
                'id': data['id'].lower(),
                'display_name': data['display_name'],
                'username': data['display_name'],
                'type': 'assistant',
                'language': data.get('language', 'de'),
                'model': data.get('model', 'gpt-3.5-turbo'),
                'updated_at': datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            }
            
            logger.info(f"[CREATE_ASSISTANT] Formatted assistant data: {assistant_data}")
            
            # Store in Qdrant
            from qdrant.operations.assistants import get_assistant_manager
            assistant_manager = get_assistant_manager()

            # Store the assistant data
            success = await assistant_manager.store_assistant(assistant_data)
            if not success:
                logger.error("[CREATE_ASSISTANT] Failed to store assistant data")
                return {
                    'success': False,
                    'error': 'Failed to create assistant',
                    'requestId': request_id
                }

            # Store system message separately using Qdrant
            from qdrant.operations.assistants import get_assistant_manager
            assistant_manager = get_assistant_manager()

            success = await assistant_manager.set_system_message(
                assistant_id=assistant_data['id'],
                system_message=data['system_message'],
                username=created_by
            )

            if not success:
                logger.error("[CREATE_ASSISTANT] Failed to store system message")
                return {
                    'success': False,
                    'error': 'Failed to store system message for new assistant',
                    'requestId': request_id
                }

            logger.info(f"[CREATE_ASSISTANT] Successfully created assistant {assistant_data}")
            return {
                'success': True,
                'data': {'assistant': assistant_data}, # Standardize response via SocketServer.emit
                'requestId': request_id
            }
            
        except Exception as e:
            error_msg = f"Error creating assistant: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False, 
                'error': error_msg,
                'requestId': data.get('requestId') # Ensure requestId is included in error response
            }

    @socket_server.handle_event('getAssistantSystemMessage')
    async def handle_get_system_message(sid: str, data: Dict[str, Any]):
        """Handles request to get the system message for an assistant."""
        try:
            logger.info("====== Get Assistant System Message Request Started ======")
            assistant_id = data.get('assistantId')
            logger.info(f"Getting system message for assistant_id: {assistant_id}")
            
            if not assistant_id:
                logger.error("Missing assistantId in request")
                return {'success': False, 'error': 'Missing assistantId', 'requestId': data.get('requestId')}
            
            assimanager = AssistantManager(es,qdrant=qdrant)
            
            # Debug: Check index and documents
            system_message = assimanager.get_system_message(assistant_id)
            logger.info(f"Retrieved system message: {system_message}")
            
            if system_message is None:
                logger.warning(f"No system message found for {assistant_id}, using default")
                system_message = (
                    "I am an AI assistant. I aim to be direct, honest, and helpful while "
                    "prioritizing safety and ethics."
                )

            logger.info(f"Sending system message for {assistant_id}: {system_message}")
            return {
                'success': True,
                'data': {'system_message': system_message},
                'requestId': data.get('requestId')
            }            
            
        except Exception as e:
            error_msg = f"Error getting system message: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return {
                'success': False, 
                'error': error_msg,
                'requestId': data.get('requestId')
            }

    @socket_server.handle_event('updateAssistant')
    async def handle_update_assistant(sid: str, data: Dict[str, Any]):
        """Handles request to update an existing assistant."""
        try:
            request_id = data.get('requestId')
            assistant_id = data.get('assistantId')
            username = data.get('username')
            updates = data.get('updates')
            logging.info(f"received : (updateAssistant): {data}")
            assi_manager = AssistantManager(es,qdrant=qdrant)
            if(updates and updates.get('system_message')):
                logging.info(f"Setting system message : {updates}")
                # system_message = updates.get('system_message')
                assi_manager.set_system_message(assistant_id, updates.get('system_message'), username)
                del updates['system_message']
                
            logger.info(f"[UPDATE_ASSISTANT] Updating assistant {assistant_id} with data: {updates}")
            
            
            # Get current assistant data
            success = assi_manager.update_assistant(username, assistant_id, updates)
            assistant = assi_manager.get_assistant(assistant_id)
            return {
                'success': success,
                'data': {'assistant': assistant},
                'requestId': request_id
            }
            
        except Exception as e:
            logger.error(f"[UPDATE_ASSISTANT] Error updating assistant: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e), 
                # 'message': 'An error occurred while updating the assistant', # Redundant with error field
                'requestId': request_id
            }
