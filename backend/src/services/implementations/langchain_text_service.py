from typing import Any, Dict, List, Optional, Tuple, Union # Added Union, <PERSON>ple
import logging
import anyio
import functools
from datetime import datetime
from agent.combine_conversation_history import _combine_conversation_history
from agent.context_management import summarize_long_chat_history
from agent.get_relevant_langchain_tools import _get_relevant_langchain_tools
from agent.trace_logging import append_trace_event, save_trace_events_to_file
from agent.summarize_node import summarize_node
from agent.post_process import postprocess_node # Assuming this takes state
from agent.preprocess_node import preprocess_node
#from agent.exec_mcp_tool_adapter import _execute_mcp_tool_adapter
from agent.convert_mcp_to_langchain import _convert_mcp_tool_to_langchain_tool
# from agent.llm_node import llm_node # No longer directly used, replaced by internal method
from agent.get_agent_tools import get_agent_tools
from agent.code_agent_tools import get_code_agent_tools, get_enhanced_qdrant_tools
from agent.code_agent_preprocessing import enhance_agent_state_for_code, detect_code_intent
from agent.combine_conversation_history import _combine_conversation_history
from agent.store_conversation import store_conversation
from interfaces import Assistant
from langchain_core.runnables import RunnableLambda # Keep for other nodes if needed

# For LangGraph Agent State
from typing import TypedDict
from langchain_core.agents import AgentAction, AgentFinish
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage # For agent_scratchpad

from managers.ServicesManager import BaseService
from managers.FunctionsManager import FunctionsManager
from clients.neoj import MyNeo4jClient
from clients.neo4j_graph_analytics import Neo4jGraphAnalytics
from clients.mcpClient import MCPClient
from clients.types import Tool as MCPClientTool # Alias to avoid confusion
from servers.mcpservers.neoj import _store_message_in_neo4j, _get_entity_relationships, _generate_graph_based_prompt, _generate_graph_based_summary
from utils.openai_setup import create_client # Assuming create_client is here or accessible
import os
import json
from datetime import datetime
from langchain.agents import Tool as LangchainAgentTool
from langchain_core.tools import StructuredTool # Import StructuredTool
from langchain_community.chat_models import ChatOpenAI # Updated import
from langchain.agents import AgentExecutor, create_openai_functions_agent # Updated imports

from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langgraph.graph import StateGraph, END
from managers.AssistantManager import AssistantManager

from clients.qdrant import MyQdrantClient
from elastic.elastic import Elastic # Import the Elastic client
from clients.types import LLMClient # Import LLMClient
from clients.neoj import MyNeo4jClient

# Use Pydantic v1 for Langchain Tool compatibility if Pydantic v2 is the primary environment version. # Good practice!
# This addresses issues where Langchain tools might expect v1 BaseModel for args_schema.
from pydantic.v1 import create_model, Field
# from pydantic import create_model, Field # Original import, potentially v2

from langchain_core.embeddings import Embeddings # For custom embedding wrapper
from langchain_community.vectorstores import Qdrant as LangchainQdrant # Specific import
# Constants for Qdrant, assuming they are defined similarly to AICodeAnalyserService or MyQdrantClient handles them
from langchain_core.tools import ToolException # For better error reporting from tools
# For simplicity, let's assume MyQdrantClient can be initialized with minimal args if env vars are set
# Or, you might need to import QDRANT_HOST, QDRANT_PORT from a central config or define them here.
QDRANT_HOST = os.getenv("QDRANT_HOST", "localhost")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", 6333))

# Define the AgentState TypedDict for LangGraph
class AgentState(TypedDict):
    input_text: str
    chat_history: List[BaseMessage] # Changed to BaseMessage for LangChain compatibility
    username: Optional[str]
    conversation_id: Optional[str]
    assistant_id: Optional[str]
    llm_handler: LLMClient # Type hint for clarity
    qdrant_client: Optional[MyQdrantClient]
    neo4j_client: Optional[MyNeo4jClient]
    assistant_manager: Optional[AssistantManager]
    functiion_manager: Optional[FunctionsManager] # Typo in original, keeping for now
    model_name: str
    api_key: str
    tools_for_this_invocation: List[LangchainAgentTool]
    agent_outcome: Optional[Union[AgentAction, AgentFinish]] # The output of the agent LLM
    intermediate_steps: List[Tuple[AgentAction, str]] # History of tool calls and their results (AgentAction, Observation string)
    plan: Optional[str] # The generated plan to follow
    output: Optional[str] # Final response text
    service_instance: Any # Reference to the LangChainService instance itself

logger = logging.getLogger(__name__)


class LangChainService(BaseService):
    # Class-level default attributes
    DEFAULT_SERVICE_ID = 'langchain_code_agent'
    DEFAULT_NAME = 'LangChain Code Agent'
    DEFAULT_DESCRIPTION = 'Advanced code agent with file operations, code analysis, and development assistance'
    DEFAULT_CATEGORY = 'Code'
    DEFAULT_ICON = '🤖'
    DEFAULT_HISTORY_LIMIT = 1  # Minimal for GPT-3.5-turbo - only current message
    DEFAULT_INITIAL_CONFIG = {
        'history_limit': DEFAULT_HISTORY_LIMIT,
        'enable_code_execution': True,
        'enable_file_operations': True,
        'enable_git_operations': True
    }

    def __init__(self,
                 functions_manager: FunctionsManager,
                 service_id: Optional[str] = None,
                 name: Optional[str] = None,
                 description: Optional[str] = None,
                 category: Optional[str] = None,
                 icon: Optional[str] = None,
                 initial_config: Optional[Dict[str, Any]] = None,
                 **kwargs):
        # Assign default values if parameters are not provided
        service_id = service_id or self.DEFAULT_SERVICE_ID
        name = name or self.DEFAULT_NAME
        description = description or self.DEFAULT_DESCRIPTION
        category = category or self.DEFAULT_CATEGORY
        icon = icon or self.DEFAULT_ICON
        initial_config = initial_config or self.DEFAULT_INITIAL_CONFIG

        # Backward compatibility: Support old service_id 'langchain_service'
        if service_id == 'langchain_service':
            service_id = self.DEFAULT_SERVICE_ID
            logger.info(f"LangChainService: Backward compatibility - mapping 'langchain_service' to '{self.DEFAULT_SERVICE_ID}'")

        # Initialize the base class with the provided or default values
        super().__init__(
            service_id=service_id,
            name=name,
            description=description,
            category=category,
            icon=icon,
            initial_config=initial_config,
            **kwargs
        )
        
        self._model = "gpt-3.5-turbo" # Default model
        # Allow model override from initial_config if provided
        self._history_limit = self.DEFAULT_HISTORY_LIMIT
        # Use the 'initial_config' parameter directly
        if initial_config and isinstance(initial_config, dict):
            self._model = initial_config.get('model', self._model)
            self._history_limit = initial_config.get('history_limit', self._history_limit)

        # self.client = create_client() # Replaced by LLMClient instance
        self._api_key = os.environ.get("OPENAI_API_KEY", "")
        self.llm_handler = LLMClient(model=self._model, temperature=0.7, max_tokens=4096) # Use LLMClient, increased max_tokens

        
        # Store injected dependencies
        self._functions_manager: FunctionsManager = functions_manager
        
        logger.info(f"LangChainCodeAgent initialized with ID: {self.id}, Model: {self._model}")
        # logger.info(f"kwargs: {kwargs}, args: {args}") # Potentially verbose
        
        # Initialize available_functions as an empty list. Populate it in initialize().
        self.available_functions: List[Dict[str, Any]] = [] 

        # Initialize client attributes to None; they will be set in initialize()
        self.my_qdrant_client: Optional[MyQdrantClient] = None
        self.neo4j_client: Optional[MyNeo4jClient] = None
        self.assistant_manager: Optional[AssistantManager] = None
        self.neo4j_graph_analytics: Optional[Neo4jGraphAnalytics] = None
        self.es_client: Optional[Elastic] = None # Add Elastic client instance
        self.tools: List[LangchainAgentTool] = [] # Langchain specific tools
        self._all_mcp_tool_definitions: List[MCPClientTool] = [] # To store all fetched MCP tool definitions
        self._dependencies_initialized_flag = False # Flag for client initializations and tool def fetching
        self.trace_events: List[Dict[str, Any]] = [] # Initialize trace events list
        self._convert_mcp_tool_to_langchain_tool = _convert_mcp_tool_to_langchain_tool
        self._get_entity_relationships = _get_entity_relationships
        self._generate_graph_based_prompt  = _generate_graph_based_prompt
        self._generate_graph_based_summary = _generate_graph_based_summary
        # self._execute_mcp_tool_adapter = _execute_mcp_tool_adapter
        self.get_agent_tools = get_agent_tools
        self._combine_conversation_history = _combine_conversation_history
    async def initialize(self) -> bool:
        """Initialize the service."""
        if not self._api_key:
            logger.warning("OpenAI API key not found in environment variables. Some functionalities might be limited.")
      
        logger.info(f"LangChainService.initialize (id={id(self)}): Starting initialization.")
        if self._dependencies_initialized_flag:
            logger.info(f"LangChainService.initialize (id={id(self)}): Dependencies (clients, MCP tool defs) already initialized. Skipping re-initialization.")
            return True
          # Initialize MyQdrantClient
        self.my_qdrant_client = None # Initialize as None
        try:
            self.my_qdrant_client = MyQdrantClient()
            logger.info(f"LangChainService (id={id(self)}): MyQdrantClient initialized for host: {QDRANT_HOST}:{QDRANT_PORT}")
        except Exception as e:
            logger.error(f"LangChainService (id={id(self)}): Failed to initialize MyQdrantClient: {e}", exc_info=True)
            self.my_qdrant_client = None # Ensure it's None if initialization fails

        self.neo4j_client = None 
        try: 
            self.neo4j_client = MyNeo4jClient()
            # Initialize Neo4jGraphAnalytics with the Neo4j client
            if self.neo4j_client:
                self.neo4j_graph_analytics = Neo4jGraphAnalytics(self.neo4j_client)
                logger.info(f"LangChainService (id={id(self)}): Neo4jGraphAnalytics initialized with Neo4j client")
            else:
                self.neo4j_graph_analytics = None
        except Exception as e:
            logger.error(f"LangChainService (id={id(self)}): Failed to initialize MyNeo4jClient: {e}", exc_info=True)
            self.neo4j_client = None # Ensure it's None if initialization fails
            self.neo4j_graph_analytics = None # Ensure it's None if Neo4j client initialization fails


        # Initialize Elastic client
        try:
            self.es_client = Elastic()
            logger.info(f"LangChainService (id={id(self)}): Elastic client initialized.")
        except Exception as e:
            logger.error(f"LangChainService (id={id(self)}): Failed to initialize Elastic client: {e}", exc_info=True)
            self.es_client = None

        # Ensure FunctionsManager is initialized (redundant if main.py does it, but safe)
        if self._functions_manager and not self._functions_manager.initialized:
             logger.warning("FunctionsManager not initialized. Attempting to initialize it now.")
             await self._functions_manager.initialize()

        # MCP servers are initialized by FunctionsManager's internal MCPClient.
        if self._functions_manager and self._functions_manager.mcp_client:
            mcp_cli = self._functions_manager.mcp_client
            if not all(s.session for s in mcp_cli.servers if hasattr(s, 'session')):
                logger.info(f"LangChainService (id={id(self)}): FunctionsManager's MCP servers not fully initialized. Attempting to initialize them now via mcp_cli.chat_session.initialize_servers().")
                await mcp_cli.chat_session.initialize_servers()
            
            # ADDED LOGGING HERE:
            logger.info(f"LangChainService (id={id(self)}): Status of MCPClient servers before listing tools:")
            for s_idx, s_server in enumerate(mcp_cli.servers):
                session_status = "ACTIVE" if s_server.session else "INACTIVE (no session)"
                logger.info(f"  - Server {s_idx} ('{s_server.name}' id={id(s_server)}): Session {session_status}")
            # END OF ADDED LOGGING
        # Get the list of available tools from initialized servers and format for OpenAI
        
        # Fetch all MCPClientTool definitions ONCE and store them
        self._all_mcp_tool_definitions = []
        if self._functions_manager and self._functions_manager.mcp_client:
            try:
                self._all_mcp_tool_definitions = await self._functions_manager.mcp_client.list_tools_from_all_servers()
                logger.info(f"LangChainService (id={id(self)}): Fetched and stored {len(self._all_mcp_tool_definitions)} MCPClientTool definitions.")
                
                # Populate self.available_functions (OpenAI schemas for all tools, if needed elsewhere)
                self.available_functions = [tool.format_for_llm() for tool in self._all_mcp_tool_definitions]
                logger.info(f"LangChainService (id={id(self)}): Fetched and stored {len(self._all_mcp_tool_definitions)} MCPClientTool definitions.")
            except Exception as e:
                logger.error(f"LangChainService (id={id(self)}): Error loading tools from FunctionsManager's MCPClient: {e}", exc_info=True)
                self._all_mcp_tool_definitions = [] # Ensure empty on error
                self.available_functions = []
        else:
            logger.warning(f"LangChainService (id={id(self)}): FunctionsManager or its MCPClient not available. Cannot load functions for LLM.")
            self._all_mcp_tool_definitions = [] # Ensure empty
            self.available_functions = []
        # Ensure Qdrant collection for conversations exists if MyQdrantClient doesn't do it.
        # This might be handled within MyQdrantClient or a separate setup script.
        if self.my_qdrant_client and hasattr(self.my_qdrant_client, 'ensure_collection_exists'):
            pass

        # LangchainAgentTool list (self.tools) is not populated here anymore.
        # It will be populated dynamically in _get_relevant_langchain_tools().
        
        self._dependencies_initialized_flag = True # Set the flag after successful initialization
        logger.info(f"LangChainService.initialize (id={id(self)}): Dependencies initialized.")
        return True


    async def _execute_mcp_tool_adapter(self, *args_from_langchain, tool_name_to_exec: str, **kwargs_from_langchain) -> str:
        logger.info(f"Adapter called for MCP Tool: {tool_name_to_exec}. Langchain raw_pos_args: {args_from_langchain}, raw_kw_args: {kwargs_from_langchain}")
        try:
            if not (self._functions_manager and self._functions_manager.mcp_client):
                logger.error(f"MCPClient not available for executing tool {tool_name_to_exec}.")
                raise ToolException(f"MCPClient not available to execute tool {tool_name_to_exec}.")

            actual_args_for_mcp = {}
            original_mcp_tool = next(
                (tool for tool in self._all_mcp_tool_definitions if tool.name == tool_name_to_exec), None
            )

            if not original_mcp_tool:
                logger.error(f"Definition for MCP tool '{tool_name_to_exec}' not found in _all_mcp_tool_definitions.")
                raise ToolException(f"Definition for MCP tool '{tool_name_to_exec}' not found.")

            # --- TRACE LOGGING: function call ---
            append_trace_event(self.trace_events, {
                "role": "assistant",
                "event_type": "function_call",
                "function_name": tool_name_to_exec,
                "function_args": kwargs_from_langchain or args_from_langchain,
                "timestamp": datetime.utcnow().isoformat()
            })
            # --- END TRACE LOGGING ---

            tool_expects_args = bool(original_mcp_tool.input_schema and original_mcp_tool.input_schema.get("properties"))

            if tool_expects_args:
                if kwargs_from_langchain: # Primarily for StructuredTool
                    actual_args_for_mcp = kwargs_from_langchain
                    if args_from_langchain:
                        logger.warning(f"Tool '{tool_name_to_exec}' received both positional args {args_from_langchain} and keyword args {kwargs_from_langchain}. Using keyword args.")
                elif args_from_langchain: # Potentially LangchainAgentTool with single arg
                    if len(args_from_langchain) == 1 and len(original_mcp_tool.input_schema["properties"]) == 1:
                        first_prop_name = list(original_mcp_tool.input_schema["properties"].keys())[0]
                        actual_args_for_mcp = {first_prop_name: args_from_langchain[0]}
                        logger.info(f"Mapped single positional arg from Langchain to '{first_prop_name}' for tool '{tool_name_to_exec}'.")
                    else:
                        logger.warning(f"Tool '{tool_name_to_exec}' expects keyword arguments based on schema, but received only positional arguments from Langchain: {args_from_langchain}. Passing empty args to MCP tool.")
                        actual_args_for_mcp = {}
                elif args_from_langchain: # Potentially LangchainAgentTool with multiple args
                    logger.warning(f"Tool '{tool_name_to_exec}' expects keyword arguments based on schema, but received positional arguments from Langchain: {args_from_langchain}. Attempting to map args by position.")
                    for idx, arg in enumerate(args_from_langchain):
                        if idx < len(original_mcp_tool.input_schema["properties"]):
                            prop_name = list(original_mcp_tool.input_schema["properties"].keys())[idx]
                            actual_args_for_mcp[prop_name] = arg
                            logger.info(f"Mapped positional arg from Langchain to '{prop_name}' for tool '{tool_name_to_exec}'.")
                        else:
                            logger.warning(f"Received extra positional argument for tool '{tool_name_to_exec}' that exceeds defined schema properties. Ignoring extra argument.")
                else: # Tool expects args, but none provided by Langchain
                    logger.info(f"Tool '{tool_name_to_exec}' expects arguments, but none were provided by Langchain. Passing empty args to MCP tool.")
                    actual_args_for_mcp = {}
            else: # Tool expects no arguments
                if args_from_langchain or kwargs_from_langchain:
                    logger.warning(f"Tool '{tool_name_to_exec}' expects NO arguments based on schema, but received from Langchain: pos={args_from_langchain}, kw={kwargs_from_langchain}. Ignoring them.")
                actual_args_for_mcp = {}

            # --- TRACE LOGGING: function call ---
            # --- END TRACE LOGGING ---

            logger.info(f"Executing MCP Tool: {tool_name_to_exec} with determined args for MCP: {actual_args_for_mcp}")
            result = await self._functions_manager.mcp_client.exec(tool_name=tool_name_to_exec, args=actual_args_for_mcp)

            # --- TRACE LOGGING: function result ---
            append_trace_event(self.trace_events, {
                "role": "tool",
                "event_type": "function_result",
                "function_name": tool_name_to_exec,
                "function_result": json.dumps(result, default=str) if isinstance(result, (dict, list)) else str(result),
                "timestamp": datetime.utcnow().isoformat()
            })
            # --- END TRACE LOGGING ---

            if isinstance(result, dict) and "error" in result:
                logger.error(f"MCP tool {tool_name_to_exec} execution returned an error object: {result['error']}")
                raise ToolException(f"Error executing MCP tool {tool_name_to_exec}: {result['error']}")
            elif isinstance(result, (dict, list)):
                # Check if this is the result from 'execute_code' and if it contains an image
                if tool_name_to_exec == "execute_code" and isinstance(result, list):
                    # The 'result' from pythonExecServer is a list of dicts (TextContent, ImageContent)
                    image_data_found = False
                    text_parts = []
                    for item in result:
                        if isinstance(item, dict):
                            if item.get("type") == "image" and item.get("data"):
                                image_data_found = True
                            if item.get("type") == "text" and item.get("text"):
                                text_parts.append(item["text"])
                    
                    if image_data_found:
                        # If an image was generated, return a placeholder message instead of the full base64 data
                        # The frontend will render the image from the initial LLM message containing the markdown.
                        text_output_for_agent = " ".join(text_parts) if text_parts else "Plot generated successfully and displayed to the user."
                        logger.info(f"Tool '{tool_name_to_exec}' generated an image. Returning placeholder to agent: '{text_output_for_agent}'")
                        return text_output_for_agent
                # For other tools or if execute_code didn't produce an image, dump the full result
                return json.dumps(result)
            return str(result)
        except Exception as e:
            logger.error(f"Exception during MCP tool {tool_name_to_exec} execution in adapter: {e}", exc_info=True)
            if isinstance(e, ToolException):
                raise
            raise ToolException(f"Exception during MCP tool {tool_name_to_exec} execution: {str(e)}")

    def _prepare_initial_data(self, input_data: Any) -> Tuple[bool, Dict[str, Any]]:
        """Parses input, prepares messages, and retrieves MCP client, username, conversation_id, and current_user_message."""
        raw_messages: Optional[List[Dict[str, Any]]] = None
        user_prompt_text: Optional[str] = None
        
        if isinstance(input_data, dict) and 'messages' in input_data:
            raw_messages = input_data['messages']
        elif isinstance(input_data, dict) and 'input' in input_data and isinstance(input_data['input'], dict) and 'messages' in input_data['input']:
            raw_messages = input_data['input']['messages']
        elif isinstance(input_data, dict) and 'user_message' in input_data:
            user_prompt_text = input_data['user_message']
        elif isinstance(input_data, dict) and 'text' in input_data:
            user_prompt_text = input_data['text']
        
        cleaned_messages: List[Dict[str, str]] = []
        if isinstance(raw_messages, list):
            for msg in raw_messages:
                if isinstance(msg, dict) and "role" in msg and "content" in msg:
                    cleaned_messages.append({"role": str(msg["role"]), "content": str(msg["content"])})
        elif user_prompt_text:
            cleaned_messages = [
                {'role': 'system', 'content': 'You are a helpful assistant.'},
                {'role': 'user', 'content': user_prompt_text}
            ]

        # Get MCPClient from FunctionsManager
        mcp_client: Optional[MCPClient] = self._functions_manager.mcp_client if self._functions_manager else None
        if not mcp_client and self.available_functions:
            logger.warning("AITextService: MCPClient not available, but functions are declared. Tool calls will fail.")
        
        # Attempt to get username and conversation_id
        username: Optional[str] = None
        conversation_id: Optional[str] = None
        assistant_id: Optional[str] = None # To store assistant_id

        # Prioritize 'input' field if it exists and is a dict
        input_field_dict = input_data.get("input") if isinstance(input_data, dict) else None
        if isinstance(input_field_dict, dict):
            username = input_field_dict.get("username")
            conversation_id = input_field_dict.get("conversation_id")
            assistant_id = input_field_dict.get("assistant_id")

        # Fallback to top-level input_data if not found in 'input' field
        if isinstance(input_data, dict):
            if username is None:
                username = input_data.get("username")
            if conversation_id is None:
                conversation_id = input_data.get("conversation_id")
            if assistant_id is None: # assistant_id might also be top-level
                assistant_id = input_data.get("assistant_id")
        
        current_user_message_content = ""
        if cleaned_messages and cleaned_messages[-1]["role"] == "user":
            current_user_message_content = cleaned_messages[-1]["content"]
        elif user_prompt_text: # Fallback if cleaned_messages was just system + user_prompt_text
            current_user_message_content = user_prompt_text
        
        logger.debug(f"_prepare_initial_data: username='{username}', conversation_id='{conversation_id}', assistant_id='{assistant_id}'")
        return True, {
            "cleaned_messages": cleaned_messages,
            "mcp_client": mcp_client,
            "username": username,
            "conversation_id": conversation_id,
            "current_user_message": current_user_message_content,
            "assistant_id": assistant_id # Include assistant_id
        }
    
    # The summarize_wrapper is not directly used in the new graph, as summarize_node is called directly.
    # Assuming summarize_node takes state and extracts what it needs (like service_instance).
    # async def summarize_wrapper(self,state, llm_handler, qdrant_client, neo4j, message_class):
    #     return await summarize_node(state,llm_handler,qdrant_client,neo4j, message_class )

    async def postprocess_wrapper(self, state):
        return await postprocess_node(state)

    async def _planner_node(self, state: AgentState) -> AgentState:
        """
        Generates a step-by-step plan for the agent to follow, with enhanced code-specific planning.
        """
        logger.info("---CODE AGENT PLANNER NODE: Generating execution plan---")

        planner_prompt_template = """You are an expert code agent planner. Your job is to create a step-by-step plan to answer the user's coding request based on the conversation history and available tools.

User Request: {input}

Conversation History:
{chat_history}

Available Tools:
{tool_names}

As a code agent, consider these planning strategies:
1. For code analysis: First search for relevant code, then analyze patterns and structure
2. For debugging: Identify the problem, search for related code, analyze logs/errors, propose fixes
3. For new features: Understand requirements, search existing code for patterns, plan implementation steps
4. For refactoring: Analyze current code, identify improvement areas, plan incremental changes
5. For file operations: Always check if files exist before modifying, backup important changes

Create a clear, chronological, and logical plan with numbered steps. Be specific about which tools to use.
If no complex plan is needed, respond with "No plan needed.".

Your Plan:
"""
        prompt = ChatPromptTemplate.from_template(planner_prompt_template)

        # Format chat history for the prompt
        history_str = "\n".join([f"{msg.type}: {msg.content}" for msg in state["chat_history"]])
        
        # Format tool names
        tool_names = [tool.name for tool in state["tools_for_this_invocation"]]

        try:
            # Use a separate, low-temperature LLM for planning to get deterministic results
            planner_llm = await anyio.to_thread.run_sync(
                functools.partial(ChatOpenAI, model=state["model_name"], temperature=0, api_key=state["api_key"])
            )
        except Exception as e:
            logger.error(f"Could not instantiate planner LLM: {e}. Using main LLM handler.")
            planner_llm = state["llm_handler"].get_langchain_chat_model()

        planner_runnable = prompt | planner_llm
        
        try:
            plan_result = await planner_runnable.ainvoke({
                "input": state["input_text"],
                "chat_history": history_str,
                "tool_names": ", ".join(tool_names) if tool_names else "No tools available."
            })
            
            # --- TRACE LOGGING: planner call ---
            append_trace_event(self.trace_events, {
                "role": "assistant",
                "event_type": "planner_call",
                "planner_input": {"input": state["input_text"], "chat_history": history_str, "tool_names": tool_names},
                "planner_output": plan_result.content,
                "timestamp": datetime.utcnow().isoformat()
            })
            # --- END TRACE LOGGING ---

            plan_text = plan_result.content
            if "no plan needed" in plan_text.lower():
                logger.info("Planner determined no specific plan is needed.")
                state["plan"] = None
            else:
                logger.info(f"Generated Plan:\n{plan_text}")
                # Escape curly braces to prevent LangChain from interpreting them as f-string variables
                state["plan"] = plan_text.replace("{", "{{").replace("}", "}}")
        except Exception as e:
            logger.error(f"Error during plan generation: {e}", exc_info=True)
            state["plan"] = "Error generating plan. Proceeding without one."

        return state

    # Renamed and refactored llm_wrapper to be the agent's LLM node method
    async def _agent_llm_node(self, state: AgentState) -> AgentState:
        llm_handler = state["llm_handler"]
        tools = state["tools_for_this_invocation"]
        chat_history = state["chat_history"]
        input_text = state["input_text"]
        intermediate_steps = state.get("intermediate_steps", [])

        # Dynamically create the system message for code agent, including the plan if it exists
        system_message = """You are a code agent. Use tools to analyze code, search files, and provide solutions. Be concise."""

        if state.get("plan"):
            system_message += f"\n\nTo achieve the user's goal, you must follow this plan:\n{state['plan']}\n\nExecute the steps in order and only finish when the plan is complete."

        # LangChain agent prompt setup
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_message),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad"),
            ]
        )

        # Convert intermediate steps for agent_scratchpad
        # AgentExecutor expects a specific format for intermediate_steps
        # (AgentAction, Observation) tuples. We convert them to messages for the LLM context.
        agent_scratchpad_messages = []
        for action, observation in intermediate_steps:
            # Represent tool calls and their outputs as AI and Human messages for the LLM context
            agent_scratchpad_messages.append(AIMessage(content=f"Tool Call: {action.tool} with args {action.tool_input}"))
            agent_scratchpad_messages.append(HumanMessage(content=f"Tool Output: {observation}"))

        # Get LangChain compatible LLM from LLMClient
        # Assuming LLMClient has a method to return a LangChain BaseChatModel
        try:
            langchain_llm = llm_handler.get_langchain_chat_model()
        except AttributeError:
            # Fallback: if LLMClient doesn't have get_langchain_chat_model,
            # directly instantiate ChatOpenAI. This assumes LLMClient wraps OpenAI.
            logger.warning("LLMClient does not have 'get_langchain_chat_model()'. Falling back to direct ChatOpenAI instantiation.")
            langchain_llm = await anyio.to_thread.run_sync(
                functools.partial(ChatOpenAI, model=state["model_name"], temperature=llm_handler.temperature, api_key=state["api_key"], max_tokens=llm_handler.max_tokens)
            )

        # Create the agent runnable
        agent_runnable = create_openai_functions_agent(langchain_llm, tools, prompt)

        # Prepare input for the agent runnable
        agent_input = {
            "input": input_text,
            "chat_history": chat_history,
            "intermediate_steps": intermediate_steps, # Pass the raw list of (AgentAction, Observation) tuples
        }

        # Invoke the agent to get its next action/finish
        try:
            # --- TRACE LOGGING: agent llm call ---
            append_trace_event(self.trace_events, {
                "role": "assistant",
                "event_type": "agent_llm_call",
                "llm_input": agent_input,
                "timestamp": datetime.utcnow().isoformat()
            })

            agent_outcome = await agent_runnable.ainvoke(agent_input)

            # --- TRACE LOGGING: agent llm response ---
            append_trace_event(self.trace_events, {
                "role": "assistant",
                "event_type": "agent_llm_response",
                "llm_output": agent_outcome.log, # The log contains the raw response
                "timestamp": datetime.utcnow().isoformat()
            })
        except Exception as e:
            logger.error(f"Error invoking agent runnable in _agent_llm_node: {e}", exc_info=True)
            # If agent fails, return an error message as final output
            state["output"] = f"An error occurred while the AI was thinking: {str(e)}"
            state["agent_outcome"] = AgentFinish(return_values={"output": state["output"]}, log=f"Error: {str(e)}")
            return state

        # Update state with the agent's decision
        state["agent_outcome"] = agent_outcome

        # If it's an AgentFinish, the output is ready
        if isinstance(agent_outcome, AgentFinish):
            state["output"] = agent_outcome.return_values["output"]
            logger.info(f"Agent decided to finish. Final output: {state['output'][:100]}...")
        else:
            logger.info(f"Agent decided to call tool: {agent_outcome.tool} with args: {agent_outcome.tool_input}")

        return state

    async def _tool_executor_node(self, state: AgentState) -> AgentState:
        agent_outcome: AgentAction = state["agent_outcome"]
        tools = state["tools_for_this_invocation"]
        intermediate_steps: List[Tuple[AgentAction, str]] = state.get("intermediate_steps", [])
        service_instance = state["service_instance"] # Reference to self

        tool_name = agent_outcome.tool
        tool_input = agent_outcome.tool_input

        # Find the tool by name
        tool_to_execute = next((t for t in tools if t.name == tool_name), None)

        observation = ""
        if not tool_to_execute:
            observation = f"Error: Tool '{tool_name}' not found."
            logger.error(observation)
        else:
            try:
                # The Langchain agent provides the arguments as a dictionary in `tool_input`.
                # Both `Tool` and `StructuredTool` are designed to be called via `arun(tool_input)`.
                # `StructuredTool`'s internal `_arun` method is smart enough to unpack the
                # dictionary from the positional `tool_input` argument into keyword arguments
                # for the underlying function.
                logger.info(f"Executing tool '{tool_name}' with input: {tool_input}")
                observation = await tool_to_execute.arun(tool_input)

                # Handle the case where observation might be a coroutine or other object
                if hasattr(observation, '__await__'):
                    observation = await observation

                # Convert observation to string for logging
                observation_str = str(observation) if observation is not None else "None"
                logger.info(f"Tool '{tool_name}' executed. Observation: {observation_str[:200]}...")
            except Exception as e:
                observation = f"Error executing tool '{tool_name}': {str(e)}"
                logger.error(observation, exc_info=True)

        # Append the action and observation to intermediate steps
        intermediate_steps.append((agent_outcome, observation))
        state["intermediate_steps"] = intermediate_steps
        # state["tool_output"] = observation # Not strictly needed, as it's in intermediate_steps

        return state

    async def execute(self, input_data: Any) -> Dict[str, Any]:
        logger.info(f"Executing {self.name} (id={id(self)}) with input: {input_data}")

        # 1. initData
        success_init, prep_data = self._prepare_initial_data(input_data)
        if not success_init:
            return prep_data # This is already a formatted error response

        mcp_client: Optional[MCPClient] = prep_data["mcp_client"]
        username: Optional[str] = prep_data["username"]
        conversation_id: Optional[str] = prep_data["conversation_id"]
        current_user_message: str = prep_data["current_user_message"]
        assistant_id: Optional[str] = prep_data.get("assistant_id") # Get assistant_id

        # --- TRACE LOGGING: Start of execution ---
        self.trace_events = [] # Clear/initialize trace for this run
        append_trace_event(self.trace_events, {
            "role": "user",
            "event_type": "user_input",
            "conversation_id": conversation_id,
            "user_id": username,
            "assistant_id": self.id,
            "content": current_user_message,
            "timestamp": datetime.utcnow().isoformat()
        })
        try:
            # Dynamically get relevant tools for this specific invocation
            tools_for_this_invocation = await _get_relevant_langchain_tools(self, current_user_message)

            if not tools_for_this_invocation:
                logger.warning(f"{self.name}: No Langchain tools determined relevant for this invocation. Agent will operate without tools.")
            else:
                logger.info(f"{self.name}: Found {len(tools_for_this_invocation)} relevant tools.")

            # Fetch combined history from Qdrant and Neo4j
            combined_history_messages: List[Dict[str, str]] = []
            if username and conversation_id:
                combined_history_messages = await _combine_conversation_history(
                    self,
                    username=username,
                    conversation_id=conversation_id,
                    limit=self._history_limit,
                    assistant_manager=self.assistant_manager,
                    assistant_id=self.id,
                    current_user_message=current_user_message,
                    funtions_manager=self._functions_manager,

                )
                logger.info(f"{self.name}: Using {len(combined_history_messages)} combined history messages for agent.")

            # from langchain_core.messages import HumanMessage, AIMessage, SystemMessage # Already imported
            langchain_chat_history = []
            for msg_dict in combined_history_messages:
                if msg_dict.get("role") == "user" and msg_dict.get("content") == current_user_message:
                    continue 
                role = msg_dict.get("role")
                content = msg_dict.get("content")
                if role == "user":
                    langchain_chat_history.append(HumanMessage(content=content))
                elif role == "assistant":
                    langchain_chat_history.append(AIMessage(content=content))
                elif role == "system" and content: 
                    langchain_chat_history.append(SystemMessage(content=content)) # Assuming SystemMessage is BaseMessage

       
            langchain_chat_history = await summarize_long_chat_history(
                self=self,
                langchain_chat_history=langchain_chat_history,
                username=username,
                conversation_id=conversation_id,
                assistant_id=assistant_id,
                system_message_class=SystemMessage,
                assistant_id_for_storage=self.id
            )
            # Pass data to the LangGraph State
            state: AgentState = { # Use the defined AgentState
                "input_text": current_user_message,
                "chat_history": langchain_chat_history,
                "username": username,
                "conversation_id": conversation_id,
                "assistant_id": assistant_id,
                "llm_handler": self.llm_handler,
                "qdrant_client": self.my_qdrant_client, # Optional
                "neo4j_client": self.neo4j_client, # Optional
                "assistant_manager": self.assistant_manager,
                "functiion_manager": self._functions_manager,
                "model_name": self._model,
                "api_key": self._api_key ,
                "tools_for_this_invocation": tools_for_this_invocation,
                "intermediate_steps": [], # Initialize empty for agent
                "agent_outcome": None, # Will be set by agent_llm_node
                "output": None, # Will be set by agent_llm_node or postprocess
                "plan": None, # Initialize plan as None
                "service_instance": self # Pass reference to self for methods
            }

            # Enhance state with code-specific analysis and context
            try:
                state = enhance_agent_state_for_code(state, current_user_message)
                logger.info(f"Enhanced agent state with code analysis. Code task: {state.get('is_code_task', False)}")
            except Exception as e:
                logger.error(f"Error enhancing agent state for code: {e}", exc_info=True)

            # --- LANGGRAPH INTEGRATION START ---
            graph = StateGraph(AgentState) # Use the defined AgentState
            graph.add_node("preprocess", preprocess_node) # Assuming preprocess_node takes state and extracts what it needs
            graph.add_node("summarize", summarize_node) # Assuming summarize_node takes state and extracts what it needs
            graph.add_node("planner", self._planner_node) # Add the new planner node
            graph.add_node("agent_llm", self._agent_llm_node) # Use the new agent LLM node method
            graph.add_node("tool_executor", self._tool_executor_node) # Use the new tool executor node method
            graph.add_node("postprocess", postprocess_node) # Assuming postprocess_node takes state and extracts what it needs

            graph.set_entry_point("preprocess")
            graph.add_edge("preprocess", "summarize")
            graph.add_edge("summarize", "planner") # After summarization, go to the planner
            graph.add_edge("planner", "agent_llm") # After planning, go to the agent LLM

            # Conditional edge from agent_llm with step limit protection
            def should_continue_or_finish(state):
                # Check if we've exceeded maximum steps to prevent infinite loops
                step_count = len(state.get("intermediate_steps", []))
                max_steps = 10  # Limit to 10 tool calls

                if step_count >= max_steps:
                    logger.warning(f"Maximum steps ({max_steps}) reached, forcing finish")
                    return "postprocess"

                # Normal logic: continue if agent wants to use a tool, finish otherwise
                if isinstance(state.get("agent_outcome"), AgentAction):
                    return "tool_executor"
                else:
                    return "postprocess"

            graph.add_conditional_edges(
                "agent_llm",
                should_continue_or_finish,
                {
                    "tool_executor": "tool_executor",
                    "postprocess": "postprocess",
                }
            )

            # After tool_executor, loop back to agent_llm to continue reasoning
            graph.add_edge("tool_executor", "agent_llm")

            # Final step: after postprocess, the graph ends
            graph.add_edge("postprocess", END)

            workflow = graph.compile()

            # Configure with higher recursion limit and better error handling
            config = {
                "recursion_limit": 50,  # Increase from default 25
                "max_execution_time": 300  # 5 minutes timeout
            }

            final_state = await workflow.ainvoke(state, config=config)
            final_response_text = final_state["output"]
            # --- LANGGRAPH INTEGRATION END ---

            logger.info(f"{self.name}: LangGraph agent execution completed. Response: {final_response_text}")


            # Store conversation in Neo4j and Qdrant if we have the necessary information
            if username and conversation_id and current_user_message:
                await store_conversation(
                    self,
                    username=username,
                    conversation_id=conversation_id,
                    current_user_message=current_user_message,
                    final_response_text=final_response_text
                )

            formatted_response = self._format_service_response(True, "LangGraph agent processed successfully", data={"response": final_response_text, "model": self._model, "service_name": self.name})
            logger.info(f"{self.name}: Returning formatted service response: {json.dumps(formatted_response, default=str)}")
            return formatted_response
        except Exception as e:
            logger.error(f"Error executing LangGraph agent in {self.name}: {e}", exc_info=True)
            error_response = self._format_service_response(False, "An unexpected error occurred with LangGraph agent.", error=str(e))
            logger.info(f"{self.name}: Returning error service response: {json.dumps(error_response, default=str)}")
            return error_response
        finally:
            # --- TRACE LOGGING: Save trace at the end ---
            if self.trace_events and conversation_id:
                save_trace_events_to_file(self.trace_events, conversation_id)
                logger.info(f"Saved execution trace for conversation {conversation_id}")
  
    async def get_config_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for this service's configuration."""
        return {
            "type": "object",
            "properties": {
                "model": {
                    "type": "string",
                    "title": "AI Model",
                    "description": "The AI model to use for processing",
                    "default": self._model, # Use the current model as default
                    # Fetch models asynchronously and use their IDs for the enum
                    "enum": await self._get_available_model_ids()
                }
            }
        }

def make_json_serializable(obj):
    if isinstance(obj, (datetime,)):
        return obj.isoformat()
    return str(obj)