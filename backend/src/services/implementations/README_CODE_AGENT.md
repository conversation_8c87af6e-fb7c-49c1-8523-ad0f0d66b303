# LangChain Code Agent - Verbesserungen

## Übersicht
Der LangChain Service wurde zu einem fortgeschrittenen Code Agent erweitert mit spezialisierten Funktionen für Softwareentwicklung, Debugging und Code-Analyse.

## Neue Funktionen

### 1. Code-spezifische Tools (`code_agent_tools.py`)

#### Datei-Analyse Tool
- **Funktion**: `analyze_file`
- **Beschreibung**: Analysiert <PERSON> auf Struktur, Abhängigkeiten, Komplexität oder Sicherheitsprobleme
- **Parameter**: 
  - `file_path`: Pfad zur zu analysierenden Datei
  - `analysis_type`: Art der Analyse ('structure', 'dependencies', 'complexity', 'security')

#### Code-Suche Tool
- **Funktion**: `search_code_patterns`
- **Beschreibung**: Sucht nach Code-Mustern im gesamten Codebase
- **Parameter**:
  - `query`: Suchanfrage für Code-Muster oder Funktionen
  - `file_pattern`: <PERSON><PERSON><PERSON> für die Suche (z.B. '*.py', '*.js')
  - `include_tests`: Ob Testdateien einbezogen werden sollen

#### Git-Operationen Tool
- **Funktion**: `git_operation`
- **Beschreibung**: Führt Git-Operationen für Code-Analyse und Historie aus
- **Parameter**:
  - `operation`: Git-Operation ('status', 'log', 'diff', 'branch', 'blame')
  - `file_path`: Dateipfad für dateispezifische Operationen
  - `additional_args`: Zusätzliche Git-Argumente

#### Projekt-Struktur Tool
- **Funktion**: `analyze_project_structure`
- **Beschreibung**: Analysiert die gesamte Projektstruktur und Organisation
- **Parameter**:
  - `max_depth`: Maximale Tiefe der Analyse

#### Code-Template Generator
- **Funktion**: `generate_code_template`
- **Beschreibung**: Generiert Code-Templates für häufige Muster
- **Parameter**:
  - `language`: Programmiersprache ('python', 'javascript')
  - `template_type`: Template-Typ ('class', 'function', 'test')
  - `description`: Beschreibung für das Template

### 2. Erweiterte Qdrant-Tools

#### Semantische Code-Suche
- **Funktion**: `semantic_code_search`
- **Beschreibung**: Erweiterte semantische Code-Suche mit Vektor-Embeddings
- **Parameter**:
  - `query`: Suchanfrage
  - `top_k`: Anzahl der Ergebnisse
  - `search_type`: Suchtyp ('semantic', 'similar_functions', 'usage_patterns')

### 3. Code Agent Preprocessing (`code_agent_preprocessing.py`)

#### Intent-Erkennung
- **Funktion**: `detect_code_intent`
- **Beschreibung**: Analysiert Benutzernachrichten zur Erkennung von Coding-Absichten
- **Erkannte Intents**:
  - `debug`: Debugging und Fehlerbehebung
  - `analyze`: Code-Analyse und Review
  - `implement`: Neue Funktionen implementieren
  - `refactor`: Code-Refactoring
  - `test`: Test-Erstellung
  - `documentation`: Dokumentation
  - `search`: Code-Suche

#### Entity-Extraktion
- **Funktion**: `extract_code_entities`
- **Beschreibung**: Extrahiert code-bezogene Entitäten aus Text
- **Extrahierte Entitäten**:
  - Dateipfade
  - Funktionsnamen
  - Klassennamen
  - Variablennamen
  - Fehlermeldungen
  - Programmiersprachen

#### Kontext-Vorbereitung
- **Funktion**: `prepare_code_context`
- **Beschreibung**: Bereitet erweiterten Kontext für code-bezogene Aufgaben vor
- **Kontext-Elemente**:
  - Primäre Absicht
  - Vorgeschlagene Tools
  - Kontext-Priorität
  - Such-Keywords
  - Datei-Fokus

### 4. Erweiterte Planner-Funktionen

Der Planner wurde speziell für Code-Aufgaben erweitert:
- Berücksichtigt Code-spezifische Planungsstrategien
- Schlägt geeignete Tools basierend auf der Aufgabe vor
- Erstellt detaillierte Schritt-für-Schritt-Pläne für:
  - Code-Analyse
  - Debugging
  - Feature-Implementierung
  - Refactoring
  - Datei-Operationen

### 5. Erweiterte System-Prompts

Der Agent erhält erweiterte System-Prompts mit:
- Code-spezifischen Fähigkeiten
- Best Practices für Softwareentwicklung
- Debugging-Strategien
- Code-Qualitäts-Richtlinien

## Konfiguration

### Service-Konfiguration
```python
DEFAULT_SERVICE_ID = 'langchain_code_agent'
DEFAULT_NAME = 'LangChain Code Agent'
DEFAULT_DESCRIPTION = 'Advanced code agent with file operations, code analysis, and development assistance'
DEFAULT_CATEGORY = 'Code'
DEFAULT_ICON = '🤖'
DEFAULT_INITIAL_CONFIG = {
    'history_limit': 15,  # Erhöht für Code-Kontext
    'enable_code_execution': True,
    'enable_file_operations': True,
    'enable_git_operations': True
}
```

## Verwendung

### Beispiel-Anfragen

1. **Code-Analyse**:
   ```
   "Analysiere die Datei backend/src/services/implementations/langchain_text_service.py auf Struktur und Komplexität"
   ```

2. **Debugging**:
   ```
   "Ich habe einen Fehler in meiner Python-Funktion. Kannst du mir helfen, das Problem zu finden?"
   ```

3. **Code-Suche**:
   ```
   "Finde alle Funktionen, die mit 'async def' beginnen und 'database' im Namen haben"
   ```

4. **Template-Generierung**:
   ```
   "Erstelle mir ein Python-Klassen-Template für einen API-Client"
   ```

5. **Projekt-Analyse**:
   ```
   "Zeige mir die Struktur des aktuellen Projekts und identifiziere die Hauptkomponenten"
   ```

## Integration

Der Code Agent ist vollständig in den bestehenden LangChain Service integriert:
- Automatische Intent-Erkennung
- Dynamische Tool-Auswahl basierend auf der Aufgabe
- Erweiterte Kontext-Vorbereitung
- Code-spezifische Planungsstrategien

## Vorteile

1. **Intelligente Code-Analyse**: Automatische Erkennung von Code-Mustern und -Strukturen
2. **Kontextbewusste Hilfe**: Versteht den Kontext von Code-Problemen
3. **Umfassende Tool-Integration**: Nutzt Git, Dateisystem und Qdrant für vollständige Code-Unterstützung
4. **Proaktive Planung**: Erstellt detaillierte Pläne für komplexe Code-Aufgaben
5. **Multi-Language-Support**: Unterstützt verschiedene Programmiersprachen

## Nächste Schritte

1. Testen der neuen Funktionen mit verschiedenen Code-Aufgaben
2. Erweitern der Template-Bibliothek für weitere Sprachen
3. Integration von Code-Qualitäts-Metriken
4. Hinzufügen von automatischen Code-Reviews
5. Implementierung von Code-Generierung basierend auf Spezifikationen
