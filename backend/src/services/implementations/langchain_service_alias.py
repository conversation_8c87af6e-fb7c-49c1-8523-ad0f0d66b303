"""
Backward compatibility alias for the LangChain service.
This ensures that old workflows using 'langchain_service' continue to work.
"""

from .langchain_text_service import LangChainService

# Create an alias class for backward compatibility
class LangChainServiceAlias(LangChainService):
    """
    Backward compatibility alias for LangChainService.
    This allows old workflows to continue using 'langchain_service' as the service ID.
    """
    
    DEFAULT_SERVICE_ID = 'langchain_service'
    DEFAULT_NAME = 'LangChain Service (Legacy)'
    DEFAULT_DESCRIPTION = 'Legacy alias for LangChain Code Agent - redirects to langchain_code_agent'
    DEFAULT_CATEGORY = 'Code'
    DEFAULT_ICON = '🔄'
    
    def __init__(self, *args, **kwargs):
        # Force the service_id to be the legacy one
        kwargs['service_id'] = self.DEFAULT_SERVICE_ID
        super().__init__(*args, **kwargs)
        
        # Log that this is a legacy service
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Using legacy LangChain service alias '{self.DEFAULT_SERVICE_ID}'. "
                      f"Please update workflows to use 'langchain_code_agent' instead.")
