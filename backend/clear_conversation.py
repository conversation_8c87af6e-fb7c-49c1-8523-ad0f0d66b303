#!/usr/bin/env python3
"""
Quick script to clear conversation history to resolve token limit issues.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from clients.qdrant import MyQdrantClient
from qdrant_client.http.models import Filter, FieldCondition, MatchValue


async def clear_conversation_history():
    """Clear the conversation history that's causing token overflow."""
    print("🧹 Clearing conversation history to resolve token limits...")
    
    try:
        # Initialize Qdrant client
        qdrant_client = MyQdrantClient(vector_size=384)
        
        # The conversation ID from the logs
        conversation_id = "8a27f4e7-f39b-4be5-8f4f-6bcc65e36269"
        
        # Create filter for this conversation
        filter_condition = Filter(
            must=[
                FieldCondition(
                    key="conversation_id",
                    match=MatchValue(value=conversation_id)
                )
            ]
        )
        
        # Delete all messages in this conversation
        result = qdrant_client.client.delete(
            collection_name="chat_collection",
            points_selector=filter_condition
        )
        
        print(f"✅ Cleared conversation {conversation_id}")
        print(f"Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error clearing conversation: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Clear the conversation history."""
    print("🚀 Clearing Conversation History for Token Optimization\n")
    
    success = await clear_conversation_history()
    
    if success:
        print("\n🎉 Conversation history cleared!")
        print("You can now start a fresh conversation without token limit issues.")
        print("Try sending your logger.info search query again.")
    else:
        print("\n❌ Failed to clear conversation history.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
