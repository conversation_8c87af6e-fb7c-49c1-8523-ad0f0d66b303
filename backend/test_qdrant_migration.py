#!/usr/bin/env python3
"""
Test script to verify Qdrant migration is working correctly.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qdrant.operations.workflows import get_workflow_manager
from qdrant.operations.assistants import get_assistant_manager


async def test_workflow_operations():
    """Test workflow operations in Qdrant."""
    print("🔧 Testing Workflow Operations...")
    
    try:
        workflow_manager = get_workflow_manager()
        
        # Test workflow creation
        test_workflow = {
            "name": "Test Workflow",
            "description": "A test workflow for Qdrant migration",
            "blocks": [
                {
                    "id": "block1",
                    "type": "service",
                    "name": "Test Block",
                    "serviceId": "langchain_code_agent",
                    "x": 100,
                    "y": 100
                }
            ],
            "connections": []
        }
        
        # Save workflow
        result = await workflow_manager.save_workflow(test_workflow)
        if result["success"]:
            workflow_id = result["workflow_id"]
            print(f"✅ Workflow saved successfully: {workflow_id}")
            
            # Retrieve workflow
            retrieved = workflow_manager.get_workflow(workflow_id)
            if retrieved:
                print(f"✅ Workflow retrieved successfully: {retrieved['name']}")
                
                # Search workflows
                search_results = await workflow_manager.search_workflows("test")
                print(f"✅ Search found {len(search_results)} workflows")
                
                # List workflows
                all_workflows = workflow_manager.list_workflows()
                print(f"✅ Listed {len(all_workflows)} total workflows")
                
                # Clean up - delete test workflow
                deleted = workflow_manager.delete_workflow(workflow_id)
                if deleted:
                    print("✅ Test workflow cleaned up successfully")
                
                return True
            else:
                print("❌ Failed to retrieve workflow")
                return False
        else:
            print("❌ Failed to save workflow")
            return False
            
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_assistant_operations():
    """Test assistant operations in Qdrant."""
    print("🤖 Testing Assistant Operations...")
    
    try:
        assistant_manager = get_assistant_manager()
        
        # Test assistant creation
        test_assistant = {
            "id": "test_assistant",
            "username": "TestBot",
            "display_name": "Test Assistant",
            "type": "assistant",
            "language": "English",
            "model": "gpt-3.5-turbo"
        }
        
        # Store assistant
        success = await assistant_manager.store_assistant(test_assistant)
        if success:
            print(f"✅ Assistant stored successfully: {test_assistant['id']}")
            
            # Retrieve assistant
            retrieved = assistant_manager.get_assistant(test_assistant['id'])
            if retrieved:
                print(f"✅ Assistant retrieved successfully: {retrieved['username']}")
                
                # Test system message
                test_message = "You are a helpful test assistant."
                msg_success = await assistant_manager.set_system_message(
                    test_assistant['id'], 
                    test_message
                )
                if msg_success:
                    print("✅ System message stored successfully")
                    
                    # Retrieve system message
                    retrieved_msg = assistant_manager.get_system_message(test_assistant['id'])
                    if retrieved_msg == test_message:
                        print("✅ System message retrieved successfully")
                    else:
                        print("❌ System message mismatch")
                        return False
                
                # Test avatar storage
                test_avatar = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                avatar_success = await assistant_manager.store_avatar(
                    test_assistant['id'],
                    test_avatar
                )
                if avatar_success:
                    print("✅ Avatar stored successfully")
                    
                    # Retrieve avatar
                    retrieved_avatar = assistant_manager.get_avatar(test_assistant['id'])
                    if retrieved_avatar == test_avatar:
                        print("✅ Avatar retrieved successfully")
                    else:
                        print("❌ Avatar mismatch")
                        return False
                
                # List assistants
                all_assistants = assistant_manager.get_assistants()
                print(f"✅ Listed {len(all_assistants)} total assistants")
                
                return True
            else:
                print("❌ Failed to retrieve assistant")
                return False
        else:
            print("❌ Failed to store assistant")
            return False
            
    except Exception as e:
        print(f"❌ Assistant test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_qdrant_collections():
    """Test that Qdrant collections are created properly."""
    print("📊 Testing Qdrant Collections...")
    
    try:
        # Initialize managers to ensure collections are created
        workflow_manager = get_workflow_manager()
        assistant_manager = get_assistant_manager()
        
        # Check if collections exist
        collections = workflow_manager.qdrant_client.client.get_collections()
        collection_names = [col.name for col in collections.collections]
        
        expected_collections = [
            "workflows",
            "assistants", 
            "assistant_system_messages",
            "assistant_avatars"
        ]
        
        for collection in expected_collections:
            if collection in collection_names:
                print(f"✅ Collection '{collection}' exists")
            else:
                print(f"❌ Collection '{collection}' missing")
                return False
        
        print(f"✅ All {len(expected_collections)} collections exist")
        return True
        
    except Exception as e:
        print(f"❌ Collection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Qdrant Migration Tests\n")
    
    tests = [
        ("Qdrant Collections", test_qdrant_collections),
        ("Workflow Operations", test_workflow_operations),
        ("Assistant Operations", test_assistant_operations)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Qdrant migration is working correctly.")
        print("Your Elasticsearch operations have been successfully migrated to Qdrant.")
    else:
        print(f"\n⚠️  {len(results) - passed} tests failed. Please check the errors above.")
    
    return passed == len(results)


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
